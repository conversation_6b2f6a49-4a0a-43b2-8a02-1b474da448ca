# SendLevel

Controls which artificial keyboard and mouse events are ignored by
hotkeys and hotstrings.

``` Syntax
SendLevel Level
```

## Parameters {#Parameters}

Level

:   Type: [Integer](../Concepts.htm#numbers)

    An integer between 0 and 100.

## Return Value {#Return_Value}

Type: [Integer](../Concepts.htm#numbers)

This function returns the previous setting; an integer between 0 and
100.

## General Remarks {#General_Remarks}

If SendLevel is not used, the default level is 0.

By default, [hook](_UseHook.htm) [hotkeys](../Hotkeys.htm) and
[hotstrings](../Hotstrings.htm) ignore keyboard and mouse events
generated by any AutoHotkey script. In some cases it can be useful to
override this behaviour; for instance, to allow a remapped key to be
used to trigger other hotkeys. SendLevel and
[#InputLevel](_InputLevel.htm) provide the means to achieve this.

SendLevel sets the level for events generated by the current [script
thread](../misc/Threads.htm), while #InputLevel sets the level for any
hotkeys or hotstrings beneath it. For any event generated by a script to
trigger a hook hotkey or hotstring, the send level of the event must be
higher than the input level of the hotkey or hotstring.

Compatibility:

- [SendPlay](Send.htm#SendPlayDetail) is not affected by SendLevel.
- [SendInput](Send.htm#SendInputDetail) is affected by SendLevel, but
  the script\'s own hook hotkeys cannot be activated while a SendInput
  is in progress, since it temporarily deactivates the hook. However,
  when Send or SendInput [reverts to
  SendEvent](Send.htm#SendInputUnavail), it is able to activate the
  script\'s own hotkeys.
- [Hotkeys](../Hotkeys.htm) using the [\"reg\"](ListHotkeys.htm) method
  are incapable of distinguishing physical and artificial input, so are
  not affected by SendLevel. However, hotkeys above level 0 always use
  the keyboard or mouse hook.
- [Hotstrings](../Hotstrings.htm) use #InputLevel only to determine
  whether the last typed character should trigger a hotstring. For
  instance, the hotstring `::btw::` can be triggered regardless of
  #InputLevel by sending `btw`{.no-highlight} at level 1 or higher and
  physically typing an [ending character](../Hotstrings.htm#EndChars).
  This is because hotstring recognition works by collecting input from
  all levels except level 0 into a single global buffer.
- Auto-replace [hotstrings](../Hotstrings.htm) always generate
  keystrokes at level 0, since it is usually undesirable for the
  replacement text to trigger another hotstring or hotkey. To work
  around this, use a non-auto-replace hotstring and the SendEvent
  function.
- Characters sent by the [ASC (Alt+nnnnn)](Send.htm#asc) method cannot
  trigger a hotstring, even if SendLevel is used.
- Characters sent by SendEvent with the [{Text}](Send.htm#SendText)
  mode, [{U+nnnn}](Send.htm#Unicode) or [Unicode fallback
  method](Send.htm#characters) can trigger hotstrings.

The built-in variable **A_SendLevel** contains the current setting.

Every newly launched hotkey or hotstring [thread](../misc/Threads.htm)
starts off with a send level equal to the [input level](_InputLevel.htm)
of the hotkey or hotstring. Every other newly launched thread (such as a
[custom menu item](Menu.htm) or [timed](SetTimer.htm) subroutine) starts
off fresh with the default setting, which is typically 0 but may be
changed by using this function during [script
startup](../Scripts.htm#auto).

If SendLevel is used during [script startup](../Scripts.htm#auto), it
also affects [keyboard and mouse remapping](../misc/Remap.htm).

AutoHotkey versions older than v1.1.06 behave as though `#InputLevel 0`
and `SendLevel 0` are in effect.

## Related {#Related}

[#InputLevel](_InputLevel.htm), [Send](Send.htm), [Click](Click.htm),
[MouseClick](MouseClick.htm), [MouseClickDrag](MouseClickDrag.htm)

## Examples {#Examples}

::: {#ExBasic .ex}
[](#ExBasic){.ex_number} SendLevel allows to trigger hotkeys and
hotstrings of another script, which normally would not be the case.

    SendLevel 1
    SendEvent "btw{Space}" ; Produces "by the way ".

    ; This may be defined in a separate script:
    ::btw::by the way
:::
