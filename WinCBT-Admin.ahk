#Requires Autohotkey v2

#SingleInstance Force
; Include error handler first so it's available to all other modules
#Include %A_ScriptDir%\lib\admin_error_handler.ahk

; Include core functionality modules
#Include %A_ScriptDir%\lib\admin_db_functions.ahk
#Include %A_ScriptDir%\lib\admin_import_export.ahk
#Include %A_ScriptDir%\lib\admin_operator_management.ahk
#Include %A_ScriptDir%\lib\admin_reports.ahk
#Include %A_ScriptDir%\lib\admin_settings.ahk
#Include %A_ScriptDir%\lib\admin_candidate_management.ahk
#Include %A_ScriptDir%\lib\csv_minimal.ahk
#Include %A_ScriptDir%\lib\secugen_wrapper.ahk
#Include %A_ScriptDir%\lib\webcam_utils.ahk

; Application version and build information
global version := "(v1.0.0 Build 20250520)"
global APP_NAME := "WinCBT-Admin"

; Global manager instances
global g_dbManager := ""          ; Database manager
global g_operatorManager := ""    ; Operator manager
global myGui := ""                ; Main GUI
global sbMain := ""               ; Status bar

; Initialize the error handler first
; Set debug mode based on command line parameter or config setting
debugMode := false
for n, param in A_Args {
    if (param = "/debug" || param = "-debug") {
        debugMode := true
        break
    }
}

; Try to read debug mode from config if not set by command line
if (!debugMode) {
    try {
        debugMode := IniRead(A_ScriptDir "\config.ini", "Settings", "DebugMode", "0") = "1"
    } catch {
        ; Ignore errors reading config
    }
}

ErrorHandler.Initialize(StatusCallback, debugMode)
ErrorHandler.LogMessage("INFO", "Starting " APP_NAME " " version)
if (debugMode)
    ErrorHandler.LogMessage("INFO", "Debug mode enabled")

; Validate required files and directories
ErrorHandler.ValidateRequiredFiles()

; Initialize the GUI
myGui := Constructor()

; Add OnExit handler for proper cleanup
CloseFunc(ExitReason, ExitCode) {
    ; Log application exit
    ErrorHandler.LogMessage("INFO", "Application exiting: " ExitReason " (Code: " ExitCode ")")
}
OnExit(CloseFunc)
myGui.OnEvent("Close", (*) => ExitApp())

; Status callback function for error handler
StatusCallback(message) {
    global sbMain
    if (IsObject(sbMain))
        sbMain.SetText(message)
    OutputDebug(message)
}

; ; Constructor()
; ; Builds and initializes the main GUI for the application.
; ; Sets up global variables, initializes managers (DB), reads configuration,
; ; creates GUI controls, defines event handlers, and sets initial states.
; ; @return: The main Gui object.
Constructor() {
    ; Log constructor start
    ErrorHandler.LogMessage("INFO", "Initializing application components")

    ; Initialize database manager with error handling
    try {
        g_dbManager := AdminDBManager()
        ErrorHandler.LogMessage("INFO", "Database manager initialized successfully")
    } catch as err {
        ErrorHandler.LogMessage("CRITICAL", "Failed to initialize database manager: " err.Message)
        ErrorHandler.ShowError("Failed to initialize database manager: " err.Message,
                              APP_NAME " - Critical Error", "Icon! 262144")
    }

    ; Initialize operator manager with error handling
    try {
        g_operatorManager := CreateOperatorManager()
        ErrorHandler.LogMessage("INFO", "Operator manager initialized successfully")

        ; Create biometric data directories
        CreateBiometricDirectories()

        ; Ensure default admin account exists
        operators := g_operatorManager.GetAllOperators()
        if (operators.Length = 0) {
            ; Create default admin account manually
            defaultAdmin := {}
            defaultAdmin.Username := "admin"
            defaultAdmin.FullName := "Administrator"
            defaultAdmin.Email := "<EMAIL>"
            defaultAdmin.Phone := ""
            defaultAdmin.Department := ""
            defaultAdmin.Role := "Administrator"
            defaultAdmin.Status := "Active"
            defaultAdmin.Password := "admin"
            defaultAdmin.Notes := "Default administrator account"

            if (g_operatorManager.AddOperator(defaultAdmin)) {
                ErrorHandler.LogMessage("INFO", "Created default admin account")
            }
        }
    } catch as err {
        ErrorHandler.LogMessage("CRITICAL", "Failed to initialize operator manager: " err.Message)
        ErrorHandler.ShowError("Failed to initialize operator manager: " err.Message,
                              APP_NAME " - Critical Error", "Icon! 262144")
    }

    ; Create Main GUI first before adding controls
    myGui := Gui()
    myGui.Title := APP_NAME . " " . version
    myGui.SetFont("s10", "Segoe UI")

    ; Create status bar early to avoid reference errors in callbacks
    sbMain := myGui.Add("StatusBar", "", "Ready | Database: Initializing... | Operator: Admin")

    ; Menu handler function
    MenuHandler(ItemName, ItemPos, Menu) {
        ; Handle specific menu items
        if (ItemName == "Import Candidates...") {
            sbMain.SetText("Opening Import Candidates dialog...")
            ShowImportCandidatesDialog()
            return
        } else if (ItemName == "Export Data...") {
            sbMain.SetText("Opening Export Data dialog...")
            ShowExportDataDialog()
            return
        } else if (ItemName == "Settings...") {
            sbMain.SetText("Opening Settings dialog...")
            ShowSettingsDialog()
            return
        } else if (ItemName == "Logout") {
            sbMain.SetText("Logging out...")
            ; TODO: Implement logout functionality
            return
        } else if (ItemName == "Candidates Database...") {
            sbMain.SetText("Opening Candidates Database...")
            ShowCandidateManagementDialog()
            return
        } else if (ItemName == "Operator Accounts...") {
            sbMain.SetText("Opening Operator Accounts...")
            ShowOperatorManagementDialog()
            return
        } else if (ItemName == "Hardware Database...") {
            sbMain.SetText("Opening Hardware Database...")
            ShowHardwareManagementDialog()
            return
        } else if (ItemName == "Room Configuration...") {
            sbMain.SetText("Opening Room Configuration...")
            ShowRoomConfigurationDialog()
            return
        } else if (ItemName == "Generate Reports...") {
            sbMain.SetText("Opening Reports Generator...")
            ShowReportsDialog()
            return
        } else if (ItemName == "View Help") {
            sbMain.SetText("Opening Help...")
            ; TODO: Implement help functionality
            return
        } else if (ItemName == "About...") {
            sbMain.SetText("Opening About dialog...")
            ShowAboutDialog()
            return
        }
    }

    ; Create Menu Structure
    FileMenu := Menu()
    FileMenu.Add("Import Candidates...", MenuHandler)
    FileMenu.Add("Export Data...", MenuHandler)
    FileMenu.Add("Settings...", MenuHandler)
    FileMenu.Add("Logout", MenuHandler)
    FileMenu.Add("Exit", (*) => ExitApp())
    FileMenu.SetIcon("Settings...","shell32.dll", 166)

    DatabaseMenu := Menu()
    DatabaseMenu.Add("Candidates Database...", MenuHandler)
    DatabaseMenu.Add("Operator Accounts...", MenuHandler)
    DatabaseMenu.Add("Hardware Database...", MenuHandler)
    DatabaseMenu.Add("Room Configuration...", MenuHandler)

    ReportsMenu := Menu()
    ReportsMenu.Add("Generate Reports...", MenuHandler)
    ReportsMenu.Add("View Verification Statistics", MenuHandler)
    ReportsMenu.Add("Export Reports...", MenuHandler)

    HelpMenu := Menu()
    HelpMenu.Add("View Help", MenuHandler)
    HelpMenu.Add("About...", MenuHandler)

    MenuBar_Storage := MenuBar()
    MenuBar_Storage.Add("&File", FileMenu)
    MenuBar_Storage.Add("&Database", DatabaseMenu)
    MenuBar_Storage.Add("&Reports", ReportsMenu)
    MenuBar_Storage.Add("&Help", HelpMenu)
    myGui.MenuBar := MenuBar_Storage

    ; Main title
    myGui.SetFont("s16 bold")
    myGui.Add("Text", "x0 y10 w1280 h30 Center", "WinCBT Database Administration System")
    myGui.SetFont("s10")

    ; Read company and exam information from db\config.ini
    dbConfigFile := "db\config.ini"
    companyName := IniRead(dbConfigFile, "Company", "Name", "University of Information and Technology")
    examName := IniRead(dbConfigFile, "Exam", "Name", "Computer Networks")
    centreID := IniRead(dbConfigFile, "Centre", "ID", "CNTR65434")

    ; Create datetime display with variable that will be updated by timer
    global DateTimeText := myGui.Add("Text", "x1000 y50 w250 h30 Center", FormatTime(, "dd-MMM-yyyy hh:mm:ss tt"))
    myGui.Add("Text", "x1000 y80 w250 h30 Center", "Centre ID: " centreID)

    ; Set a timer to update the date and time display
    SetTimer(UpdateDateTime, 1000)

    ; Create tabs for different sections
    Tabs := myGui.Add("Tab3", "x10 y50 w1260 h580", ["Candidates", "Operators", "Hardware", "Rooms", "Reports", "Settings"])

    ; ==================== CANDIDATES TAB ====================
    Tabs.UseTab(1)
    myGui.Add("Text", "x20 y90 w200 h20", "Candidate Management")
    myGui.Add("Button", "x20 y120 w200 h40 +0x1000", "View All Candidates").OnEvent("Click", (*) => ShowCandidateManagementDialog())
    myGui.Add("Button", "x20 y170 w200 h40 +0x1000", "Import Candidates").OnEvent("Click", (*) => ShowImportCandidatesDialog())
    myGui.Add("Button", "x20 y220 w200 h40 +0x1000", "Export Candidates").OnEvent("Click", (*) => ShowExportDataDialog())
    myGui.Add("Button", "x20 y270 w200 h40 +0x1000", "Search Candidates").OnEvent("Click", (*) => ShowCandidateSearchDialog())

    ; Quick stats panel
    myGui.Add("GroupBox", "x240 y90 w400 h220", "Candidate Statistics")
    myGui.Add("Text", "x260 y120 w180 h20", "Total Candidates:")
    myGui.Add("Text", "x260 y150 w180 h20", "Verified Candidates:")
    myGui.Add("Text", "x260 y180 w180 h20", "Special Candidates:")
    myGui.Add("Text", "x260 y210 w180 h20", "Assigned Seats:")
    myGui.Add("Text", "x260 y240 w180 h20", "Verification Rate:")

    global TotalCandidatesText := myGui.Add("Text", "x450 y120 w180 h20 Right", "0")
    global VerifiedCandidatesText := myGui.Add("Text", "x450 y150 w180 h20 Right", "0")
    global SpecialCandidatesText := myGui.Add("Text", "x450 y180 w180 h20 Right", "0")
    global AssignedSeatsText := myGui.Add("Text", "x450 y210 w180 h20 Right", "0")
    global VerificationRateText := myGui.Add("Text", "x450 y240 w180 h20 Right", "0%")

    ; Recent activity panel
    myGui.Add("GroupBox", "x650 y90 w600 h220", "Recent Activity")
    global RecentActivityList := myGui.Add("ListView", "x670 y120 w560 h170", ["Timestamp", "Action", "Operator", "Details"])
    RecentActivityList.ModifyCol(1, 150)
    RecentActivityList.ModifyCol(2, 100)
    RecentActivityList.ModifyCol(3, 100)
    RecentActivityList.ModifyCol(4, 200)

    ; ==================== OPERATORS TAB ====================
    Tabs.UseTab(2)
    myGui.Add("Text", "x20 y90 w200 h20", "Operator Management")
    myGui.Add("Button", "x20 y120 w200 h40 +0x1000", "View All Operators").OnEvent("Click", (*) => ShowOperatorManagementDialog())
    myGui.Add("Button", "x20 y170 w200 h40 +0x1000", "Add New Operator").OnEvent("Click", (*) => ShowAddOperatorDialog())
    myGui.Add("Button", "x20 y220 w200 h40 +0x1000", "Manage Permissions").OnEvent("Click", (*) => ShowPermissionsDialog())
    myGui.Add("Button", "x20 y270 w200 h40 +0x1000", "Search Operators").OnEvent("Click", (*) => ShowOperatorSearchDialog())
    myGui.Add("Button", "x20 y320 w200 h40 +0x1000", "Operator Attendance").OnEvent("Click", (*) => ShowOperatorAttendanceDialog())

    ; Operator statistics panel
    myGui.Add("GroupBox", "x240 y90 w400 h220", "Operator Statistics")
    myGui.Add("Text", "x260 y120 w180 h20", "Total Operators:")
    myGui.Add("Text", "x260 y150 w180 h20", "Active Operators:")
    myGui.Add("Text", "x260 y180 w180 h20", "Administrators:")
    myGui.Add("Text", "x260 y210 w180 h20", "Last Login Today:")
    myGui.Add("Text", "x260 y240 w180 h20", "Inactive Operators:")

    global TotalOperatorsText := myGui.Add("Text", "x450 y120 w180 h20 Right", "0")
    global ActiveOperatorsText := myGui.Add("Text", "x450 y150 w180 h20 Right", "0")
    global AdminOperatorsText := myGui.Add("Text", "x450 y180 w180 h20 Right", "0")
    global TodayLoginText := myGui.Add("Text", "x450 y210 w180 h20 Right", "0")
    global InactiveOperatorsText := myGui.Add("Text", "x450 y240 w180 h20 Right", "0")

    ; Recent operator activity panel
    myGui.Add("GroupBox", "x650 y90 w600 h220", "Recent Operator Activity")
    global RecentOperatorActivityList := myGui.Add("ListView", "x670 y120 w560 h170", ["Time", "Operator", "Action", "Details"])
    RecentOperatorActivityList.ModifyCol(1, 120)
    RecentOperatorActivityList.ModifyCol(2, 120)
    RecentOperatorActivityList.ModifyCol(3, 120)
    RecentOperatorActivityList.ModifyCol(4, 200)

    ; ==================== HARDWARE TAB ====================
    Tabs.UseTab(3)
    myGui.Add("Text", "x20 y90 w200 h20", "Hardware Management")
    myGui.Add("Button", "x20 y120 w200 h40 +0x1000", "View All Hardware").OnEvent("Click", (*) => ShowHardwareManagementDialog())
    myGui.Add("Button", "x20 y170 w200 h40 +0x1000", "Add New Hardware").OnEvent("Click", (*) => ShowAddHardwareDialog())
    myGui.Add("Button", "x20 y220 w200 h40 +0x1000", "Import Hardware List").OnEvent("Click", (*) => ShowImportHardwareDialog())

    ; ==================== ROOMS TAB ====================
    Tabs.UseTab(4)
    myGui.Add("Text", "x20 y90 w200 h20", "Room Configuration")
    myGui.Add("Button", "x20 y120 w200 h40 +0x1000", "View All Rooms").OnEvent("Click", (*) => ShowRoomConfigurationDialog())
    myGui.Add("Button", "x20 y170 w200 h40 +0x1000", "Add New Room").OnEvent("Click", (*) => ShowAddRoomDialog())
    myGui.Add("Button", "x20 y220 w200 h40 +0x1000", "Room Layout Editor").OnEvent("Click", (*) => ShowRoomLayoutDialog())

    ; ==================== REPORTS TAB ====================
    Tabs.UseTab(5)
    myGui.Add("Text", "x20 y90 w200 h20", "Reports")
    myGui.Add("Button", "x20 y120 w200 h40 +0x1000", "Verification Report").OnEvent("Click", (*) => ShowVerificationReportDialog())
    myGui.Add("Button", "x20 y170 w200 h40 +0x1000", "Seat Assignment Report").OnEvent("Click", (*) => ShowSeatReportDialog())
    myGui.Add("Button", "x20 y220 w200 h40 +0x1000", "Special Candidates Report").OnEvent("Click", (*) => ShowSpecialCandidatesReportDialog())
    myGui.Add("Button", "x20 y270 w200 h40 +0x1000", "Custom Report").OnEvent("Click", (*) => ShowCustomReportDialog())

    ; ==================== SETTINGS TAB ====================
    Tabs.UseTab(6)
    myGui.Add("Text", "x20 y90 w200 h20", "System Settings")
    myGui.Add("Button", "x20 y120 w200 h40 +0x1000", "Database Settings").OnEvent("Click", (*) => ShowDatabaseSettingsDialog())
    myGui.Add("Button", "x20 y170 w200 h40 +0x1000", "Verification Settings").OnEvent("Click", (*) => ShowVerificationSettingsDialog())
    myGui.Add("Button", "x20 y220 w200 h40 +0x1000", "Backup Settings").OnEvent("Click", (*) => ShowBackupSettingsDialog())
    myGui.Add("Button", "x20 y270 w200 h40 +0x1000", "System Log").OnEvent("Click", (*) => ShowSystemLogDialog())

    ; Return to default tab
    Tabs.UseTab()

    ; Update statistics
    UpdateStatistics()

    ; Show the GUI
    myGui.Show("w1280 h700")

    return myGui
}

; ; UpdateDateTime()
; ; Updates the date and time display in the GUI.
UpdateDateTime() {
    global DateTimeText
    if (IsObject(DateTimeText))
        DateTimeText.Text := FormatTime(, "dd-MMM-yyyy hh:mm:ss tt")
}

; ; UpdateStatistics()
; ; Updates the candidate and operator statistics in the GUI.
UpdateStatistics() {
    global TotalCandidatesText, VerifiedCandidatesText, SpecialCandidatesText, AssignedSeatsText, VerificationRateText
    global TotalOperatorsText, ActiveOperatorsText, AdminOperatorsText, TodayLoginText, InactiveOperatorsText

    ; Update candidate statistics
    if (IsObject(g_dbManager)) {
        try {
            stats := g_dbManager.GetCandidateStatistics()
            TotalCandidatesText.Text := stats["TotalCandidates"]
            VerifiedCandidatesText.Text := stats["VerifiedCandidates"]
            SpecialCandidatesText.Text := stats["SpecialCandidates"]
            AssignedSeatsText.Text := stats["AssignedSeats"]
            VerificationRateText.Text := stats["VerificationRate"] "%"
        } catch {
            ; Fallback to default values
            TotalCandidatesText.Text := "0"
            VerifiedCandidatesText.Text := "0"
            SpecialCandidatesText.Text := "0"
            AssignedSeatsText.Text := "0"
            VerificationRateText.Text := "0%"
        }
    }

    ; Update operator statistics
    UpdateOperatorStatistics()

    ; Set a timer to update statistics periodically
    SetTimer(UpdateStatistics, 60000)  ; Update every minute
}

; ; CreateBiometricDirectories()
; ; Creates the directory structure for biometric data storage.
CreateBiometricDirectories() {
    try {
        ; Create main operators directory
        operatorsDir := A_ScriptDir "\db\operators"
        if (!DirExist(operatorsDir)) {
            DirCreate(operatorsDir)
            ErrorHandler.LogMessage("INFO", "Created operators directory: " operatorsDir)
        }

        ; Create subdirectories
        photosDir := operatorsDir "\photos"
        if (!DirExist(photosDir)) {
            DirCreate(photosDir)
            ErrorHandler.LogMessage("INFO", "Created photos directory: " photosDir)
        }

        fingerprintsDir := operatorsDir "\fingerprints"
        if (!DirExist(fingerprintsDir)) {
            DirCreate(fingerprintsDir)
            ErrorHandler.LogMessage("INFO", "Created fingerprints directory: " fingerprintsDir)
        }

        logsDir := operatorsDir "\logs"
        if (!DirExist(logsDir)) {
            DirCreate(logsDir)
            ErrorHandler.LogMessage("INFO", "Created operator logs directory: " logsDir)
        }

    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Failed to create biometric directories: " err.Message)
    }
}

; ; UpdateOperatorStatistics()
; ; Updates the operator statistics in the GUI.
UpdateOperatorStatistics() {
    global TotalOperatorsText, ActiveOperatorsText, AdminOperatorsText, TodayLoginText, InactiveOperatorsText

    try {
        ; Initialize operator manager if not already done
        if (!IsObject(g_operatorManager)) {
            global g_operatorManager := CreateOperatorManager()
        }

        ; Get all operators
        operators := g_operatorManager.GetAllOperators()

        ; Calculate statistics
        totalOperators := operators.Length
        activeOperators := 0
        adminOperators := 0
        todayLogins := 0
        inactiveOperators := 0

        today := FormatTime(, "yyyyMMdd")

        for operator in operators {
            ; Count active operators
            if (operator.Status = "Active")
                activeOperators++
            else
                inactiveOperators++

            ; Count administrators
            if (operator.Role = "Administrator")
                adminOperators++

            ; Count today's logins
            if (operator.LastLogin != "" && SubStr(operator.LastLogin, 1, 8) = today)
                todayLogins++
        }

        ; Update the display
        TotalOperatorsText.Text := totalOperators
        ActiveOperatorsText.Text := activeOperators
        AdminOperatorsText.Text := adminOperators
        TodayLoginText.Text := todayLogins
        InactiveOperatorsText.Text := inactiveOperators

    } catch as err {
        ; Fallback to default values on error
        TotalOperatorsText.Text := "0"
        ActiveOperatorsText.Text := "0"
        AdminOperatorsText.Text := "0"
        TodayLoginText.Text := "0"
        InactiveOperatorsText.Text := "0"

        ErrorHandler.LogMessage("ERROR", "Failed to update operator statistics: " err.Message)
    }
}

; Placeholder functions for dialogs
ShowImportCandidatesDialog() {
    MsgBox("Import Candidates dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowExportDataDialog() {
    MsgBox("Export Data dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowSettingsDialog() {
    MsgBox("Settings dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowCandidateManagementDialog() {
    MsgBox("Candidate Management dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowOperatorManagementDialog() {
    ; Initialize operator manager if not already done
    if (!IsObject(g_operatorManager)) {
        global g_operatorManager := CreateOperatorManager()
    }

    ; Create the operator management dialog
    operatorGui := Gui("+Resize +MinSize600x400", "Operator Management")
    operatorGui.SetFont("s10", "Segoe UI")

    ; Add title
    operatorGui.Add("Text", "x10 y10 w580 h25 Center", "Operator Account Management").SetFont("s12 Bold")

    ; Add search controls
    operatorGui.Add("Text", "x10 y45 w60 h20", "Search:")
    searchEdit := operatorGui.Add("Edit", "x75 y43 w200 h23")
    searchBtn := operatorGui.Add("Button", "x285 y42 w80 h25", "Search")
    clearBtn := operatorGui.Add("Button", "x375 y42 w80 h25", "Clear")
    refreshBtn := operatorGui.Add("Button", "x465 y42 w80 h25", "Refresh")

    ; Add filter controls
    operatorGui.Add("Text", "x10 y75 w60 h20", "Filter:")
    filterCombo := operatorGui.Add("ComboBox", "x75 y73 w120 h200", ["All Operators", "Active Only", "Inactive Only", "Administrators", "Supervisors", "Operators", "Viewers"])
    filterCombo.Choose(1)  ; Default to "All Operators"

    ; Add ListView for operators with biometric status
    operatorList := operatorGui.Add("ListView", "x10 y105 w580 h250 Grid", ["Username", "Full Name", "Email", "Role", "Status", "Photo", "Fingerprint", "Created"])
    operatorList.ModifyCol(1, 80)   ; Username
    operatorList.ModifyCol(2, 120)  ; Full Name
    operatorList.ModifyCol(3, 120)  ; Email
    operatorList.ModifyCol(4, 80)   ; Role
    operatorList.ModifyCol(5, 60)   ; Status
    operatorList.ModifyCol(6, 50)   ; Photo
    operatorList.ModifyCol(7, 70)   ; Fingerprint
    operatorList.ModifyCol(8, 80)   ; Created

    ; Add action buttons
    addBtn := operatorGui.Add("Button", "x10 y365 w80 h30", "Add New")
    editBtn := operatorGui.Add("Button", "x100 y365 w80 h30", "Edit")
    deleteBtn := operatorGui.Add("Button", "x190 y365 w80 h30", "Delete")
    resetPwdBtn := operatorGui.Add("Button", "x280 y365 w100 h30", "Reset Password")
    closeBtn := operatorGui.Add("Button", "x510 y365 w80 h30", "Close")

    ; Function to load operators into ListView
    LoadOperators(filter := "") {
        operatorList.Delete()  ; Clear existing items

        try {
            operators := g_operatorManager.GetAllOperators()

            for operator in operators {
                ; Apply filter
                if (filter != "" && filter != "All Operators") {
                    switch filter {
                        case "Active Only":
                            if (operator.Status != "Active")
                                continue
                        case "Inactive Only":
                            if (operator.Status = "Active")
                                continue
                        case "Administrators":
                            if (operator.Role != "Administrator")
                                continue
                        case "Supervisors":
                            if (operator.Role != "Supervisor")
                                continue
                        case "Operators":
                            if (operator.Role != "Operator")
                                continue
                        case "Viewers":
                            if (operator.Role != "Viewer")
                                continue
                    }
                }

                ; Format dates for display
                createdDate := operator.Created != "" ? FormatTime(operator.Created, "yyyy-MM-dd") : ""

                ; Check biometric status
                photoStatus := (operator.PhotoPath != "" && FileExist(operator.PhotoPath)) ? "✓" : "✗"
                fingerprintStatus := (operator.FingerprintPath != "" && FileExist(operator.FingerprintPath)) ? "✓" : "✗"

                ; Add to ListView
                operatorList.Add("", operator.Username, operator.FullName, operator.Email,
                               operator.Role, operator.Status, photoStatus, fingerprintStatus, createdDate)
            }

            ; Update status
            if (IsObject(sbMain))
                sbMain.SetText("Loaded " operators.Length " operators")

        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to load operators: " err.Message)
            MsgBox("Failed to load operators: " err.Message, "Error", "Icon! 262144")
        }
    }

    ; Function to search operators
    SearchOperators() {
        searchTerm := searchEdit.Text
        if (searchTerm = "") {
            LoadOperators(filterCombo.Text)
            return
        }

        operatorList.Delete()

        try {
            operators := g_operatorManager.GetAllOperators()

            for operator in operators {
                ; Check if search term matches any field
                if (InStr(operator.Username, searchTerm) || InStr(operator.FullName, searchTerm) ||
                    InStr(operator.Email, searchTerm) || InStr(operator.Role, searchTerm)) {

                    ; Apply filter
                    filter := filterCombo.Text
                    if (filter != "" && filter != "All Operators") {
                        switch filter {
                            case "Active Only":
                                if (operator.Status != "Active")
                                    continue
                            case "Inactive Only":
                                if (operator.Status = "Active")
                                    continue
                            case "Administrators":
                                if (operator.Role != "Administrator")
                                    continue
                            case "Supervisors":
                                if (operator.Role != "Supervisor")
                                    continue
                            case "Operators":
                                if (operator.Role != "Operator")
                                    continue
                            case "Viewers":
                                if (operator.Role != "Viewer")
                                    continue
                        }
                    }

                    ; Format dates for display
                    createdDate := operator.Created != "" ? FormatTime(operator.Created, "yyyy-MM-dd") : ""

                    ; Check biometric status
                    photoStatus := (operator.PhotoPath != "" && FileExist(operator.PhotoPath)) ? "✓" : "✗"
                    fingerprintStatus := (operator.FingerprintPath != "" && FileExist(operator.FingerprintPath)) ? "✓" : "✗"

                    ; Add to ListView
                    operatorList.Add("", operator.Username, operator.FullName, operator.Email,
                                   operator.Role, operator.Status, photoStatus, fingerprintStatus, createdDate)
                }
            }

        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to search operators: " err.Message)
            MsgBox("Failed to search operators: " err.Message, "Error", "Icon! 262144")
        }
    }

    ; Event handlers
    searchBtn.OnEvent("Click", (*) => SearchOperators())
    clearBtn.OnEvent("Click", (*) => (searchEdit.Text := "", LoadOperators(filterCombo.Text)))
    refreshBtn.OnEvent("Click", (*) => LoadOperators(filterCombo.Text))
    filterCombo.OnEvent("Change", (*) => LoadOperators(filterCombo.Text))

    addBtn.OnEvent("Click", (*) => (operatorGui.Destroy(), ShowAddOperatorDialog()))
    editBtn.OnEvent("Click", EditOperator)
    deleteBtn.OnEvent("Click", DeleteOperator)
    resetPwdBtn.OnEvent("Click", ResetPassword)
    closeBtn.OnEvent("Click", (*) => operatorGui.Destroy())

    ; Handle Enter key in search box
    searchEdit.OnEvent("Change", (*) => SearchOperators())

    ; Handle dialog close
    operatorGui.OnEvent("Close", (*) => operatorGui.Destroy())

    ; Function to edit selected operator
    EditOperator(*) {
        selectedRow := operatorList.GetNext()
        if (selectedRow = 0) {
            MsgBox("Please select an operator to edit.", "No Selection", "Icon! 262144")
            return
        }

        username := operatorList.GetText(selectedRow, 1)
        operatorGui.Destroy()
        ShowEditOperatorDialog(username)
    }

    ; Function to delete selected operator
    DeleteOperator(*) {
        selectedRow := operatorList.GetNext()
        if (selectedRow = 0) {
            MsgBox("Please select an operator to delete.", "No Selection", "Icon! 262144")
            return
        }

        username := operatorList.GetText(selectedRow, 1)
        fullName := operatorList.GetText(selectedRow, 2)

        ; Confirm deletion
        result := MsgBox("Are you sure you want to delete operator '" username "' (" fullName ")?`n`nThis action cannot be undone.",
                        "Confirm Deletion", "YesNo Icon! 262144")

        if (result = "Yes") {
            try {
                if (g_operatorManager.DeleteOperator(username)) {
                    MsgBox("Operator deleted successfully.", "Success", "Icon! 262144")
                    LoadOperators(filterCombo.Text)  ; Refresh the list
                    UpdateOperatorStatistics()  ; Update statistics
                } else {
                    MsgBox("Failed to delete operator.", "Error", "Icon! 262144")
                }
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to delete operator: " err.Message)
                MsgBox("Failed to delete operator: " err.Message, "Error", "Icon! 262144")
            }
        }
    }

    ; Function to reset password for selected operator
    ResetPassword(*) {
        selectedRow := operatorList.GetNext()
        if (selectedRow = 0) {
            MsgBox("Please select an operator to reset password.", "No Selection", "Icon! 262144")
            return
        }

        username := operatorList.GetText(selectedRow, 1)
        fullName := operatorList.GetText(selectedRow, 2)

        ; Show password reset dialog
        ShowPasswordResetDialog(username, fullName)
    }

    ; Load initial data
    LoadOperators()

    ; Show the dialog
    operatorGui.Show("w600 h405")
}

ShowHardwareManagementDialog() {
    MsgBox("Hardware Management dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowRoomConfigurationDialog() {
    MsgBox("Room Configuration dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowReportsDialog() {
    MsgBox("Reports dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowAboutDialog() {
    MsgBox("WinCBT-Admin " version "`n`nDatabase Administration System for WinCBT-Biometric`n`n© 2025 Your Organization", "About WinCBT-Admin", "Icon! 262144")
}

ShowAddOperatorDialog() {
    ; Initialize operator manager if not already done
    if (!IsObject(g_operatorManager)) {
        global g_operatorManager := CreateOperatorManager()
    }

    ; Create the add operator dialog with larger size for biometric controls
    addGui := Gui("+AlwaysOnTop", "Add New Operator")
    addGui.SetFont("s10", "Segoe UI")

    ; Add title
    addGui.Add("Text", "x10 y10 w780 h25 Center", "Add New Operator Account").SetFont("s12 Bold")

    ; Left column - Basic Information
    addGui.Add("GroupBox", "x10 y40 w380 h320", "Basic Information")

    addGui.Add("Text", "x20 y65 w100 h20", "Username:")
    usernameEdit := addGui.Add("Edit", "x130 y63 w200 h23")
    addGui.Add("Text", "x340 y65 w50 h20 cRed", "*")

    addGui.Add("Text", "x20 y95 w100 h20", "Full Name:")
    fullNameEdit := addGui.Add("Edit", "x130 y93 w200 h23")
    addGui.Add("Text", "x340 y95 w50 h20 cRed", "*")

    addGui.Add("Text", "x20 y125 w100 h20", "Email:")
    emailEdit := addGui.Add("Edit", "x130 y123 w200 h23")

    addGui.Add("Text", "x20 y155 w100 h20", "Phone:")
    phoneEdit := addGui.Add("Edit", "x130 y153 w200 h23")

    addGui.Add("Text", "x20 y185 w100 h20", "Department:")
    departmentEdit := addGui.Add("Edit", "x130 y183 w200 h23")

    addGui.Add("Text", "x20 y215 w100 h20", "Role:")
    roleCombo := addGui.Add("ComboBox", "x130 y213 w200 h200", ["Administrator", "Supervisor", "Operator", "Viewer"])
    roleCombo.Choose(3)  ; Default to "Operator"
    addGui.Add("Text", "x340 y215 w50 h20 cRed", "*")

    addGui.Add("Text", "x20 y245 w100 h20", "Status:")
    statusCombo := addGui.Add("ComboBox", "x130 y243 w200 h200", ["Active", "Inactive"])
    statusCombo.Choose(1)  ; Default to "Active"

    addGui.Add("Text", "x20 y275 w100 h20", "Password:")
    passwordEdit := addGui.Add("Edit", "x130 y273 w200 h23 Password")
    addGui.Add("Text", "x340 y275 w50 h20 cRed", "*")

    addGui.Add("Text", "x20 y305 w100 h20", "Confirm Password:")
    confirmPasswordEdit := addGui.Add("Edit", "x130 y303 w200 h23 Password")
    addGui.Add("Text", "x340 y305 w50 h20 cRed", "*")

    addGui.Add("Text", "x20 y335 w100 h20", "Notes:")
    notesEdit := addGui.Add("Edit", "x130 y333 w200 h23")

    ; Right column - Biometric Data
    addGui.Add("GroupBox", "x400 y40 w380 h320", "Biometric Data")

    ; Photo capture section
    addGui.Add("Text", "x410 y65 w100 h20", "Photo:")
    ; Use Text control for webcam feed, Picture control for captured image
    try {
        photoPreview := addGui.Add("Text", "x410 y85 w160 h120 +Border +0x200 +Center", "No Photo")
        capturedPhotoControl := addGui.Add("Picture", "x410 y85 w160 h120 +Border +Hidden", "")

        ; Validate controls were created successfully
        if (!IsObject(photoPreview) || !IsObject(capturedPhotoControl)) {
            throw Error("Failed to create photo controls")
        }

        ErrorHandler.LogMessage("INFO", "Photo controls created successfully")
    } catch as controlErr {
        ErrorHandler.LogMessage("ERROR", "Failed to create photo controls: " controlErr.Message)
        MsgBox("Failed to create photo controls. The dialog may not function properly.", "Control Creation Error", "Icon! 262144")
    }

    capturePhotoBtn := addGui.Add("Button", "x410 y215 w75 h25", "Capture")
    retakePhotoBtn := addGui.Add("Button", "x495 y215 w75 h25", "Retake")
    retakePhotoBtn.Enabled := false

    ; Fingerprint capture section
    addGui.Add("Text", "x590 y65 w100 h20", "Fingerprint:")
    fingerprintPreview := addGui.Add("Picture", "x590 y85 w160 h120 +Border", "")
    fingerprintPreview.Text := "No Fingerprint"

    captureFingerprintBtn := addGui.Add("Button", "x590 y215 w75 h25", "Capture")
    retakeFingerprintBtn := addGui.Add("Button", "x675 y215 w75 h25", "Retake")
    retakeFingerprintBtn.Enabled := false

    ; Biometric status indicators
    photoStatusText := addGui.Add("Text", "x410 y245 w160 h20 Center", "Photo: Not Captured")
    fingerprintStatusText := addGui.Add("Text", "x590 y245 w160 h20 Center", "Fingerprint: Not Captured")

    ; Quality indicators
    photoQualityText := addGui.Add("Text", "x410 y265 w160 h20 Center", "")
    fingerprintQualityText := addGui.Add("Text", "x590 y265 w160 h20 Center", "")

    ; Variables to store captured data (declared at function scope for nested function access)
    capturedPhotoPath := ""
    capturedFingerprintPath := ""
    webcamHandle := 0
    isCapturingPhoto := false

    ; Add required field note
    addGui.Add("Text", "x20 y370 w300 h20 cRed", "* Required fields")

    ; Add role description
    roleDescText := addGui.Add("Text", "x20 y395 w360 h40 Wrap", "")

    ; Function to update role description
    UpdateRoleDescription() {
        selectedRole := roleCombo.Text
        if (OperatorManager.ROLES.Has(selectedRole)) {
            description := OperatorManager.ROLES[selectedRole]["description"]
            roleDescText.Text := "Role Description: " description
        }
    }

    ; Update description when role changes
    roleCombo.OnEvent("Change", (*) => UpdateRoleDescription())
    UpdateRoleDescription()  ; Set initial description

    ; Add buttons
    saveBtn := addGui.Add("Button", "x530 y445 w80 h30", "Save")
    cancelBtn := addGui.Add("Button", "x620 y445 w80 h30", "Cancel")

    ; Function to capture photo using webcam
    CapturePhoto(*) {
        ; Prevent multiple simultaneous capture attempts
        if (isCapturingPhoto) {
            photoStatusText.Text := "Photo: Capture in progress..."
            return
        }

        isCapturingPhoto := true
        local tempWebcamHandle := 0

        try {
            ; Validate controls exist before proceeding
            if (!IsObject(photoPreview) || !IsObject(capturedPhotoControl) || !IsObject(photoStatusText)) {
                throw Error("Required controls are not properly initialized")
            }

            ; Update status to show we're starting
            photoStatusText.Text := "Photo: Initializing camera..."
            photoStatusText.SetFont("c0x0000FF")  ; Blue color
            ErrorHandler.LogMessage("INFO", "Starting photo capture process")

            ; Stop any existing webcam safely
            if (webcamHandle != 0) {
                try {
                    ErrorHandler.LogMessage("INFO", "Stopping existing webcam handle: " webcamHandle)
                    StopWebcam(webcamHandle)
                    Sleep(100)  ; Give time for cleanup
                } catch as stopErr {
                    ErrorHandler.LogMessage("WARNING", "Error stopping existing webcam: " stopErr.Message)
                }
                webcamHandle := 0
            }

            ; Ensure the photo preview control is ready for webcam
            if (photoPreview.HasProp("Text")) {
                photoPreview.Text := "Starting camera..."
            }

            ; Make sure the webcam control is visible and captured image is hidden
            try {
                ToggleWebcamAndCapturedImage(photoPreview, capturedPhotoControl, true, true)
            } catch as toggleErr {
                ErrorHandler.LogMessage("WARNING", "Error toggling controls: " toggleErr.Message)
            }

            ; Start webcam for photo capture with better error handling
            photoStatusText.Text := "Photo: Starting camera..."
            ErrorHandler.LogMessage("INFO", "Attempting to start webcam on control")

            ; Try to detect available cameras first
            cameras := DetectWebCameras(true)
            cameraName := "Default Camera"
            if (cameras.Length > 0) {
                cameraName := cameras[1]  ; Use first available camera
                ErrorHandler.LogMessage("INFO", "Using camera: " cameraName)
            } else {
                ErrorHandler.LogMessage("WARNING", "No cameras detected, using default name")
            }

            ; Try to start webcam with enhanced error handling
            tempWebcamHandle := 0
            try {
                tempWebcamHandle := StartWebcam(photoPreview, cameraName)
            } catch as startErr {
                photoStatusText.Text := "Photo: Webcam Start Error"
                photoStatusText.SetFont("c0xFF0000")  ; Red color
                ErrorHandler.LogMessage("ERROR", "Webcam start crashed: " startErr.Message)
                MsgBox("Webcam initialization crashed: " startErr.Message "`n`nThis may be due to missing camera drivers or hardware issues.", "Webcam Crash", "Icon! 262144")
                return
            }

            if (tempWebcamHandle = 0) {
                photoStatusText.Text := "Photo: Camera Error"
                photoStatusText.SetFont("c0xFF0000")  ; Red color
                ErrorHandler.LogMessage("ERROR", "Failed to initialize webcam")

                ; Provide more detailed error information
                errorMsg := "Failed to initialize camera. Possible causes:`n`n"
                errorMsg .= "• Camera is not connected or not working`n"
                errorMsg .= "• Camera is in use by another application`n"
                errorMsg .= "• Camera drivers are not installed`n"
                errorMsg .= "• avicap32.dll is not available`n`n"
                errorMsg .= "Please check your camera and try again."

                MsgBox(errorMsg, "Camera Error", "Icon! 262144")
                return
            }

            ; Store the handle for cleanup
            webcamHandle := tempWebcamHandle
            ErrorHandler.LogMessage("INFO", "Webcam started successfully with handle: " webcamHandle)

            ; Wait for camera to stabilize with status update
            photoStatusText.Text := "Photo: Camera ready, preparing capture..."
            Sleep(2000)  ; Longer wait for better stability

            ; Validate webcam is still active before capture
            if (!DllCall("IsWindow", "Ptr", webcamHandle)) {
                throw Error("Webcam window was destroyed during initialization")
            }

            ; Update status before capture
            photoStatusText.Text := "Photo: Capturing image..."
            ErrorHandler.LogMessage("INFO", "Attempting to capture image")

            ; Capture the image with timeout protection
            result := CaptureWebcamImage(photoPreview, webcamHandle)

            if (result.success && result.filename != "") {
                ErrorHandler.LogMessage("INFO", "Image captured successfully: " result.filename)

                ; Validate captured file exists
                if (!FileExist(result.filename)) {
                    throw Error("Captured image file does not exist: " result.filename)
                }

                ; Generate unique filename for this operator
                username := usernameEdit.Text
                if (username = "") {
                    username := "temp_" FormatTime(, "yyyyMMddHHmmss")
                }

                ; Ensure photo directory exists
                photoDir := A_ScriptDir "\db\operators\photos"
                if (!DirExist(photoDir)) {
                    DirCreate(photoDir)
                    ErrorHandler.LogMessage("INFO", "Created photo directory: " photoDir)
                }

                ; Create final photo path
                capturedPhotoPath := photoDir "\" username "_photo.jpg"

                ; Copy captured image to final location with error handling
                try {
                    FileCopy(result.filename, capturedPhotoPath, true)
                    ErrorHandler.LogMessage("INFO", "Photo saved to: " capturedPhotoPath)

                    ; Verify the copied file exists
                    if (!FileExist(capturedPhotoPath)) {
                        throw Error("Failed to verify copied photo file")
                    }
                } catch as copyErr {
                    throw Error("Failed to save photo: " copyErr.Message)
                }

                ; Show captured image in the picture control with error handling
                try {
                    capturedPhotoControl.Value := capturedPhotoPath

                    ; Toggle visibility to show captured image
                    ToggleWebcamAndCapturedImage(photoPreview, capturedPhotoControl, false, true)
                } catch as displayErr {
                    ErrorHandler.LogMessage("WARNING", "Error displaying captured image: " displayErr.Message)
                    ; Continue anyway as the image was captured successfully
                }

                ; Update status
                photoStatusText.Text := "Photo: Captured"
                photoStatusText.SetFont("c0x008000")  ; Green color
                photoQualityText.Text := "Quality: Good"

                ; Enable retake button
                retakePhotoBtn.Enabled := true

                ; Stop webcam after successful capture
                try {
                    StopWebcam(webcamHandle)
                    webcamHandle := 0
                    ErrorHandler.LogMessage("INFO", "Webcam stopped after successful capture")
                } catch as stopErr {
                    ErrorHandler.LogMessage("WARNING", "Error stopping webcam after capture: " stopErr.Message)
                }

                ErrorHandler.LogMessage("INFO", "Photo captured successfully for operator: " username)
            } else {
                photoStatusText.Text := "Photo: Capture Failed"
                photoStatusText.SetFont("c0xFF0000")  ; Red color
                errorMsg := result.HasOwnProp("error") ? result.error : "Unknown capture error"
                ErrorHandler.LogMessage("ERROR", "Image capture failed: " errorMsg)
                MsgBox("Failed to capture photo: " errorMsg, "Capture Error", "Icon! 262144")

                ; Clean up webcam on failure
                if (webcamHandle != 0) {
                    try {
                        StopWebcam(webcamHandle)
                        webcamHandle := 0
                    } catch as stopErr {
                        ErrorHandler.LogMessage("WARNING", "Error stopping webcam after failed capture: " stopErr.Message)
                    }
                }
            }

        } catch as err {
            photoStatusText.Text := "Photo: Error"
            photoStatusText.SetFont("c0xFF0000")  ; Red color
            ErrorHandler.LogMessage("ERROR", "Photo capture error: " err.Message)
            MsgBox("Photo capture error: " err.Message, "Error", "Icon! 262144")

            ; Ensure webcam is stopped on any error
            if (webcamHandle != 0) {
                try {
                    StopWebcam(webcamHandle)
                    webcamHandle := 0
                } catch as stopErr {
                    ErrorHandler.LogMessage("WARNING", "Error stopping webcam after error: " stopErr.Message)
                }
            }
        } finally {
            ; Always reset the capture flag
            isCapturingPhoto := false
        }
    }

    ; Function to capture fingerprint
    CaptureFingerprint(*) {
        try {
            ; Initialize SecuGen fingerprint reader
            fpReader := SecuGenFingerprint()
            fpReader.Init()

            fingerprintStatusText.Text := "Fingerprint: Capturing..."
            fingerprintStatusText.SetFont("c0x0000FF")  ; Blue color

            ; Generate unique filename for this operator
            username := usernameEdit.Text
            if (username = "") {
                username := "temp_" FormatTime(, "yyyyMMddHHmmss")
            }

            ; Create fingerprint paths
            fpDir := A_ScriptDir "\db\operators\fingerprints"
            capturedFingerprintPath := fpDir "\" username "_fingerprint.dat"
            fpImagePath := fpDir "\" username "_fingerprint.bmp"

            ; Capture fingerprint template
            templateBuffer := fpReader.CaptureTemplate(capturedFingerprintPath)

            ; Also capture image for preview
            imageBuffer := fpReader.CaptureImage(fpImagePath)

            ; Get quality score
            quality := fpReader.GetImageQuality(imageBuffer)

            ; Update preview and status
            fingerprintPreview.Value := fpImagePath
            fingerprintStatusText.Text := "Fingerprint: Captured"
            fingerprintStatusText.SetFont("c0x008000")  ; Green color

            ; Display quality
            qualityText := ""
            if (quality >= 80)
                qualityText := "Quality: Excellent (" quality "%)"
            else if (quality >= 60)
                qualityText := "Quality: Good (" quality "%)"
            else if (quality >= 40)
                qualityText := "Quality: Fair (" quality "%)"
            else
                qualityText := "Quality: Poor (" quality "%)"

            fingerprintQualityText.Text := qualityText

            ; Enable retake button
            retakeFingerprintBtn.Enabled := true

            ; Close fingerprint reader
            fpReader.Close()

            ErrorHandler.LogMessage("INFO", "Fingerprint captured successfully for operator: " username " (Quality: " quality "%)")

        } catch as err {
            fingerprintStatusText.Text := "Fingerprint: Error"
            fingerprintStatusText.SetFont("c0xFF0000")  ; Red color

            ; Handle specific errors
            if (InStr(err.Message, "Device not found") || InStr(err.Message, "failed with result: 2")) {
                MsgBox("Fingerprint scanner not found. Please ensure the SecuGen fingerprint scanner is connected and drivers are installed.", "Scanner Not Found", "Icon! 262144")
            } else if (InStr(err.Message, "timed out")) {
                MsgBox("Fingerprint capture timed out. Please place your finger on the scanner and try again.", "Capture Timeout", "Icon! 262144")
            } else {
                MsgBox("Fingerprint capture error: " err.Message, "Error", "Icon! 262144")
            }

            ErrorHandler.LogMessage("ERROR", "Fingerprint capture error: " err.Message)
        }
    }

    ; Function to validate form
    ValidateForm() {
        errors := []

        ; Check required fields
        if (usernameEdit.Text = "")
            errors.Push("Username is required")
        else if (RegExMatch(usernameEdit.Text, "[^a-zA-Z0-9_]"))
            errors.Push("Username can only contain letters, numbers, and underscores")

        if (fullNameEdit.Text = "")
            errors.Push("Full Name is required")

        ; Email is now optional
        if (emailEdit.Text != "" && !RegExMatch(emailEdit.Text, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"))
            errors.Push("Please enter a valid email address")

        if (roleCombo.Text = "")
            errors.Push("Role is required")

        if (passwordEdit.Text = "")
            errors.Push("Password is required")
        else if (StrLen(passwordEdit.Text) < 4)
            errors.Push("Password must be at least 4 characters long")

        if (confirmPasswordEdit.Text = "")
            errors.Push("Password confirmation is required")
        else if (passwordEdit.Text != confirmPasswordEdit.Text)
            errors.Push("Passwords do not match")

        return errors
    }

    ; Function to save operator
    SaveOperator(*) {
        ; Validate form
        errors := ValidateForm()
        if (errors.Length > 0) {
            errorMsg := "Please correct the following errors:`n`n"
            for error in errors {
                errorMsg .= "• " error "`n"
            }
            MsgBox(errorMsg, "Validation Error", "Icon! 262144")
            return
        }

        ; Check if username already exists
        try {
            existingOperator := g_operatorManager.GetOperator(usernameEdit.Text)
            if (existingOperator.HasOwnProp("Username")) {
                MsgBox("Username '" usernameEdit.Text "' already exists. Please choose a different username.", "Username Exists", "Icon! 262144")
                usernameEdit.Focus()
                return
            }
        } catch {
            ; Username doesn't exist, which is what we want
        }

        ; Create operator data object
        operatorData := {}
        operatorData.Username := usernameEdit.Text
        operatorData.FullName := fullNameEdit.Text
        operatorData.Email := emailEdit.Text
        operatorData.Phone := phoneEdit.Text
        operatorData.Department := departmentEdit.Text
        operatorData.Role := roleCombo.Text
        operatorData.Status := statusCombo.Text
        operatorData.Password := passwordEdit.Text
        operatorData.Notes := notesEdit.Text

        ; Add biometric data paths if captured
        if (capturedPhotoPath != "") {
            operatorData.PhotoPath := capturedPhotoPath
        }
        if (capturedFingerprintPath != "") {
            operatorData.FingerprintPath := capturedFingerprintPath
        }

        ; Try to add the operator
        try {
            if (g_operatorManager.AddOperator(operatorData)) {
                MsgBox("Operator '" operatorData.FullName "' has been added successfully.", "Success", "Icon! 262144")

                ; Update statistics
                UpdateOperatorStatistics()

                ; Clean up webcam if still active
                if (webcamHandle != 0) {
                    try {
                        StopWebcam(webcamHandle)
                        webcamHandle := 0
                        ErrorHandler.LogMessage("INFO", "Webcam stopped during save operation")
                    } catch as stopErr {
                        ErrorHandler.LogMessage("WARNING", "Error stopping webcam during save: " stopErr.Message)
                    }
                }

                ; Close dialog
                addGui.Destroy()

                ; Optionally reopen operator management dialog
                ShowOperatorManagementDialog()
            } else {
                MsgBox("Failed to add operator. Please check the error log for details.", "Error", "Icon! 262144")
            }
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to add operator: " err.Message)
            MsgBox("Failed to add operator: " err.Message, "Error", "Icon! 262144")
        }
    }

    ; Function to handle retaking photos
    RetakePhoto(*) {
        try {
            ; Stop any active webcam first
            if (webcamHandle != 0) {
                try {
                    StopWebcam(webcamHandle)
                    webcamHandle := 0
                    ErrorHandler.LogMessage("INFO", "Stopped webcam for photo retake")
                } catch as stopErr {
                    ErrorHandler.LogMessage("WARNING", "Error stopping webcam for retake: " stopErr.Message)
                }
            }

            ; Reset photo state
            capturedPhotoPath := ""
            retakePhotoBtn.Enabled := false
            photoQualityText.Text := ""

            ; Show webcam control, hide captured image with error handling
            try {
                ToggleWebcamAndCapturedImage(photoPreview, capturedPhotoControl, true, true)
            } catch as toggleErr {
                ErrorHandler.LogMessage("WARNING", "Error toggling controls during retake: " toggleErr.Message)
            }

            ; Reset control states
            if (photoPreview.HasProp("Text")) {
                photoPreview.Text := "No Photo"
            }

            ; Clear captured image
            try {
                capturedPhotoControl.Value := ""
            } catch as clearErr {
                ErrorHandler.LogMessage("WARNING", "Error clearing captured image: " clearErr.Message)
            }

            ; Reset status
            photoStatusText.Text := "Photo: Not Captured"
            photoStatusText.SetFont("c0x000000")  ; Black color

            ; Reset capture flag
            isCapturingPhoto := false

            ErrorHandler.LogMessage("INFO", "Photo retake initiated successfully")
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Error during photo retake: " err.Message)
            photoStatusText.Text := "Photo: Retake Error"
            photoStatusText.SetFont("c0xFF0000")  ; Red color
        }
    }

    ; Function to safely cleanup and close dialog
    CleanupAndClose(*) {
        try {
            ; Reset capture flag first
            isCapturingPhoto := false

            ; Stop webcam if still active
            if (webcamHandle != 0) {
                try {
                    StopWebcam(webcamHandle)
                    ErrorHandler.LogMessage("INFO", "Webcam stopped during dialog close")
                } catch as stopErr {
                    ErrorHandler.LogMessage("WARNING", "Error stopping webcam during close: " stopErr.Message)
                }
                webcamHandle := 0
            }

            ; Clean up any temporary files if they exist
            try {
                if (capturedPhotoPath != "" && FileExist(capturedPhotoPath) && InStr(capturedPhotoPath, "temp_")) {
                    ; Only delete temporary files (those with temp_ prefix)
                    FileDelete(capturedPhotoPath)
                    ErrorHandler.LogMessage("INFO", "Cleaned up temporary photo file")
                }
            } catch as cleanupErr {
                ErrorHandler.LogMessage("WARNING", "Error cleaning up temporary files: " cleanupErr.Message)
            }

        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Error during cleanup: " err.Message)
        } finally {
            ; Always destroy the dialog
            try {
                addGui.Destroy()
            } catch as destroyErr {
                ErrorHandler.LogMessage("WARNING", "Error destroying dialog: " destroyErr.Message)
            }
        }
    }

    ; Event handlers for biometric capture
    capturePhotoBtn.OnEvent("Click", CapturePhoto)
    retakePhotoBtn.OnEvent("Click", RetakePhoto)
    captureFingerprintBtn.OnEvent("Click", CaptureFingerprint)
    retakeFingerprintBtn.OnEvent("Click", CaptureFingerprint)

    ; Event handlers for form
    saveBtn.OnEvent("Click", SaveOperator)
    cancelBtn.OnEvent("Click", CleanupAndClose)

    ; Handle dialog close
    addGui.OnEvent("Close", CleanupAndClose)

    ; Show the dialog with larger size
    addGui.Show("w800 h485")

    ; Focus on username field
    usernameEdit.Focus()
}

ShowPermissionsDialog() {
    MsgBox("Permissions dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowAddHardwareDialog() {
    MsgBox("Add Hardware dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowImportHardwareDialog() {
    MsgBox("Import Hardware dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowAddRoomDialog() {
    MsgBox("Add Room dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowRoomLayoutDialog() {
    MsgBox("Room Layout dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowVerificationReportDialog() {
    MsgBox("Verification Report dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowSeatReportDialog() {
    MsgBox("Seat Assignment Report dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowSpecialCandidatesReportDialog() {
    MsgBox("Special Candidates Report dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowCustomReportDialog() {
    MsgBox("Custom Report dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowDatabaseSettingsDialog() {
    MsgBox("Database Settings dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowVerificationSettingsDialog() {
    MsgBox("Verification Settings dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowBackupSettingsDialog() {
    MsgBox("Backup Settings dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowSystemLogDialog() {
    MsgBox("System Log dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowCandidateSearchDialog() {
    MsgBox("Candidate Search dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowOperatorSearchDialog() {
    MsgBox("Operator Search dialog will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowOperatorAttendanceDialog() {
    ; Initialize operator manager if not already done
    if (!IsObject(g_operatorManager)) {
        global g_operatorManager := CreateOperatorManager()
    }

    ; Create the operator attendance dialog
    attendanceGui := Gui("+Resize +MinSize800x600", "Operator Attendance - Exam Day Verification")
    attendanceGui.SetFont("s10", "Segoe UI")

    ; Add title
    attendanceGui.Add("Text", "x10 y10 w780 h25 Center", "Operator Attendance Verification System").SetFont("s14 Bold")

    ; Left panel - Verification Controls
    attendanceGui.Add("GroupBox", "x10 y40 w380 h520", "Biometric Verification")

    ; Fingerprint verification section
    attendanceGui.Add("Text", "x20 y65 w100 h20", "Fingerprint Verification:")
    fpPreview := attendanceGui.Add("Picture", "x20 y85 w200 h150 +Border", "")
    fpPreview.Text := "Place finger on scanner"

    scanFpBtn := attendanceGui.Add("Button", "x20 y245 w100 h30", "Scan Fingerprint")
    clearFpBtn := attendanceGui.Add("Button", "x130 y245 w100 h30", "Clear")

    fpStatusText := attendanceGui.Add("Text", "x20 y285 w350 h20", "Status: Ready for fingerprint scan")
    fpQualityText := attendanceGui.Add("Text", "x20 y305 w350 h20", "")
    fpMatchText := attendanceGui.Add("Text", "x20 y325 w350 h20", "")

    ; Photo verification section
    attendanceGui.Add("Text", "x20 y355 w100 h20", "Photo Verification:")
    photoPreview := attendanceGui.Add("Picture", "x20 y375 w200 h150 +Border", "")
    photoPreview.Text := "Operator photo will appear here"

    ; Manual verification controls
    attendanceGui.Add("Text", "x240 y375 w120 h20", "Manual Verification:")
    verifyPhotoBtn := attendanceGui.Add("Button", "x240 y395 w120 h25", "Verify Photo Match")
    rejectPhotoBtn := attendanceGui.Add("Button", "x240 y425 w120 h25", "Reject Photo")

    photoVerifyText := attendanceGui.Add("Text", "x240 y455 w120 h40 Wrap", "")

    ; Action buttons
    markPresentBtn := attendanceGui.Add("Button", "x20 y530 w100 h25", "Mark Present")
    markAbsentBtn := attendanceGui.Add("Button", "x130 y530 w100 h25", "Mark Absent")
    markPresentBtn.Enabled := false
    markAbsentBtn.Enabled := false

    ; Right panel - Operator List and Status
    attendanceGui.Add("GroupBox", "x400 y40 w380 h520", "Operator Status")

    ; Filter controls
    attendanceGui.Add("Text", "x410 y65 w60 h20", "Filter:")
    filterCombo := attendanceGui.Add("ComboBox", "x475 y63 w120 h200", ["All Operators", "Present", "Absent", "Pending"])
    filterCombo.Choose(1)
    refreshBtn := attendanceGui.Add("Button", "x605 y62 w80 h25", "Refresh")

    ; Operator list
    operatorList := attendanceGui.Add("ListView", "x410 y95 w360 h400 Grid", ["Username", "Full Name", "Role", "Status", "Verified"])
    operatorList.ModifyCol(1, 80)   ; Username
    operatorList.ModifyCol(2, 120)  ; Full Name
    operatorList.ModifyCol(3, 80)   ; Role
    operatorList.ModifyCol(4, 60)   ; Status
    operatorList.ModifyCol(5, 60)   ; Verified

    ; Statistics
    attendanceGui.Add("Text", "x410 y505 w100 h20", "Present:")
    attendanceGui.Add("Text", "x410 y525 w100 h20", "Absent:")
    attendanceGui.Add("Text", "x410 y545 w100 h20", "Total:")

    presentCountText := attendanceGui.Add("Text", "x515 y505 w50 h20 Right", "0")
    absentCountText := attendanceGui.Add("Text", "x515 y525 w50 h20 Right", "0")
    totalCountText := attendanceGui.Add("Text", "x515 y545 w50 h20 Right", "0")

    ; Close button
    closeBtn := attendanceGui.Add("Button", "x700 y530 w70 h25", "Close")

    ; Variables for verification process
    currentOperator := {}
    fpReader := ""

    ; Function to load operators into list
    LoadOperators(filter := "") {
        operatorList.Delete()

        try {
            operators := g_operatorManager.GetAllOperators()
            presentCount := 0
            absentCount := 0

            for operator in operators {
                ; Apply filter
                if (filter != "" && filter != "All Operators") {
                    switch filter {
                        case "Present":
                            if (operator.AttendanceStatus != "Present")
                                continue
                        case "Absent":
                            if (operator.AttendanceStatus != "Absent")
                                continue
                        case "Pending":
                            if (operator.AttendanceStatus != "Pending")
                                continue
                    }
                }

                ; Determine verification status
                verified := "No"
                if (operator.PhotoPath != "" && operator.FingerprintPath != "") {
                    verified := "Yes"
                } else if (operator.PhotoPath != "" || operator.FingerprintPath != "") {
                    verified := "Partial"
                }

                ; Count attendance
                if (operator.AttendanceStatus = "Present")
                    presentCount++
                else if (operator.AttendanceStatus = "Absent")
                    absentCount++

                ; Add to list
                operatorList.Add("", operator.Username, operator.FullName, operator.Role,
                               operator.AttendanceStatus, verified)
            }

            ; Update statistics
            presentCountText.Text := presentCount
            absentCountText.Text := absentCount
            totalCountText.Text := operators.Length

        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to load operators for attendance: " err.Message)
            MsgBox("Failed to load operators: " err.Message, "Error", "Icon! 262144")
        }
    }

    ; Function to scan fingerprint
    ScanFingerprint(*) {
        try {
            fpStatusText.Text := "Status: Initializing fingerprint scanner..."

            ; Initialize fingerprint reader
            fpReader := SecuGenFingerprint()
            fpReader.Init()

            fpStatusText.Text := "Status: Place finger on scanner..."
            fpStatusText.SetFont("c0x0000FF")  ; Blue color

            ; Capture fingerprint
            tempFpPath := A_ScriptDir "\temp\scanned_fingerprint.dat"
            tempFpImagePath := A_ScriptDir "\temp\scanned_fingerprint.bmp"

            ; Ensure temp directory exists
            if (!DirExist(A_ScriptDir "\temp")) {
                DirCreate(A_ScriptDir "\temp")
            }

            templateBuffer := fpReader.CaptureTemplate(tempFpPath)
            imageBuffer := fpReader.CaptureImage(tempFpImagePath)
            quality := fpReader.GetImageQuality(imageBuffer)

            ; Update preview
            fpPreview.Value := tempFpImagePath
            fpQualityText.Text := "Quality: " quality "%"

            if (quality < 40) {
                fpStatusText.Text := "Status: Poor quality - please rescan"
                fpStatusText.SetFont("c0xFF0000")  ; Red color
                fpReader.Close()
                return
            }

            fpStatusText.Text := "Status: Fingerprint captured - matching..."
            fpStatusText.SetFont("c0x008000")  ; Green color

            ; Match against all operators
            MatchFingerprint(tempFpPath)

            fpReader.Close()

        } catch as err {
            fpStatusText.Text := "Status: Scanner error"
            fpStatusText.SetFont("c0xFF0000")  ; Red color

            if (InStr(err.Message, "Device not found") || InStr(err.Message, "failed with result: 2")) {
                MsgBox("Fingerprint scanner not found. Please ensure the SecuGen fingerprint scanner is connected.", "Scanner Error", "Icon!")
            } else if (InStr(err.Message, "timed out")) {
                MsgBox("Fingerprint scan timed out. Please try again.", "Timeout", "Icon!")
            } else {
                MsgBox("Fingerprint scan error: " err.Message, "Error", "Icon!")
            }

            ErrorHandler.LogMessage("ERROR", "Fingerprint scan error: " err.Message)
        }
    }

    ; Function to match fingerprint against database
    MatchFingerprint(scannedFpPath) {
        try {
            operators := g_operatorManager.GetAllOperators()
            bestMatch := ""
            bestScore := 0

            for operator in operators {
                if (operator.FingerprintPath != "" && FileExist(operator.FingerprintPath)) {
                    ; Use SecuGen matching
                    fpReader := SecuGenFingerprint()
                    score := fpReader.MatchTemplates(scannedFpPath, operator.FingerprintPath)

                    if (score > bestScore) {
                        bestScore := score
                        bestMatch := operator.Username
                    }
                }
            }

            if (bestScore >= 70) {  ; Threshold for match
                ; Found a match
                currentOperator := g_operatorManager.GetOperator(bestMatch)
                fpMatchText.Text := "Match: " currentOperator.FullName " (" bestScore "%)"
                fpMatchText.SetFont("c0x008000")  ; Green color

                ; Load operator photo if available
                if (currentOperator.PhotoPath != "" && FileExist(currentOperator.PhotoPath)) {
                    photoPreview.Value := currentOperator.PhotoPath
                    photoVerifyText.Text := "Please verify photo matches operator"
                } else {
                    photoPreview.Text := "No photo available"
                    photoVerifyText.Text := "No photo to verify"
                }

                ; Enable action buttons
                markPresentBtn.Enabled := true
                markAbsentBtn.Enabled := true

                ; Select operator in list
                loop operatorList.GetCount() {
                    if (operatorList.GetText(A_Index, 1) = bestMatch) {
                        operatorList.Modify(A_Index, "Select")
                        break
                    }
                }

            } else {
                fpMatchText.Text := "No match found (Best: " bestScore "%)"
                fpMatchText.SetFont("c0xFF0000")  ; Red color
                currentOperator := {}
                markPresentBtn.Enabled := false
                markAbsentBtn.Enabled := false
            }

        } catch as err {
            fpMatchText.Text := "Matching error"
            fpMatchText.SetFont("c0xFF0000")  ; Red color
            ErrorHandler.LogMessage("ERROR", "Fingerprint matching error: " err.Message)
        }
    }

    ; Event handlers
    scanFpBtn.OnEvent("Click", ScanFingerprint)
    clearFpBtn.OnEvent("Click", (*) => (fpPreview.Text := "Place finger on scanner", fpStatusText.Text := "Status: Ready", fpQualityText.Text := "", fpMatchText.Text := "", currentOperator := {}, markPresentBtn.Enabled := false, markAbsentBtn.Enabled := false))

    verifyPhotoBtn.OnEvent("Click", (*) => (photoVerifyText.Text := "Photo verified manually", photoVerifyText.SetFont("c0x008000")))
    rejectPhotoBtn.OnEvent("Click", (*) => (photoVerifyText.Text := "Photo rejected", photoVerifyText.SetFont("c0xFF0000")))

    markPresentBtn.OnEvent("Click", (*) => MarkAttendance("Present"))
    markAbsentBtn.OnEvent("Click", (*) => MarkAttendance("Absent"))

    refreshBtn.OnEvent("Click", (*) => LoadOperators(filterCombo.Text))
    filterCombo.OnEvent("Change", (*) => LoadOperators(filterCombo.Text))
    closeBtn.OnEvent("Click", (*) => attendanceGui.Destroy())

    ; Function to mark attendance
    MarkAttendance(status) {
        if (currentOperator.Username = "") {
            MsgBox("No operator selected for attendance marking.", "No Selection", "Icon!")
            return
        }

        try {
            ; Update attendance status in database
            IniWrite(status, A_ScriptDir "\db\operators.ini", currentOperator.Username, "AttendanceStatus")

            ; Log attendance action
            logEntry := FormatTime(, "yyyy-MM-dd HH:mm:ss") " - " currentOperator.FullName " marked as " status
            logFile := A_ScriptDir "\db\operators\logs\" currentOperator.Username "_attendance.log"
            FileAppend(logEntry "`n", logFile)

            MsgBox("Operator " currentOperator.FullName " marked as " status ".", "Attendance Updated", "Icon!")

            ; Refresh the list
            LoadOperators(filterCombo.Text)

            ; Clear current selection
            currentOperator := {}
            markPresentBtn.Enabled := false
            markAbsentBtn.Enabled := false
            fpMatchText.Text := ""
            photoVerifyText.Text := ""

        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to mark attendance: " err.Message)
            MsgBox("Failed to mark attendance: " err.Message, "Error", "Icon! 262144")
        }
    }

    ; Handle dialog close
    attendanceGui.OnEvent("Close", (*) => attendanceGui.Destroy())

    ; Load initial data
    LoadOperators()

    ; Show the dialog
    attendanceGui.Show("w800 h570")
}

ShowEditOperatorDialog(username) {
    MsgBox("Edit Operator dialog for '" username "' will be implemented here.", "Not Implemented", "Icon! 262144")
}

ShowPasswordResetDialog(username, fullName) {
    ; Create password reset dialog
    resetGui := Gui("+AlwaysOnTop", "Reset Password")
    resetGui.SetFont("s10", "Segoe UI")

    ; Add title
    resetGui.Add("Text", "x10 y10 w280 h25 Center", "Reset Password for " fullName).SetFont("s12 Bold")

    ; Add form fields
    resetGui.Add("Text", "x20 y50 w100 h20", "New Password:")
    newPasswordEdit := resetGui.Add("Edit", "x130 y48 w150 h23 Password")

    resetGui.Add("Text", "x20 y80 w100 h20", "Confirm Password:")
    confirmPasswordEdit := resetGui.Add("Edit", "x130 y78 w150 h23 Password")

    ; Add buttons
    resetBtn := resetGui.Add("Button", "x130 y120 w80 h30", "Reset")
    cancelBtn := resetGui.Add("Button", "x220 y120 w80 h30", "Cancel")

    ; Function to reset password
    ResetPassword(*) {
        ; Validate passwords
        if (newPasswordEdit.Text = "") {
            MsgBox("Please enter a new password.", "Validation Error", "Icon! 262144")
            newPasswordEdit.Focus()
            return
        }

        if (StrLen(newPasswordEdit.Text) < 4) {
            MsgBox("Password must be at least 4 characters long.", "Validation Error", "Icon! 262144")
            newPasswordEdit.Focus()
            return
        }

        if (newPasswordEdit.Text != confirmPasswordEdit.Text) {
            MsgBox("Passwords do not match.", "Validation Error", "Icon! 262144")
            confirmPasswordEdit.Focus()
            return
        }

        ; Initialize operator manager if not already done
        if (!IsObject(g_operatorManager)) {
            global g_operatorManager := CreateOperatorManager()
        }

        ; Try to update the password
        try {
            updateData := {}
            updateData.Password := newPasswordEdit.Text

            if (g_operatorManager.UpdateOperator(username, updateData)) {
                MsgBox("Password has been reset successfully for " fullName ".", "Success", "Icon! 262144")
                resetGui.Destroy()
            } else {
                MsgBox("Failed to reset password. Please check the error log for details.", "Error", "Icon! 262144")
            }
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to reset password: " err.Message)
            MsgBox("Failed to reset password: " err.Message, "Error", "Icon! 262144")
        }
    }

    ; Event handlers
    resetBtn.OnEvent("Click", ResetPassword)
    cancelBtn.OnEvent("Click", (*) => resetGui.Destroy())

    ; Handle dialog close
    resetGui.OnEvent("Close", (*) => resetGui.Destroy())

    ; Show the dialog
    resetGui.Show("w300 h160")

    ; Focus on password field
    newPasswordEdit.Focus()
}
