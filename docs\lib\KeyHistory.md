# KeyHistory

Displays script info and a history of the most recent keystrokes and
mouse clicks.

``` Syntax
KeyHistory MaxEvents
```

## Parameters {#Parameters}

MaxEvents

:   Type: [Integer](../Concepts.htm#numbers)

    If omitted, the script\'s [main window](../Program.htm#main-window)
    will be shown, equivalent to selecting the \"View-\>Key history\"
    menu item. Otherwise, specify the maximum number of keyboard and
    mouse events that can be recorded for display in the window (limit
    500). The key history is also reset, but the main window is not
    shown or refreshed. Specify 0 to disable key history entirely.

## Remarks {#Remarks}

If KeyHistory is not used, the default setting is 40.

To disable key history, use the following:

    KeyHistory 0

This feature is intended to help [debug scripts and
hotkeys](../Scripts.htm#debug). It can also be used to detect the scan
code of a non-standard keyboard key using the steps described at the
bottom of the [key list](../KeyList.htm#SpecialKeys) page (knowing the
scan code allows such a key to be made into a hotkey).

The virtual key codes (VK) of the wheel events (<PERSON>Down, WheelUp,
WheelLeft, and WheelRight) are placeholder values that do not have any
meaning outside of AutoHotkey. Also, the scan code for wheel events is
actually the number of notches by which the wheel was turned (typically
1).

If the script does not have the [keyboard hook](InstallKeybdHook.htm)
installed, the KeyHistory window will display only the keyboard events
generated by the script itself (i.e. not the user\'s). If the script
does not have the [mouse hook](InstallMouseHook.htm) installed, mouse
button events will not be shown. You can find out if your script uses
either hook via \"View-\>Key History\" in the [script\'s main
window](../Program.htm#main-window) (accessible via \"Open\" in the
[tray icon](../Program.htm#tray-icon)). You can force the hooks to be
installed by adding either or both of the following lines to the script:

    InstallKeybdHook
    InstallMouseHook

Because each keystroke or mouse click consists of a down-event and an
up-event, KeyHistory displays only half as many \"complete events\" as
specified by *MaxEvents*. For example, if the script calls
`KeyHistory 50`, up to 25 keystrokes and mouse clicks will be displayed.

## Related {#Related}

[InstallKeybdHook](InstallKeybdHook.htm),
[InstallMouseHook](InstallMouseHook.htm),
[ListHotkeys](ListHotkeys.htm), [ListLines](ListLines.htm),
[ListVars](ListVars.htm), [GetKeyState](GetKeyState.htm),
[KeyWait](KeyWait.htm), [A_PriorKey](../Variables.htm#PriorKey)

## Examples {#Examples}

::: {#ExBasic .ex}
[](#ExBasic){.ex_number} Displays the history info in a window.

    KeyHistory
:::

::: {#ExMaxEvents .ex}
[](#ExMaxEvents){.ex_number} Causes KeyHistory to display the last 100
instead 40 keyboard and mouse events.

    KeyHistory 100
:::

::: {#ExDisable .ex}
[](#ExDisable){.ex_number} Disables key history entirely.

    KeyHistory 0
:::
