# EditGetLine

Returns the text of the specified line in an Edit control.

``` Syntax
Line := EditGetLine(N, Control , WinTitle, WinText, ExcludeTitle, ExcludeText)
```

## Parameters {#Parameters}

N

:   Type: [Integer](../Concepts.htm#numbers)

    The line number. Line 1 is the first line.

Control

:   Type: [String](../Concepts.htm#strings),
    [Integer](../Concepts.htm#numbers) or
    [Object](../Concepts.htm#objects)

    The control\'s ClassNN, text or HWND, or an object with a
    `Hwnd`{.no-highlight} property. For details, see [The Control
    Parameter](Control.htm#Parameter).

WinTitle, WinText, ExcludeTitle, ExcludeText

:   Type: [String](../Concepts.htm#strings),
    [Integer](../Concepts.htm#numbers) or
    [Object](../Concepts.htm#objects)

    If each of these is blank or omitted, the [Last Found
    Window](../misc/WinTitle.htm#LastFoundWindow) will be used.
    Otherwise, specify for *WinTitle* a [window title or other
    criteria](../misc/WinTitle.htm) to identify the target window and/or
    for *WinText* a substring from a single text element of the target
    window (as revealed by the included Window Spy utility).

    *ExcludeTitle* and *ExcludeText* can be used to exclude one or more
    windows by their title or text. Their specification is similar to
    *WinTitle* and *WinText*, except that *ExcludeTitle* does not
    recognize any criteria other than the window title.

    Window titles and text are case-sensitive. By default, hidden
    windows are not detected and hidden text elements are detected,
    unless changed with [DetectHiddenWindows](DetectHiddenWindows.htm)
    and [DetectHiddenText](DetectHiddenText.htm); however, when using
    [pure HWNDs](../misc/WinTitle.htm#ahk_id), hidden windows are always
    detected regardless of DetectHiddenWindows. By default, a window
    title can contain *WinTitle* or *ExcludeTitle* anywhere inside it to
    be a match, unless changed with
    [SetTitleMatchMode](SetTitleMatchMode.htm).

## Return Value {#Return_Value}

Type: [String](../Concepts.htm#strings)

This function returns the text of line *N* in an Edit control. Depending
on the nature of the control, the string might end in a carriage return
(\`r) or a carriage return + linefeed (\`r\`n).

## Error Handling {#Error_Handling}

A [TargetError](Error.htm#TargetError) is thrown if the window or
control could not be found.

A [ValueError](Error.htm#ValueError) is thrown if *N* is out of range or
otherwise invalid.

An [OSError](Error.htm#OSError) is thrown if a message could not be sent
to the control.

## Related {#Related}

[EditGetCurrentCol](EditGetCurrentCol.htm),
[EditGetCurrentLine](EditGetCurrentLine.htm),
[EditGetLineCount](EditGetLineCount.htm),
[EditGetSelectedText](EditGetSelectedText.htm),
[EditPaste](EditPaste.htm), [Control functions](Control.htm)

## Examples {#Examples}

::: {#ExBasic .ex}
[](#ExBasic){.ex_number} Retrieves the first line of the Notepad\'s Edit
control.

    line1 := EditGetLine(1, "Edit1", "ahk_class Notepad")
:::
