#Requires AutoHotkey v2.0

; Database functions for WinCBT-Admin
; This module handles database operations for the admin application

; Define global database path variables
global dbPath := IniRead(A_ScriptDir "\config.ini", "Paths", "dbPath", "db")

; Convert relative to absolute path if needed
if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
    dbPath := A_ScriptDir "\" dbPath
} else if (SubStr(dbPath, -1) != "\") {
    dbPath := dbPath "\"
}

; ; InitializeDatabasePaths()
; ; Initializes and validates database paths, creates required directories and files if they don't exist.
; ; @return: Object containing database paths.
InitializeDatabasePaths() {
    global dbPath

    ; Ensure dbPath ends with a backslash
    if (SubStr(dbPath, -1) != "\") {
        dbPath := dbPath "\"
    }

    ; Define database file paths
    configPath := dbPath "config.ini"
    candidatesPath := dbPath "candidates.ini"
    hardwarePath := dbPath "hardware.ini"
    roomsPath := dbPath "rooms.ini"
    seatAssignmentsPath := dbPath "seat_assignments.ini"
    operatorsPath := dbPath "operators.ini"
    candidatesImgPath := dbPath "img\candidates\"
    fingerprintPath := dbPath "fpt\"

    ; Create required subdirectories
    requiredDirs := [
        Map("path", dbPath "img", "desc", "Database images directory"),
        Map("path", candidatesImgPath, "desc", "Candidates images directory"),
        Map("path", fingerprintPath, "desc", "Fingerprint templates directory"),
        Map("path", dbPath "tmp", "desc", "Temporary files directory"),
        Map("path", dbPath "reports", "desc", "Reports directory"),
        Map("path", dbPath "backup", "desc", "Backup directory")
    ]

    for dir in requiredDirs {
        if (!DirExist(dir["path"])) {
            try {
                DirCreate(dir["path"])
                ErrorHandler.LogMessage("INFO", "Created " dir["desc"] ": " dir["path"])
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create " dir["desc"] ": " err.Message)
            }
        }
    }

    ; Create required files if they don't exist
    requiredFiles := [
        Map("path", configPath, "desc", "Configuration file"),
        Map("path", candidatesPath, "desc", "Candidates database"),
        Map("path", hardwarePath, "desc", "Hardware database"),
        Map("path", roomsPath, "desc", "Rooms database"),
        Map("path", seatAssignmentsPath, "desc", "Seat assignments database"),
        Map("path", operatorsPath, "desc", "Operators database")
    ]

    for file in requiredFiles {
        if (!FileExist(file["path"])) {
            try {
                FileAppend("", file["path"])
                ErrorHandler.LogMessage("INFO", "Created empty " file["desc"] ": " file["path"])
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create " file["desc"] ": " err.Message)
            }
        }
    }

    ; Return the paths as a Map
    return Map(
        "DB_PATH", dbPath,
        "CONFIG_PATH", configPath,
        "CANDIDATES_PATH", candidatesPath,
        "HARDWARE_PATH", hardwarePath,
        "ROOMS_PATH", roomsPath,
        "SEAT_ASSIGNMENTS_PATH", seatAssignmentsPath,
        "OPERATORS_PATH", operatorsPath,
        "CANDIDATES_IMG_PATH", candidatesImgPath,
        "FINGERPRINT_PATH", fingerprintPath
    )
}

; Initialize database paths
global dbPaths := InitializeDatabasePaths()

; Define global path variables for backward compatibility
global DB_PATH := dbPaths["DB_PATH"]
global CONFIG_PATH := dbPaths["CONFIG_PATH"]
global CANDIDATES_PATH := dbPaths["CANDIDATES_PATH"]
global HARDWARE_PATH := dbPaths["HARDWARE_PATH"]
global ROOMS_PATH := dbPaths["ROOMS_PATH"]
global SEAT_ASSIGNMENTS_PATH := dbPaths["SEAT_ASSIGNMENTS_PATH"]
global OPERATORS_PATH := dbPaths["OPERATORS_PATH"]
global CANDIDATES_IMG_PATH := dbPaths["CANDIDATES_IMG_PATH"]
global FINGERPRINT_PATH := dbPaths["FINGERPRINT_PATH"]

; ; AdminDBManager class
; ; Handles database operations for the admin application
class AdminDBManager {
    ; Cache for database data to avoid frequent disk reads
    static candidateCache := Map()
    static hardwareCache := Map()
    static roomCache := Map()
    static seatAssignmentCache := Map()
    static operatorCache := Map()

    ; ; __New()
    ; ; Initializes the AdminDBManager instance by loading data into caches.
    __New() {
        this.ReloadCache()
    }

    ; ; ReloadCache()
    ; ; Reloads all database caches from disk.
    ReloadCache() {
        this.LoadCandidateCache()
        this.LoadHardwareCache()
        this.LoadRoomCache()
        this.LoadSeatAssignmentCache()
        this.LoadOperatorCache()
    }

    ; ; LoadCandidateCache()
    ; ; Loads candidate data from candidates.ini into the cache.
    LoadCandidateCache() {
        AdminDBManager.candidateCache := Map()

        try {
            ; Read all sections from the candidates.ini file
            fileContent := FileRead(CANDIDATES_PATH)

            ; Parse the file to extract section names (roll numbers)
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    rollNumber := match[1]

                    ; Read all candidate data for this roll number
                    candidateData := Map()
                    candidateData["RollNumber"] := rollNumber
                    candidateData["Name"] := IniRead(CANDIDATES_PATH, rollNumber, "Name", "")
                    candidateData["FatherName"] := IniRead(CANDIDATES_PATH, rollNumber, "FatherName", "")
                    candidateData["DateOfBirth"] := IniRead(CANDIDATES_PATH, rollNumber, "DateOfBirth", "")
                    candidateData["Gender"] := IniRead(CANDIDATES_PATH, rollNumber, "Gender", "")
                    candidateData["Email"] := IniRead(CANDIDATES_PATH, rollNumber, "Email", "")
                    candidateData["Mobile"] := IniRead(CANDIDATES_PATH, rollNumber, "Mobile", "")
                    candidateData["Status"] := IniRead(CANDIDATES_PATH, rollNumber, "Status", "Active")
                    candidateData["Special"] := IniRead(CANDIDATES_PATH, rollNumber, "Special", "0")
                    candidateData["PhotoStatus"] := IniRead(CANDIDATES_PATH, rollNumber, "PhotoStatus", "")
                    candidateData["BiometricStatus"] := IniRead(CANDIDATES_PATH, rollNumber, "BiometricStatus", "")
                    candidateData["FingerprintStatus"] := IniRead(CANDIDATES_PATH, rollNumber, "FingerprintStatus", "")
                    candidateData["RightFingerprintStatus"] := IniRead(CANDIDATES_PATH, rollNumber, "RightFingerprintStatus", "")
                    candidateData["SignatureStatus"] := IniRead(CANDIDATES_PATH, rollNumber, "SignatureStatus", "")
                    candidateData["ThumbPreference"] := IniRead(CANDIDATES_PATH, rollNumber, "ThumbPreference", "")

                    ; Add to cache
                    AdminDBManager.candidateCache[rollNumber] := candidateData
                }
            }

            ErrorHandler.LogMessage("INFO", "Loaded " AdminDBManager.candidateCache.Count " candidates into cache")
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to load candidate cache: " err.Message)
        }
    }

    ; ; LoadHardwareCache()
    ; ; Loads hardware data from hardware.ini into the cache.
    LoadHardwareCache() {
        AdminDBManager.hardwareCache := Map()

        try {
            ; Read all sections from the hardware.ini file
            fileContent := FileRead(HARDWARE_PATH)

            ; Parse the file to extract section names (MAC addresses)
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    macAddress := match[1]

                    ; Read hardware data
                    hwData := Map()
                    hwData["ip"] := IniRead(HARDWARE_PATH, macAddress, "ip", "")
                    hwData["hwid"] := IniRead(HARDWARE_PATH, macAddress, "hwid", "")
                    hwData["floor"] := IniRead(HARDWARE_PATH, macAddress, "floor", "")
                    hwData["room"] := IniRead(HARDWARE_PATH, macAddress, "room", "")
                    hwData["seat"] := IniRead(HARDWARE_PATH, macAddress, "seat", "")
                    hwData["is_active"] := IniRead(HARDWARE_PATH, macAddress, "is_active", "0")

                    ; Construct seat ID
                    hwData["seatId"] := "F" hwData["floor"] "-R" hwData["room"] "-S" hwData["seat"]

                    ; Add to cache
                    AdminDBManager.hardwareCache[macAddress] := hwData
                }
            }

            ErrorHandler.LogMessage("INFO", "Loaded " AdminDBManager.hardwareCache.Count " hardware entries into cache")
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to load hardware cache: " err.Message)
        }
    }

    ; ; LoadRoomCache()
    ; ; Loads room data from rooms.ini into the cache.
    LoadRoomCache() {
        AdminDBManager.roomCache := Map()

        try {
            ; Read all sections from the rooms.ini file
            fileContent := FileRead(ROOMS_PATH)

            ; Parse the file to extract section names (room IDs)
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    roomId := match[1]

                    ; Read room data
                    roomData := Map()
                    roomData["Floor"] := IniRead(ROOMS_PATH, roomId, "Floor", "")
                    roomData["Room"] := IniRead(ROOMS_PATH, roomId, "Room", "")
                    roomData["Name"] := IniRead(ROOMS_PATH, roomId, "Name", "")
                    roomData["TotalSeats"] := IniRead(ROOMS_PATH, roomId, "TotalSeats", "0")
                    roomData["BufferSeats"] := IniRead(ROOMS_PATH, roomId, "BufferSeats", "0")
                    roomData["Priority"] := IniRead(ROOMS_PATH, roomId, "Priority", "0")
                    roomData["IsActive"] := IniRead(ROOMS_PATH, roomId, "IsActive", "1")
                    roomData["SpecialNeeds"] := IniRead(ROOMS_PATH, roomId, "SpecialNeeds", "0")

                    ; Add to cache
                    AdminDBManager.roomCache[roomId] := roomData
                }
            }

            ErrorHandler.LogMessage("INFO", "Loaded " AdminDBManager.roomCache.Count " rooms into cache")
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to load room cache: " err.Message)
        }
    }

    ; ; LoadSeatAssignmentCache()
    ; ; Loads seat assignment data from seat_assignments.ini into the cache.
    LoadSeatAssignmentCache() {
        AdminDBManager.seatAssignmentCache := Map()

        try {
            ; Read all sections from the seat_assignments.ini file
            fileContent := FileRead(SEAT_ASSIGNMENTS_PATH)

            ; Parse the file to extract section names (seat IDs)
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    seatId := match[1]

                    ; Read seat assignment data
                    assignmentData := Map()
                    assignmentData["RollNumber"] := IniRead(SEAT_ASSIGNMENTS_PATH, seatId, "RollNumber", "")
                    assignmentData["Name"] := IniRead(SEAT_ASSIGNMENTS_PATH, seatId, "Name", "")
                    assignmentData["AssignedTime"] := IniRead(SEAT_ASSIGNMENTS_PATH, seatId, "AssignedTime", "")
                    assignmentData["AssignedBy"] := IniRead(SEAT_ASSIGNMENTS_PATH, seatId, "AssignedBy", "")
                    assignmentData["ExamID"] := IniRead(SEAT_ASSIGNMENTS_PATH, seatId, "ExamID", "")

                    ; Add to cache
                    AdminDBManager.seatAssignmentCache[seatId] := assignmentData
                }
            }

            ErrorHandler.LogMessage("INFO", "Loaded " AdminDBManager.seatAssignmentCache.Count " seat assignments into cache")
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to load seat assignment cache: " err.Message)
        }
    }

    ; ; LoadOperatorCache()
    ; ; Loads operator data from operators.ini into the cache.
    LoadOperatorCache() {
        AdminDBManager.operatorCache := Map()

        try {
            ; Read all sections from the operators.ini file
            fileContent := FileRead(OPERATORS_PATH)

            ; Parse the file to extract section names (operator usernames)
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    username := match[1]

                    ; Read operator data
                    operatorData := Map()
                    operatorData["Username"] := username
                    operatorData["FullName"] := IniRead(OPERATORS_PATH, username, "FullName", "")
                    operatorData["Email"] := IniRead(OPERATORS_PATH, username, "Email", "")
                    operatorData["Phone"] := IniRead(OPERATORS_PATH, username, "Phone", "")
                    operatorData["Department"] := IniRead(OPERATORS_PATH, username, "Department", "")
                    operatorData["Role"] := IniRead(OPERATORS_PATH, username, "Role", "Operator")
                    operatorData["Created"] := IniRead(OPERATORS_PATH, username, "Created", "")
                    operatorData["LastLogin"] := IniRead(OPERATORS_PATH, username, "LastLogin", "")
                    operatorData["Status"] := IniRead(OPERATORS_PATH, username, "Status", "Active")
                    operatorData["Notes"] := IniRead(OPERATORS_PATH, username, "Notes", "")

                    ; Add to cache
                    AdminDBManager.operatorCache[username] := operatorData
                }
            }

            ErrorHandler.LogMessage("INFO", "Loaded " AdminDBManager.operatorCache.Count " operators into cache")
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to load operator cache: " err.Message)
        }
    }

    ; ; GetCandidateStatistics()
    ; ; Returns statistics about candidates in the database.
    ; ; @return: Object with statistics.
    GetCandidateStatistics() {
        stats := Map(
            "TotalCandidates", AdminDBManager.candidateCache.Count,
            "VerifiedCandidates", 0,
            "SpecialCandidates", 0,
            "AssignedSeats", 0,
            "VerificationRate", 0
        )

        ; Count verified candidates
        for rollNumber, candidateData in AdminDBManager.candidateCache {
            if (candidateData["BiometricStatus"] = "Verified")
                stats["VerifiedCandidates"]++

            if (candidateData["Special"] = "1")
                stats["SpecialCandidates"]++
        }

        ; Count assigned seats
        stats["AssignedSeats"] := AdminDBManager.seatAssignmentCache.Count

        ; Calculate verification rate
        if (stats["TotalCandidates"] > 0)
            stats["VerificationRate"] := Round((stats["VerifiedCandidates"] / stats["TotalCandidates"]) * 100)

        return stats
    }
}
