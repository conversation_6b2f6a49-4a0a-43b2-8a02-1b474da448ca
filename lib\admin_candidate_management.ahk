#Requires AutoHotkey v2.0

; Candidate Management functionality for WinCBT-Admin
; This module handles candidate database management for the admin application

; Define paths
global CANDIDATE_DB_PATH := IniRead(A_ScriptDir "\config.ini", "Paths", "dbPath", "db")
; Convert relative to absolute path if needed
if (SubStr(CANDIDATE_DB_PATH, 1, 1) != "\" && SubStr(CANDIDATE_DB_PATH, 2, 1) != ":") {
    CANDIDATE_DB_PATH := A_ScriptDir "\" CANDIDATE_DB_PATH "\"
} else if (SubStr(CANDIDATE_DB_PATH, -1) != "\") {
    CANDIDATE_DB_PATH := CANDIDATE_DB_PATH "\"
}

; Define database file paths
global CANDIDATE_CANDIDATES_PATH := CANDIDATE_DB_PATH "candidates.ini"
global CANDIDATE_IMG_PATH := CANDIDATE_DB_PATH "img\candidates\"
global CANDIDATE_FPT_PATH := CANDIDATE_DB_PATH "fpt\"

; ; CandidateManager class
; ; Handles candidate database management for the admin application
class CandidateManager {
    ; Required fields for candidates
    static REQUIRED_FIELDS := ["RollNumber", "Name"]

    ; Default fields for candidates
    static DEFAULT_FIELDS := Map(
        "Status", "Active",
        "PhotoStatus", "",
        "BiometricStatus", "",
        "FingerprintStatus", "",
        "RightFingerprintStatus", "",
        "SignatureStatus", ""
    )

    ; Constructor
    __New() {
        ; Nothing to initialize
    }

    ; Get all candidates
    ; Parameters:
    ; - filter: Function to filter candidates (optional)
    ; Returns: Array of candidate objects
    GetAllCandidates(filter := "") {
        candidates := []

        try {
            ; Read all sections from the INI file
            fileContent := FileRead(CANDIDATE_CANDIDATES_PATH)
            sections := []

            ; Parse the file to extract section names
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    sections.Push(match[1])
                }
            }

            ; Read data for each candidate
            for rollNumber in sections {
                candidateData := {}
                candidateData.RollNumber := rollNumber
                candidateData.Name := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Name", "")
                candidateData.FatherName := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "FatherName", "")
                candidateData.DateOfBirth := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "DateOfBirth", "")
                candidateData.Gender := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Gender", "")
                candidateData.Email := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Email", "")
                candidateData.Mobile := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Mobile", "")
                candidateData.Status := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Status", "Active")
                candidateData.Special := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Special", "0")
                candidateData.PhotoStatus := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "PhotoStatus", "")
                candidateData.BiometricStatus := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "BiometricStatus", "")
                candidateData.FingerprintStatus := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "FingerprintStatus", "")
                candidateData.RightFingerprintStatus := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "RightFingerprintStatus", "")
                candidateData.SignatureStatus := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "SignatureStatus", "")
                candidateData.ThumbPreference := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "ThumbPreference", "")

                ; Get file paths
                candidateData.Picture := CANDIDATE_IMG_PATH rollNumber "_photo.jpg"
                candidateData.Signature := CANDIDATE_IMG_PATH rollNumber "_signature.jpg"
                candidateData.Fingerprint := CANDIDATE_FPT_PATH rollNumber "_fingerprint.fpt"
                candidateData.RightFingerprint := CANDIDATE_FPT_PATH rollNumber "_right_fingerprint.fpt"

                ; Check if files exist
                candidateData.HasPicture := FileExist(candidateData.Picture) ? true : false
                candidateData.HasSignature := FileExist(candidateData.Signature) ? true : false
                candidateData.HasFingerprint := FileExist(candidateData.Fingerprint) ? true : false
                candidateData.HasRightFingerprint := FileExist(candidateData.RightFingerprint) ? true : false

                ; Apply filter if provided
                if (IsObject(filter)) {
                    if (filter(candidateData))
                        candidates.Push(candidateData)
                } else {
                    candidates.Push(candidateData)
                }
            }

            return candidates
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get candidates: " err.Message)
            return []
        }
    }

    ; Get candidate by roll number
    ; Parameters:
    ; - rollNumber: The roll number of the candidate
    ; Returns: Candidate object or empty object if not found
    GetCandidate(rollNumber) {
        try {
            ; Check if candidate exists
            if (!FileExist(CANDIDATE_CANDIDATES_PATH) || IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Name", "") = "")
                return {}

            ; Read candidate data
            candidateData := {}
            candidateData.RollNumber := rollNumber
            candidateData.Name := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Name", "")
            candidateData.FatherName := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "FatherName", "")
            candidateData.DateOfBirth := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "DateOfBirth", "")
            candidateData.Gender := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Gender", "")
            candidateData.Email := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Email", "")
            candidateData.Mobile := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Mobile", "")
            candidateData.Status := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Status", "Active")
            candidateData.Special := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Special", "0")
            candidateData.PhotoStatus := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "PhotoStatus", "")
            candidateData.BiometricStatus := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "BiometricStatus", "")
            candidateData.FingerprintStatus := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "FingerprintStatus", "")
            candidateData.RightFingerprintStatus := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "RightFingerprintStatus", "")
            candidateData.SignatureStatus := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "SignatureStatus", "")
            candidateData.ThumbPreference := IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "ThumbPreference", "")

            ; Get file paths
            candidateData.Picture := CANDIDATE_IMG_PATH rollNumber "_photo.jpg"
            candidateData.Signature := CANDIDATE_IMG_PATH rollNumber "_signature.jpg"
            candidateData.Fingerprint := CANDIDATE_FPT_PATH rollNumber "_fingerprint.fpt"
            candidateData.RightFingerprint := CANDIDATE_FPT_PATH rollNumber "_right_fingerprint.fpt"

            ; Check if files exist
            candidateData.HasPicture := FileExist(candidateData.Picture) ? true : false
            candidateData.HasSignature := FileExist(candidateData.Signature) ? true : false
            candidateData.HasFingerprint := FileExist(candidateData.Fingerprint) ? true : false
            candidateData.HasRightFingerprint := FileExist(candidateData.RightFingerprint) ? true : false

            return candidateData
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get candidate: " err.Message)
            return {}
        }
    }

    ; Add a new candidate
    ; Parameters:
    ; - candidateData: Object with candidate data
    ; Returns: True if successful, False otherwise
    AddCandidate(candidateData) {
        try {
            ; Validate required fields
            for field in CandidateManager.REQUIRED_FIELDS {
                if (!candidateData.HasOwnProp(field) || candidateData[field] = "")
                    throw Error(field " is required")
            }

            ; Check if candidate already exists
            if (IniRead(CANDIDATE_CANDIDATES_PATH, candidateData.RollNumber, "Name", "") != "")
                throw Error("Candidate with roll number " candidateData.RollNumber " already exists")

            ; Write candidate data to INI file
            for field, value in candidateData {
                ; Skip file paths
                if (field = "Picture" || field = "Signature" || field = "Fingerprint" || field = "RightFingerprint")
                    continue

                ; Skip file existence flags
                if (field = "HasPicture" || field = "HasSignature" || field = "HasFingerprint" || field = "HasRightFingerprint")
                    continue

                IniWrite(value, CANDIDATE_CANDIDATES_PATH, candidateData.RollNumber, field)
            }

            ; Set default values for missing fields
            for field, defaultValue in CandidateManager.DEFAULT_FIELDS {
                if (!candidateData.HasOwnProp(field))
                    IniWrite(defaultValue, CANDIDATE_CANDIDATES_PATH, candidateData.RollNumber, field)
            }

            ErrorHandler.LogMessage("INFO", "Added new candidate: " candidateData.RollNumber)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to add candidate: " err.Message)
            return false
        }
    }

    ; Update an existing candidate
    ; Parameters:
    ; - rollNumber: The roll number of the candidate to update
    ; - candidateData: Object with candidate data to update
    ; Returns: True if successful, False otherwise
    UpdateCandidate(rollNumber, candidateData) {
        try {
            ; Check if candidate exists
            if (IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Name", "") = "")
                throw Error("Candidate not found")

            ; Update candidate data
            for field, value in candidateData {
                ; Skip roll number field
                if (field = "RollNumber")
                    continue

                ; Skip file paths
                if (field = "Picture" || field = "Signature" || field = "Fingerprint" || field = "RightFingerprint")
                    continue

                ; Skip file existence flags
                if (field = "HasPicture" || field = "HasSignature" || field = "HasFingerprint" || field = "HasRightFingerprint")
                    continue

                IniWrite(value, CANDIDATE_CANDIDATES_PATH, rollNumber, field)
            }

            ErrorHandler.LogMessage("INFO", "Updated candidate: " rollNumber)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to update candidate: " err.Message)
            return false
        }
    }

    ; Delete a candidate
    ; Parameters:
    ; - rollNumber: The roll number of the candidate to delete
    ; - deleteFiles: Whether to delete associated files (default: true)
    ; Returns: True if successful, False otherwise
    DeleteCandidate(rollNumber, deleteFiles := true) {
        try {
            ; Check if candidate exists
            if (IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Name", "") = "")
                throw Error("Candidate not found")

            ; Delete associated files if requested
            if (deleteFiles) {
                ; Define file paths
                photoPath := CANDIDATE_IMG_PATH rollNumber "_photo.jpg"
                signaturePath := CANDIDATE_IMG_PATH rollNumber "_signature.jpg"
                fingerprintPath := CANDIDATE_FPT_PATH rollNumber "_fingerprint.fpt"
                rightFingerprintPath := CANDIDATE_FPT_PATH rollNumber "_right_fingerprint.fpt"

                ; Delete files if they exist
                if (FileExist(photoPath))
                    FileDelete(photoPath)

                if (FileExist(signaturePath))
                    FileDelete(signaturePath)

                if (FileExist(fingerprintPath))
                    FileDelete(fingerprintPath)

                if (FileExist(rightFingerprintPath))
                    FileDelete(rightFingerprintPath)

                ; Delete any captured fingerprint files
                Loop Files, CANDIDATE_FPT_PATH rollNumber "_captured_fingerprint_*.fpt" {
                    FileDelete(A_LoopFileFullPath)
                }

                Loop Files, CANDIDATE_FPT_PATH rollNumber "_captured_fingerprint_*.bmp" {
                    FileDelete(A_LoopFileFullPath)
                }
            }

            ; Delete the candidate section
            IniDelete(CANDIDATE_CANDIDATES_PATH, rollNumber)

            ErrorHandler.LogMessage("INFO", "Deleted candidate: " rollNumber)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to delete candidate: " err.Message)
            return false
        }
    }

    ; Search for candidates
    ; Parameters:
    ; - searchTerm: The term to search for
    ; - searchFields: Array of fields to search in (default: ["RollNumber", "Name"])
    ; Returns: Array of matching candidate objects
    SearchCandidates(searchTerm, searchFields := ["RollNumber", "Name"]) {
        try {
            ; Create search filter function
            searchFilter(candidate) {
                for field in searchFields {
                    if (candidate.HasOwnProp(field) && InStr(candidate[field], searchTerm))
                        return true
                }
                return false
            }

            ; Get candidates using the filter
            return this.GetAllCandidates(searchFilter)
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to search candidates: " err.Message)
            return []
        }
    }

    ; Get candidates by status
    ; Parameters:
    ; - status: The status to filter by (e.g., "Active", "Inactive")
    ; Returns: Array of matching candidate objects
    GetCandidatesByStatus(status) {
        try {
            ; Create status filter function
            statusFilter(candidate) {
                return candidate.Status = status
            }

            ; Get candidates using the filter
            return this.GetAllCandidates(statusFilter)
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get candidates by status: " err.Message)
            return []
        }
    }

    ; Get special candidates
    ; Returns: Array of special candidate objects
    GetSpecialCandidates() {
        try {
            ; Create special filter function
            specialFilter(candidate) {
                return candidate.Special = "1"
            }

            ; Get candidates using the filter
            return this.GetAllCandidates(specialFilter)
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get special candidates: " err.Message)
            return []
        }
    }

    ; Get verified candidates
    ; Returns: Array of verified candidate objects
    GetVerifiedCandidates() {
        try {
            ; Create verified filter function
            verifiedFilter(candidate) {
                return candidate.BiometricStatus = "Verified"
            }

            ; Get candidates using the filter
            return this.GetAllCandidates(verifiedFilter)
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get verified candidates: " err.Message)
            return []
        }
    }

    ; Get unverified candidates
    ; Returns: Array of unverified candidate objects
    GetUnverifiedCandidates() {
        try {
            ; Create unverified filter function
            unverifiedFilter(candidate) {
                return candidate.BiometricStatus != "Verified"
            }

            ; Get candidates using the filter
            return this.GetAllCandidates(unverifiedFilter)
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get unverified candidates: " err.Message)
            return []
        }
    }

    ; Import a photo for a candidate
    ; Parameters:
    ; - rollNumber: The roll number of the candidate
    ; - photoPath: The path to the photo file
    ; Returns: True if successful, False otherwise
    ImportPhoto(rollNumber, photoPath) {
        try {
            ; Check if candidate exists
            if (IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Name", "") = "")
                throw Error("Candidate not found")

            ; Check if photo file exists
            if (!FileExist(photoPath))
                throw Error("Photo file not found")

            ; Define destination path
            destPath := CANDIDATE_IMG_PATH rollNumber "_photo.jpg"

            ; Create directory if it doesn't exist
            if (!DirExist(CANDIDATE_IMG_PATH))
                DirCreate(CANDIDATE_IMG_PATH)

            ; Copy the photo
            FileCopy(photoPath, destPath, true)

            ; Update photo status
            IniWrite("", CANDIDATE_CANDIDATES_PATH, rollNumber, "PhotoStatus")

            ErrorHandler.LogMessage("INFO", "Imported photo for candidate: " rollNumber)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to import photo: " err.Message)
            return false
        }
    }

    ; Import a signature for a candidate
    ; Parameters:
    ; - rollNumber: The roll number of the candidate
    ; - signaturePath: The path to the signature file
    ; Returns: True if successful, False otherwise
    ImportSignature(rollNumber, signaturePath) {
        try {
            ; Check if candidate exists
            if (IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Name", "") = "")
                throw Error("Candidate not found")

            ; Check if signature file exists
            if (!FileExist(signaturePath))
                throw Error("Signature file not found")

            ; Define destination path
            destPath := CANDIDATE_IMG_PATH rollNumber "_signature.jpg"

            ; Create directory if it doesn't exist
            if (!DirExist(CANDIDATE_IMG_PATH))
                DirCreate(CANDIDATE_IMG_PATH)

            ; Copy the signature
            FileCopy(signaturePath, destPath, true)

            ; Update signature status
            IniWrite("", CANDIDATE_CANDIDATES_PATH, rollNumber, "SignatureStatus")

            ErrorHandler.LogMessage("INFO", "Imported signature for candidate: " rollNumber)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to import signature: " err.Message)
            return false
        }
    }

    ; Import a fingerprint template for a candidate
    ; Parameters:
    ; - rollNumber: The roll number of the candidate
    ; - fingerprintPath: The path to the fingerprint template file
    ; - isRightThumb: Whether this is a right thumb template (default: false)
    ; Returns: True if successful, False otherwise
    ImportFingerprint(rollNumber, fingerprintPath, isRightThumb := false) {
        try {
            ; Check if candidate exists
            if (IniRead(CANDIDATE_CANDIDATES_PATH, rollNumber, "Name", "") = "")
                throw Error("Candidate not found")

            ; Check if fingerprint file exists
            if (!FileExist(fingerprintPath))
                throw Error("Fingerprint file not found")

            ; Define destination path
            if (isRightThumb)
                destPath := CANDIDATE_FPT_PATH rollNumber "_right_fingerprint.fpt"
            else
                destPath := CANDIDATE_FPT_PATH rollNumber "_fingerprint.fpt"

            ; Create directory if it doesn't exist
            if (!DirExist(CANDIDATE_FPT_PATH))
                DirCreate(CANDIDATE_FPT_PATH)

            ; Copy the fingerprint template
            FileCopy(fingerprintPath, destPath, true)

            ; Update fingerprint status
            if (isRightThumb)
                IniWrite("", CANDIDATE_CANDIDATES_PATH, rollNumber, "RightFingerprintStatus")
            else
                IniWrite("", CANDIDATE_CANDIDATES_PATH, rollNumber, "FingerprintStatus")

            ErrorHandler.LogMessage("INFO", "Imported fingerprint for candidate: " rollNumber)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to import fingerprint: " err.Message)
            return false
        }
    }
}
