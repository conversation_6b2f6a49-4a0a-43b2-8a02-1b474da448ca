# Break

Exits (terminates) any type of [loop
statement](../Language.htm#loop-statement).

``` Syntax
Break LoopLabel
```

## Parameters {#Parameters}

LoopLabel

:   If omitted or 1, this statement applies to the innermost loop in
    which it is enclosed. Otherwise, specify which loop this statement
    should apply to; either by [label name](../misc/Labels.htm) or
    numeric nesting level. If a [label](../misc/Labels.htm) is
    specified, it must point directly at a [loop
    statement](../Language.htm#loop-statement).

    *LoopLabel* must be a constant value - variables and expressions are
    not supported, with the exception of a single literal number or
    quoted string enclosed in parentheses. For example: `break("outer")`

## Remarks {#Remarks}

The use of Break and [Continue](Continue.htm) are encouraged over
[Goto](Goto.htm) since they usually make scripts more readable and
maintainable.

## Related {#Related}

[Continue](Continue.htm), [Loop](Loop.htm), [While-loop](While.htm),
[For-loop](For.htm), [Blocks](Block.htm), [Labels](../misc/Labels.htm)

## Examples {#Examples}

::: {#ExBasic .ex}
[](#ExBasic){.ex_number} Breaks the loop if `var`{.variable} is greater
than 25.

    Loop
    {
        ; ...
        if (var > 25)
            break
        ; ...
        if (var <= 5)
            continue
    }
:::

::: {#ExBreakOuter .ex}
[](#ExBreakOuter){.ex_number} Breaks the outer loop from within a nested
loop.

    outer:
    Loop 3
    {
        x := A_Index
        Loop 3
        {
            if (x*A_Index = 6)
                break outer  ; Equivalent to break 2 or goto break_outer.
            MsgBox x "," A_Index
        }
    }
    break_outer: ; For goto.
:::
