@import url('highlighter/dark.css');

/* general */

* {
  scrollbar-base-color: #1c1d20;
  scrollbar-face-color: #34363c;
  scrollbar-3dlight-color: #1c1d20;
  scrollbar-highlight-color: #1c1d20;
  scrollbar-track-color: #1c1d20;
  scrollbar-arrow-color: #34363c;
  scrollbar-shadow-color: #1c1d20;
}

::-webkit-scrollbar {
  background-color: #1c1d20;
}

::-webkit-scrollbar-track {
  background-color: #1c1d20;
}

::-webkit-scrollbar-thumb {
  background-color: #34363c;
  border: 2px solid #1c1d20;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #474a52;
}

::-webkit-scrollbar-corner {
  background-color: inherit;
}

::-ms-expand {
  background-color: #202124;
  color: #34363c;
  border-color: #1c1d20;
}

hr {
  border-color: #353535;
  background-color: #353535;
}

select {
  border-color: #353535;
  background-color: #202124;
  color: inherit;
}

/* theme.css */

body {
  background-color: #202124;
  color: #bdc1c6;
}

h1 {
  color: inherit;
}

h2 {
  color: #a25a5a;
}

h3 {
  color: #428242;
}

h4.func_section {
  color: #a25a5a;
  border-bottom-color: #2e2e2e;
}

h1, h2, h3, h4 {
  border-bottom-color: #353535;
}

a:link, a:visited, a:visited:hover {
  color: #8ab4f8;
}

pre, code {
  background-color: #262525;
}

.Syntax {
  background-color: #3e3823;
  border-color: #443b1c;
}

.Syntax span.func {
  color: #ffa;
}

table.info td {
  border-color: #353535;
}

table.info th {
  background-color: #1c1d20;
  border-color: #353535;
}

table.info tr.sep_below {
  border-bottom-color: #353535;
}

.blue {
  color: royalblue;
}

.note {
  border-left-color: #90b790;
  background-color: #293029;
  color: #90b790;
}

.warning {
  border-left-color: #d2aeae;
  background-color: #2f2a26;
  color: #d2aeae;
}

blockquote {
  background-color: #2e2e2e;
}

.methodShort {
  border-color: #353535;
}

.methodShort h2, .methodShort h3 {
  color: inherit;
}

.methodShort:target {
  border-color: inherit;
}

dt {
  color: inherit;
}

dd {
  border-left-color: #353535;
}

kbd {
  border-color: #202124;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), 0 0 0 2px #353535 inset;
  color: #bdc1c6;
  background-color: #202124;
  text-shadow: 0 1px 0 #000;
}

.ex {
  border-color: #353535;
}

.regex {
  background-color: #382d1f;
}

.subj {
  background-color: #233523;
}

/* content.css */

#head {
  background-color: #171717;
  color: #a3a3a3;
}

#head li:hover {
  background-color: inherit;
  color: #fff;
}

#head .selected {
  background-color: #6a6e72;
  color: #171717;
}

#head li.selected:hover {
  background-color: #6a6e72;
  color: #fff;
}

#left {
  background-color: #1c1d20;
  color: #bdc1c6;
}

.lds-dual-ring:after {
  border-color: #a3a3a3 transparent #a3a3a3 transparent;
}

#left .toc ul, #left .quick .main ul {
  color: #a3a3a3;
}

#left .toc ul > li > ul, #left .quick .main > ul {
  border-left-color: #353535;
}

#left .toc ul > li.highlighted > ul {
  border-left-color: #a3a3a3;
}

#left .toc li.closed > span, #left .toc li.opened > span {
  color: #bdc1c6;
}

#left .toc .selected {
  border-left-color: #a3a3a3;
}

#left .input input {
  border-color: #353535;
  background-color: #202124;
}

#left .input input.match {
  background-color: #324232;
}

#left .input input.mismatch {
  background-color: #664766;
}

#left .select select {
  border-color: #353535;
  background-color: #202124;
}

#left .select select > option {
  color: #bdc1c6;
}

#left .select .empty {
  color: grey !important;
}

#left .list {
  background-color: #202124;
  border-color: #353535;
}

#left .search .checkbox label {
  border-color: #353535;
}

#left .search .checkbox .updown {
  border-color: #353535;
}

#left .list > a:hover {
  background-color: #353535;
}

#left .list > a.selected {
  background-color: #6a6e72;
  color: #171717;
}

#left .deprecated {
  color: indianred !important;
}

#left .quick .header {
  border-top-color: #353535;
  border-bottom-color: #353535;
}

.dragbar {
  background-color: #353535;
}

#right .footer {
  border-top-color: #353535;
}

#right .back-to-top {
  background-color: #171717;
  color: #a3a3a3;
}

#right pre.parent > div.buttons > a {
  background-color: #171717;
  color: #a3a3a3;
}

#right table.mobile {
  border-color: #353535;
}

#right table.mobile tbody {
  border-top-color: #353535;
}

#right table.mobile tbody > tr:first-child {
  background-color: #1c1d20;
}

#right table.mobile td:first-child {
  background-color: #1c1d20;
}

#right .deprecated {
  color: indianred;
  border-bottom-color: indianred;
}

pre.origin em, code em {
  color: #6a9955;
}
