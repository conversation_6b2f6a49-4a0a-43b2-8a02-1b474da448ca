# SetMouseDelay

Sets the delay that will occur after each mouse movement or click.

``` Syntax
SetMouseDelay Delay , "Play"
```

## Parameters {#Parameters}

Delay

:   Type: [Integer](../Concepts.htm#numbers)

    Time in milliseconds. Specify -1 for no delay at all or 0 for the
    smallest possible delay (however, if the *Play* parameter is
    present, both 0 and -1 produce no delay).

Play

:   Type: [String](../Concepts.htm#strings)

    If blank or omitted, the delay is applied to the traditional
    SendEvent mode. Otherwise, specify the word **Play** to apply the
    delay to the [SendPlay mode](Send.htm#SendPlayDetail). If a script
    never uses this parameter, the delay is always -1 for SendPlay.

## Return Value {#Return_Value}

Type: [Integer](../Concepts.htm#numbers)

This function returns the previous setting.

## Remarks {#Remarks}

If SetMouseDelay is not used, the default delay is 10 for the
traditional SendEvent mode and -1 for [SendPlay
mode](Send.htm#SendPlayDetail).

A short delay (sleep) is done automatically after every mouse movement
or click generated by [<PERSON>lick](Click.htm), [<PERSON>Move](MouseMove.htm),
[MouseClick](MouseClick.htm), and [MouseClickDrag](MouseClickDrag.htm)
(except for [SendInput mode](SendMode.htm#Input)). This is done to
improve the reliability of scripts because a window sometimes can\'t
keep up with a rapid flood of mouse events.

Due to the granularity of the OS\'s time-keeping system, delays might be
rounded up to the nearest multiple of 10 or 15.

A delay of 0 internally executes a Sleep(0), which yields the remainder
of the script\'s timeslice to any other process that may need it. If
there is none, Sleep(0) will not sleep at all. By contrast, a delay of
-1 will never sleep.

The built-in variable **A_MouseDelay** contains the current setting for
Send/SendEvent mode. **A_MouseDelayPlay** contains the current setting
for [SendPlay mode](Send.htm#SendPlayDetail).

Every newly launched [thread](../misc/Threads.htm) (such as a
[hotkey](../Hotkeys.htm), [custom menu item](Menu.htm), or
[timed](SetTimer.htm) subroutine) starts off fresh with the default
setting for this function. That default may be changed by using this
function during [script startup](../Scripts.htm#auto).

## Related {#Related}

[SetDefaultMouseSpeed](SetDefaultMouseSpeed.htm), [Click](Click.htm),
[MouseMove](MouseMove.htm), [MouseClick](MouseClick.htm),
[MouseClickDrag](MouseClickDrag.htm), [SendMode](SendMode.htm),
[SetKeyDelay](SetKeyDelay.htm), [SetControlDelay](SetControlDelay.htm),
[SetWinDelay](SetWinDelay.htm)

## Examples {#Examples}

::: {#ExBasic .ex}
[](#ExBasic){.ex_number} Causes the smallest possible delay to occur
after each mouse movement or click.

    SetMouseDelay 0
:::
