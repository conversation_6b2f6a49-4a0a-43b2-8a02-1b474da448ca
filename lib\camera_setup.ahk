#Requires AutoHotkey v2.0

; WinCBT-Biometric Camera Setup Dialog
; Provides a GUI for detecting and configuring camera devices

; ; DetectCameras()
; ; Detects available camera devices using FFmpeg only
; ; @return: Array of camera names
DetectCameras() {
    cameras := []

    ; Use FFmpeg to list DirectShow devices
    ffmpegPath := A_ScriptDir "\bin\ffmpeg.exe"
    if (!FileExist(ffmpegPath)) {
        OutputDebug("FFmpeg executable not found at: " ffmpegPath)
        MsgBox("FFmpeg executable not found at: " ffmpegPath "`n`nPlease make sure FFmpeg is installed in the bin directory.", "Camera Detection Error", "Icon!")
        return cameras
    }

    ; Create a temporary file to store the output
    tempFile := A_Temp "\camera_list.txt"

    ; Ensure the temporary file doesn't exist
    if (FileExist(tempFile))
        FileDelete(tempFile)

    ; Run FFmpeg with the DirectShow device list command using cmd.exe to ensure proper redirection
    ffmpegCmd := ffmpegPath . " -list_devices true -f dshow -i dummy"
    cmdLine := A_ComSpec . " /c " . ffmpegCmd . " > `"" . tempFile . "`" 2>&1"

    try {
        ; Run the command and wait for it to complete
        OutputDebug("Running command: " . cmdLine)
        RunWait(cmdLine, , "Hide")

        ; Read the output file
        if (FileExist(tempFile)) {
            OutputDebug("Reading output file: " . tempFile)
            fileContent := FileRead(tempFile)

            ; Debug output
            OutputDebug("File content length: " . StrLen(fileContent))

            ; Parse the output to find camera names
            ; FFmpeg lists devices with lines like: [dshow @ 000001e2542c7b40] "HD Pro Webcam C920" (video)
            ; followed by lines with "Alternative name" containing the device path
            inVideoDevices := true  ; Assume we're in the video devices section by default
            videoDeviceName := ""

            Loop Parse, fileContent, "`n", "`r" {
                OutputDebug("Parsing line: " . A_LoopField)

                ; Check for video device section
                if (InStr(A_LoopField, "DirectShow video devices")) {
                    OutputDebug("Found video devices section")
                    inVideoDevices := true
                    continue
                }

                ; Stop when we reach audio devices section
                if (InStr(A_LoopField, "DirectShow audio devices") || InStr(A_LoopField, "(audio)")) {
                    OutputDebug("Found audio devices section, stopping")
                    inVideoDevices := false
                    continue
                }

                ; Extract camera name directly from the dshow output line
                ; Format: [dshow @ XXXXXXXX] "Camera Name" (video)
                if (InStr(A_LoopField, "(video)")) {
                    ; Extract the camera name between quotes
                    if (RegExMatch(A_LoopField, "\] `"(.+)`" \(video\)", &match)) {
                        videoDeviceName := match[1]
                        OutputDebug("Found video device: " . videoDeviceName)
                        cameras.Push(videoDeviceName)
                    } else if (RegExMatch(A_LoopField, "`"(.+)`" \(video\)", &match)) {
                        videoDeviceName := match[1]
                        OutputDebug("Found video device (alt method): " . videoDeviceName)
                        cameras.Push(videoDeviceName)
                    } else {
                        ; Try a more general approach to extract text between quotes
                        startPos := InStr(A_LoopField, "`"")
                        if (startPos > 0) {
                            endPos := InStr(A_LoopField, "`"", false, startPos + 1)
                            if (endPos > startPos) {
                                videoDeviceName := SubStr(A_LoopField, startPos + 1, endPos - startPos - 1)
                                OutputDebug("Found video device (general method): " . videoDeviceName)
                                cameras.Push(videoDeviceName)
                            }
                        }
                    }
                }
            }

            ; Delete the temporary file
            try {
                FileDelete(tempFile)
                OutputDebug("Deleted temporary file")
            } catch Error as e {
                OutputDebug("Error deleting temporary file: " . e.Message)
            }
        } else {
            OutputDebug("Output file not found: " . tempFile)
        }
    } catch Error as e {
        OutputDebug("Error detecting cameras: " . e.Message)
    }

    ; Log the detected cameras
    OutputDebug("Detected " . cameras.Length . " cameras")
    for i, camera in cameras {
        OutputDebug("Camera " . i . ": " . camera)
    }

    return cameras
}

; ; ShowCameraSetupDialog()
; ; Shows a dialog for detecting and configuring camera devices
; ; @return: True if camera was configured, False otherwise
ShowCameraSetupDialog() {
    ; Create the dialog
    setupGui := Gui("+AlwaysOnTop +Owner", "Camera Setup")
    setupGui.SetFont("s10", "Segoe UI")

    ; Add controls
    setupGui.Add("Text", "x20 y20 w400 h20", "Select a camera device to use for photo capture:")

    ; Add a ComboBox for camera selection
    CameraCombo := setupGui.Add("ComboBox", "x20 y50 w400 h200")

    ; Add status text
    StatusText := setupGui.Add("Text", "x20 y80 w400 h20", "Detecting cameras...")

    ; Add buttons
    ButtonDetect := setupGui.Add("Button", "x20 y120 w120 h30 +0x1000", "Detect Cameras")
    ButtonSave := setupGui.Add("Button", "x150 y120 w120 h30 Disabled +0x1000", "Save")
    ButtonCancel := setupGui.Add("Button", "x280 y120 w120 h30 +0x1000", "Cancel")

    ; Current camera from config
    configFile := A_ScriptDir "\WinCBT-Biometric.ini"
    ; Fall back to old config file if new one doesn't exist
    if (!FileExist(configFile)) {
        configFile := A_ScriptDir "\config.ini"
    }
    currentCamera := IniRead(configFile, "Camera", "CameraName", "")

    ; If not found, try the old section for backward compatibility
    if (currentCamera == "") {
        currentCamera := IniRead(configFile, "BiometricDevices", "Camera", "")
        OutputDebug("Camera name not found in [Camera] section, using value from [BiometricDevices]: " currentCamera)
    } else {
        OutputDebug("Using camera name from [Camera] section: " currentCamera)
    }

    ; Event handlers
    ButtonDetect.OnEvent("Click", DetectCamerasHandler)
    ButtonSave.OnEvent("Click", SaveCamera)
    ButtonCancel.OnEvent("Click", CancelDialog)
    CameraCombo.OnEvent("Change", OnCameraSelect)

    ; Handle dialog close
    setupGui.OnEvent("Close", CancelDialog)

    ; Cancel dialog function
    CancelDialog(*) {
        ; Destroy the dialog
        setupGui.Destroy()
        OutputDebug("Camera setup dialog closed")
    }

    ; Detect cameras function
    DetectCamerasHandler(*) {
        ; Clear the combo box
        CameraCombo.Delete()

        ; Update status
        StatusText.Value := "Detecting cameras..."
        OutputDebug("Starting camera detection")

        ; Detect cameras using the global function
        cameras := DetectCameras()

        ; Add cameras to the combo box
        if (cameras.Length > 0) {
            selectedIndex := 1  ; Default to first camera

            OutputDebug("Found " cameras.Length " cameras")
            for i, camera in cameras {
                CameraCombo.Add([camera])
                OutputDebug("Added camera " i ": " camera)

                ; Check if this is the current camera
                if (camera == currentCamera) {
                    selectedIndex := i
                    OutputDebug("Current camera matched at index " i)
                }
            }

            ; Select the current camera or the first one
            CameraCombo.Choose(selectedIndex)
            OutputDebug("Selected camera index: " selectedIndex)

            ; Enable the save button
            ButtonSave.Enabled := true

            ; Update status
            StatusText.Value := cameras.Length . " camera(s) found."
        } else {
            StatusText.Value := "No cameras detected. Make sure your camera is connected."
            OutputDebug("No cameras detected")
            ButtonSave.Enabled := false
        }
    }

    ; Camera selection handler
    OnCameraSelect(*) {
        ; Get the selected camera index (0-based)
        selectedIndex := CameraCombo.Value - 1

        ; Enable the Save button if a camera is selected
        if (selectedIndex >= 0) {
            ButtonSave.Enabled := true
            StatusText.Value := "Camera selected: " . CameraCombo.Text
        } else {
            ButtonSave.Enabled := false
        }
    }

    ; Save camera function
    SaveCamera(*) {
        ; Get the selected camera
        selectedIndex := CameraCombo.Value
        if (selectedIndex > 0) {
            selectedCamera := CameraCombo.Text

            ; Calculate the camera index (0-based)
            cameraIndex := selectedIndex - 1

            ; Save to WinCBT-Biometric.ini
            try {
                configFile := A_ScriptDir "\WinCBT-Biometric.ini"

                ; Save to the new [Camera] section
                IniWrite(selectedCamera, configFile, "Camera", "CameraName")
                IniWrite(cameraIndex, configFile, "Camera", "CameraIndex")
                OutputDebug("Saved camera configuration - Name: " selectedCamera ", Index: " cameraIndex)
            } catch as err {
                OutputDebug("Error saving camera configuration: " err.Message)
                MsgBox("Error saving camera configuration: " err.Message, "Camera Setup Error", "Icon! +AlwaysOnTop")
                return
            }

            ; Show success message
            MsgBox("Camera '" selectedCamera "' has been configured successfully.", "Camera Setup", "262208 +AlwaysOnTop")

            ; Close the dialog
            setupGui.Destroy()
            OutputDebug("Camera setup dialog closed after saving")
        }
    }

    ; Initial camera detection
    DetectCamerasHandler()

    ; Show the dialog
    setupGui.Show("w440 h180")

    ; Make dialog modal
    WinWaitClose("ahk_id " setupGui.Hwnd)

    ; Check if a camera was selected and saved
    configFile := A_ScriptDir "\WinCBT-Biometric.ini"
    ; Fall back to old config file if new one doesn't exist
    if (!FileExist(configFile)) {
        configFile := A_ScriptDir "\config.ini"
        if (!FileExist(configFile))
            return false
    }

    ; Check the new [Camera] section first
    cameraName := IniRead(configFile, "Camera", "CameraName", "")
    if (cameraName != "")
        return true

    ; For backward compatibility, also check the old section
    return IniRead(configFile, "BiometricDevices", "Camera", "") != ""
}
