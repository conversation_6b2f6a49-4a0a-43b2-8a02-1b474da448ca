#Requires AutoHotkey v2.0

; CSV Import functionality for WinCBT-Biometric
; This module handles importing candidate data from CSV files

; Define paths directly without including db_functions.ahk
global CSV_DB_PATH := IniRead(A_ScriptDir "\WinCBT-Biometric.ini", "Paths", "dbPath", "db")
; Convert relative to absolute path if needed
if (SubStr(CSV_DB_PATH, 1, 1) != "\" && SubStr(CSV_DB_PATH, 2, 1) != ":") {
    CSV_DB_PATH := A_ScriptDir "\" CSV_DB_PATH "\"
} else if (SubStr(CSV_DB_PATH, -1) != "\") {
    CSV_DB_PATH := CSV_DB_PATH "\"
}

; Define database file paths using the CSV_DB_PATH
global CSV_CANDIDATES_PATH := CSV_DB_PATH "candidates.ini"
global CSV_CANDIDATES_IMG_PATH := CSV_DB_PATH "img\candidates\"
global CSV_FINGERPRINT_PATH := CSV_DB_PATH "fpt\"

; Helper function to join array elements with a delimiter
StrJoin(array, delimiter) {
    result := ""
    for i, item in array {
        if (i > 1) {
            result .= delimiter
        }
        result .= item
    }
    return result
}

; Function to parse various date formats
DateParse(dateStr) {
    ; Try common date formats
    formats := [
        "yyyy-MM-dd",
        "MM/dd/yyyy",
        "dd/MM/yyyy",
        "yyyy/MM/dd",
        "MMM dd, yyyy",
        "dd MMM yyyy",
        "MMMM dd, yyyy",
        "dd MMMM yyyy",
        "ddMMyyyy"
    ]

    ; Try to parse with each format
    for format in formats {
        try {
            date := ParseDate(dateStr, format)
            if date
                return date
        } catch {
            continue
        }
    }

    return ""
}

; Function to parse date with specific format
ParseDate(dateStr, format) {
    ; Simple implementation for common formats
    if (format = "ddMMyyyy") {
        if (StrLen(dateStr) = 8 && RegExMatch(dateStr, "^\d{8}$")) {
            day := SubStr(dateStr, 1, 2)
            month := SubStr(dateStr, 3, 2)
            year := SubStr(dateStr, 5, 4)
            return {day: day, month: month, year: year}
        }
    }

    ; For other formats, just check if it looks like a date
    if (RegExMatch(dateStr, "^\d{1,2}[/-]\d{1,2}[/-]\d{2,4}$") ||
        RegExMatch(dateStr, "^\d{4}[/-]\d{1,2}[/-]\d{1,2}$")) {
        return {valid: true}
    }

    return ""
}

; Create CSV Importer class
class CSVImporter {
    ; Configuration
    importDir := A_ScriptDir "\import"
    tempDir := A_ScriptDir "\temp"
    candidatesPath := ""

    ; Required fields for candidate import
    requiredFields := ["RollNumber", "Name"]

    ; Field mappings for CSV import
    defaultFieldMappings := Map(
        "RollNumber", "RollNumber",
        "Name", "Name",
        "FatherName", "FatherName",
        "DateOfBirth", "DOB",
        "Email", "Email",
        "Mobile", "Mobile",
        "Gender", "Gender",
        "CenterID", "CenterID",
        "ExamID", "ExamID",
        "StudentID", "StudentID",
        "Status", "Status",
        "Special", "Special"
    )

    ; Constructor - Initialize directories
    __New() {
        ; Create directories if they don't exist
        for dir in [this.importDir, this.tempDir] {
            if (!DirExist(dir)) {
                DirCreate(dir)
            }
        }

        ; Set the candidates path from the global variable
        global CSV_CANDIDATES_PATH
        this.candidatesPath := CSV_CANDIDATES_PATH
    }

    ; Import candidates from CSV file
    ImportCandidatesFromCSV(csvFile, fieldMappings := "", options := "") {
        ; Initialize results
        results := {
            totalRows: 0,
            imported: 0,
            skipped: 0,
            errors: [],
            validationErrors: Map()
        }

        ; Set default options
        if (!IsObject(options)) {
            options := {}
        }
        options.overwrite := options.HasOwnProp("overwrite") ? options.overwrite : false
        options.validateOnly := options.HasOwnProp("validateOnly") ? options.validateOnly : false
        options.delimiter := options.HasOwnProp("delimiter") ? options.delimiter : ","

        ; Use default field mappings if not provided
        if (!IsObject(fieldMappings) || !fieldMappings.Count) {
            fieldMappings := this.defaultFieldMappings.Clone()
        }

        ; Check if file exists
        if (!FileExist(csvFile)) {
            results.errors.Push("File not found: " csvFile)
            return results
        }

        try {
            ; Read CSV file
            fileContent := FileRead(csvFile)

            ; Split into lines
            lines := StrSplit(fileContent, "`n", "`r")

            ; Get header line
            if (lines.Length < 2) {
                results.errors.Push("CSV file must have at least a header row and one data row")
                return results
            }

            ; Parse header
            headerLine := lines[1]
            headers := StrSplit(headerLine, options.delimiter)

            ; Validate required fields
            missingFields := []
            for field in this.requiredFields {
                fieldFound := false
                for mappedField, originalField in fieldMappings {
                    for i, header in headers {
                        if (header = originalField) {
                            fieldFound := true
                            break
                        }
                    }
                    if (fieldFound) {
                        break
                    }
                }
                if (!fieldFound) {
                    missingFields.Push(field)
                }
            }

            if (missingFields.Length > 0) {
                results.errors.Push("Missing required fields: " StrJoin(missingFields, ", "))
                return results
            }

            ; Process data rows
            for i, line in lines {
                ; Skip header row
                if (i = 1 || Trim(line) = "") {
                    continue
                }

                results.totalRows++

                ; Parse line
                fields := StrSplit(line, options.delimiter)

                ; Create candidate data object
                candidateData := {}

                ; Map fields
                for j, header in headers {
                    if (j <= fields.Length) {
                        ; Find the internal field name for this header
                        for mappedField, originalField in fieldMappings {
                            if (header = originalField) {
                                candidateData[mappedField] := fields[j]
                                break
                            }
                        }
                    }
                }

                ; Validate candidate data
                validationErrors := this.ValidateCandidateData(candidateData)

                ; Store validation errors
                if (validationErrors.Length > 0) {
                    results.validationErrors[candidateData.RollNumber] := validationErrors
                    results.skipped++
                    continue
                }

                ; Skip validation-only mode
                if (options.validateOnly) {
                    continue
                }

                ; Check if candidate already exists
                candidateExists := this.CandidateExists(candidateData.RollNumber)

                ; Skip if candidate exists and overwrite is false
                if (candidateExists && !options.overwrite) {
                    results.skipped++
                    continue
                }

                ; Import candidate
                try {
                    this.ImportCandidate(candidateData)
                    results.imported++
                } catch Error as e {
                    results.errors.Push("Error importing candidate " candidateData.RollNumber ": " e.Message)
                    results.skipped++
                }
            }

            return results
        } catch Error as e {
            results.errors.Push("Error processing CSV file: " e.Message)
            return results
        }
    }

    ; Validate candidate data
    ValidateCandidateData(candidateData) {
        errors := []

        ; Check required fields
        for field in this.requiredFields {
            if (!candidateData.HasOwnProp(field) || candidateData[field] = "") {
                errors.Push("Missing required field: " field)
            }
        }

        ; Validate roll number format (alphanumeric)
        if (candidateData.HasOwnProp("RollNumber") && candidateData.RollNumber != "") {
            if (!RegExMatch(candidateData.RollNumber, "^[A-Za-z0-9]+$")) {
                errors.Push("Roll Number must be alphanumeric")
            }
        }

        ; Validate date of birth format if present
        if (candidateData.HasOwnProp("DateOfBirth") && candidateData.DateOfBirth != "") {
            ; Try to parse the date
            parsedDate := DateParse(candidateData.DateOfBirth)
            if (!parsedDate) {
                errors.Push("Invalid Date of Birth format")
            }
        }

        ; Validate Special field if present (must be 0 or 1)
        if (candidateData.HasOwnProp("Special") && candidateData.Special != "") {
            if (candidateData.Special != "0" && candidateData.Special != "1") {
                errors.Push("Special field must be 0 or 1")
            }
        }

        return errors
    }

    ; Check if candidate exists
    CandidateExists(rollNumber) {
        try {
            name := IniRead(this.candidatesPath, rollNumber, "Name", "")
            return name != ""
        } catch {
            return false
        }
    }

    ; Import a single candidate
    ImportCandidate(candidateData) {
        ; Ensure roll number exists
        if (!candidateData.HasOwnProp("RollNumber") || candidateData.RollNumber = "") {
            throw Error("Roll Number is required")
        }

        ; Write candidate data to INI file
        for field, value in candidateData.OwnProps() {
            IniWrite(value, this.candidatesPath, candidateData.RollNumber, field)
        }

        ; Set default values for missing fields
        defaultFields := Map(
            "Status", "Active",
            "PhotoStatus", "",
            "BiometricStatus", "",
            "FingerprintStatus", "",
            "RightFingerprintStatus", "",
            "SignatureStatus", ""
        )

        for field, defaultValue in defaultFields {
            if (!candidateData.HasOwnProp(field)) {
                IniWrite(defaultValue, this.candidatesPath, candidateData.RollNumber, field)
            }
        }
    }
}
