@import url('content.css');

body {
background:#fff;
color:#000;
margin:.72em;
padding:0px;
counter-reset: ex_number;
}

html {
font:.9em/1.6 Arial,sans-serif;
margin:0px;
padding:0px
}

h1 {
font-size:2em;
font-weight:700;
border-bottom-color:#FFF;
border-bottom-width:2px;
margin-top:8px;
color:#3F5770;
word-wrap:break-word;
overflow-wrap:break-word
}

h2 {
margin-top:1.1em;
margin-bottom:.5em;
color:#A04040
}

h3 {
margin-top:1.5em;
margin-bottom:.5em;
color:#080
}

h4.func_section {
margin-bottom: -.2em;
margin-top: -.2em;
font-weight: normal;
color: #A04040;
border-bottom: 1px solid #eee;
}

h2:first-child {
margin-top:0
}

h1,h2,h3,h4 {
border-bottom:1px solid #ddd;
counter-reset: ex_number;
}

a {
text-decoration:none
}

a:link,a:visited,a:visited:hover {
color:#0148c2
}

a:hover,a:active {
text-decoration:underline
}

hr {
border-color: #e0e0e0;
background-color: #e0e0e0;
height: 2px;
border: 0;
}

img {
max-width:100%;
height:auto;
width:auto\9; /*IE8*/
border:none
}

#ahklogo {
margin:20px 0
}

/* pre: code blocks, code: inline code, .Syntax: syntax definition (block/pre or inline/span) */

pre,code {
background-color:#eff0f1
}

pre,code,table.info .code {
font-family:Consolas,Monaco,Courier New,monospace
}

.Syntax {
background-color:#FFA;
border:solid 1px #E8E89A
}

.Syntax span.func {
font-weight:700;
color:#605428
}

.Syntax span.optional:before {
content:"[";
color:orange;
font:130% serif;
line-height:0
}

.Syntax span.optional:after {
content:"]";
color:orange;
font:130% serif;
line-height:0
}

code {
padding: 0 .2em;
line-height: normal;
white-space: pre-wrap;
word-break: break-word;
}

span.Syntax {
padding:0 1px
}

pre {
line-height:120%;
margin:1em 1.5em;
padding:.7em 0 .7em .7em;
white-space:pre-wrap; /* works in IE8 but apparently not CHM viewer */
word-wrap:break-word; /* works in CHM viewer */
word-break:break-word; /* Chrome: breaks long words in tables */
tab-size:4
}

pre.Syntax {
margin:1em 0 1em;
line-height:150%
}

.NoIndent {
margin-left:0
}

/* comments */

pre em,code em {
color:green;
font-style:normal
}

/* tables */

table.info {
margin-top:1em;
margin-bottom:1em;
border-collapse:collapse;
width:100%
}

table.info td {
border:solid 1px silver;
padding: .3em .5em;
}

table.info td *:first-child {
margin-top:0
}

table.info td *:last-child {
margin-bottom:0
}

table.info th {
border: solid 1px silver;
background-color:#F6F6F6;
padding: 0 .5em;
text-align: left;
white-space: nowrap;
}

table.info tr.sep_below {
border-bottom: 3px solid silver;
}

table.info .center {
text-align: center;
}

table.info .right {
text-align: right;
}

table.info .wrap {
white-space: normal;
}

table.info .bold {
font-weight: bold;
}

/* heading note / version number/requirement tag */

h1 .headnote,h2 .headnote,h3 .headnote,h1 .ver,h2 .ver,h3 .ver {
font-size:65%;
font-weight:400;
margin-left:1em
}

h4 .headnote,h4 .ver,dt .ver {
font-weight:400
}

.headnote,.ver,.dull {
color:gray
}

a.ver:hover,a.ver:focus {
text-decoration:none
}

/* emphases */

.red {
color:red
}

.blue {
color:blue
}

.note {
border-left:2px solid #9B9;
background-color:#E6FFE6;
color:#050;
padding:.5em 1em
}

.warning {
border-left:2px solid #FFA000;
background-color:#FFF8E6;
color:#C05500;
padding:.5em 1em
}

blockquote {
border-left:2px solid #4080f0;
background-color:#f8f8f8;
margin:.7em 1.5em;
padding:.7em 0 .7em .7em;
}

/* styles for briefly documenting multiple methods on one page: */

.methodShort {
border:solid thin silver;
padding:.5em;
margin-bottom:1em
}

.methodShort h2, .methodShort h3 {
margin-top: 0;
color: inherit;
border-bottom: 0;
}

.methodShort h4 {
margin-bottom: -.1em;
margin-top: -.1em;
}

.methodShort table.info {
border:none
}

.methodShort > table.info td {
border:none;
vertical-align:text-top
}

.methodShort:target { /* non-essential, but helpful if it works */
border-color: inherit;
}

/* function parameters */

dt { /* param name */
color:#3F5770;
margin-left:.5em;
font-weight:bold
}

dd {
margin-left:1.5em;
padding-left:1em;
border-left:.3em solid #e0e0e0;
margin-bottom:1em
}

dd > p:first-child {
margin-top:.3em
}

/* keyboard keys */

kbd {
border:1px solid #ccc;
border-radius:3px;
box-shadow:0 1px 0 rgba(0, 0, 0, 0.2), 0 0 0 2px #fff inset;
color:#333;
background-color: #fff;
display:inline-block;
font-family:Consolas,Monaco,Courier New,monospace;
line-height: 1;
padding: 2px 4px;
text-shadow:0 1px 0 #fff;
white-space:nowrap
}

/* example frames */

.ex {
border:1px solid #d1d1d1;
margin-bottom:0.7em
}

.ex .ex_number:before {
counter-increment: ex_number;
content: "#" counter(ex_number) ":";
}

.ex pre {
margin:.2em
}

.ex pre+pre {
margin-top:0.7em
}

.ex p {
margin:0.2em 0.2em
}

/* RegEx */

.regex {
background-color:#FFEED3;
font-weight:bold;
font-family: Consolas,Monaco,Courier New,monospace;
word-break: break-word;
}

.subj {
background-color:#DDFFDD
}

/* indented list */

ul.indent {
list-style-type: none;
padding-left: 0;
}

ul.indent ul {
list-style-type: disc;
}
