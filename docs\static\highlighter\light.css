@import url('highlighter.css');

.highlight {
  color: #001080;
}

.highlight>.cfs,
.highlight>.dir {
  color: #af00db;
}

.highlight>.str {
  color: #a31515;
}

.highlight .str>.esc {
  color: #ee0000;
}

.highlight .hot>.esc {
  color: #4b00ea;
}

.highlight .lab>.esc,
.highlight .opt>.esc {
  color: #862626;
}

.highlight>.biv,
.highlight>.dec,
.highlight>.hot {
  color: #0000ff;
}

.highlight>.cls {
  color: #267f99;
}

.highlight>.cmd,
.highlight>.fun,
.highlight>.met {
  color: #795e26;
}

.highlight>.num {
  color: #098658;
}

.highlight>.opr,
.highlight>.lab,
.highlight>.opt {
  color: #3b3b3b;
}

.highlight .cmt {
  color: #008000;
}

.highlight.line-numbers .line-numbers-rows {
  border-right-color: #999;
}

.highlight.line-numbers .line-numbers-rows>span:before {
  color: #999;
}
