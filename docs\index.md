[![AutoHotkey](static/ahk_logo.svg){onerror="this.src='static/ahk_logo.png'; this.onerror=null;"}](https://www.autohotkey.com){#ahklogo
target="_blank"}

# Version [2.0.19](ChangeLog.htm)

<https://www.autohotkey.com>

© 2014 <PERSON>, <PERSON>, portions © [AutoIt
Team](https://www.autoitscript.com/) and various others

Software License: [GNU General Public License](license.htm)

## Quick Reference {#Quick_Reference}

Getting started:

- [How to use the program](Program.htm)
- Tutorials:
  - [How to run example code](howto/RunExamples.htm)
  - [How to write hotkeys](howto/WriteHotkeys.htm)
  - [How to send keystrokes](howto/SendKeys.htm)
  - [How to run programs](howto/RunPrograms.htm)
  - [How to manage windows](howto/ManageWindows.htm)
  - [Beginner tutorial by tidbit](Tutorial.htm)
- [Text editors with AutoHotkey support](misc/Editors.htm)
- [Frequently asked questions](FAQ.htm)

Scripts:

- [Concepts and conventions](Concepts.htm): explanations of various
  things you need to know.
- [Scripting language](Language.htm): how to write scripts.
- [Miscellaneous topics](Scripts.htm)
- [List of built-in functions](lib/index.htm)
- [Variables and expressions](Variables.htm)
- [How to use functions](Functions.htm)
- [Objects](Objects.htm)
- [Interactive debugging](Scripts.htm#idebug)

Keyboard and mouse:

- [Hotkeys (mouse, controller and keyboard shortcuts)](Hotkeys.htm)
- [Hotstrings and auto-replace](Hotstrings.htm)
- [Remapping keys and buttons](misc/Remap.htm)
- [List of keys, mouse buttons and controller controls](KeyList.htm)

Other:

- [DllCall](lib/DllCall.htm)
- [RegEx quick reference](misc/RegEx-QuickRef.htm)

## Acknowledgements {#Acknowledgements}

A special thanks to Jonathan Bennett, whose generosity in releasing
AutoIt v2 as free software in 1999 served as an inspiration and
time-saver for myself and many others worldwide. In addition, many of
AutoHotkey\'s enhancements to the AutoIt v2 command set, as well as the
Window Spy and the old script compiler, were adapted directly from the
AutoIt v3 source code. So thanks to Jon and the other AutoIt authors for
those as well.

Finally, AutoHotkey would not be what it is today without [these other
individuals](misc/Acknowledgements.htm).

*\~ Chris Mallett*
