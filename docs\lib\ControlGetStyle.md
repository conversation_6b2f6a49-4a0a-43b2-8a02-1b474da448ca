# ControlGetStyle / ControlGetExStyle

Returns an integer representing the style or extended style of the
specified control.

``` Syntax
Style := ControlGetStyle(Control , WinTitle, WinText, ExcludeTitle, ExcludeText)
ExStyle := ControlGetExStyle(Control , WinTitle, WinText, ExcludeTitle, ExcludeText)
```

## Parameters {#Parameters}

Control

:   Type: [String](../Concepts.htm#strings),
    [Integer](../Concepts.htm#numbers) or
    [Object](../Concepts.htm#objects)

    The control\'s ClassNN, text or HWND, or an object with a
    `Hwnd`{.no-highlight} property. For details, see [The Control
    Parameter](Control.htm#Parameter).

WinTitle, WinText, ExcludeTitle, ExcludeText

:   Type: [String](../Concepts.htm#strings),
    [Integer](../Concepts.htm#numbers) or
    [Object](../Concepts.htm#objects)

    If each of these is blank or omitted, the [Last Found
    Window](../misc/WinTitle.htm#LastFoundWindow) will be used.
    Otherwise, specify for *WinTitle* a [window title or other
    criteria](../misc/WinTitle.htm) to identify the target window and/or
    for *WinText* a substring from a single text element of the target
    window (as revealed by the included Window Spy utility).

    *ExcludeTitle* and *ExcludeText* can be used to exclude one or more
    windows by their title or text. Their specification is similar to
    *WinTitle* and *WinText*, except that *ExcludeTitle* does not
    recognize any criteria other than the window title.

    Window titles and text are case-sensitive. By default, hidden
    windows are not detected and hidden text elements are detected,
    unless changed with [DetectHiddenWindows](DetectHiddenWindows.htm)
    and [DetectHiddenText](DetectHiddenText.htm); however, when using
    [pure HWNDs](../misc/WinTitle.htm#ahk_id), hidden windows are always
    detected regardless of DetectHiddenWindows. By default, a window
    title can contain *WinTitle* or *ExcludeTitle* anywhere inside it to
    be a match, unless changed with
    [SetTitleMatchMode](SetTitleMatchMode.htm).

## Return Value {#Return_Value}

Type: [Integer](../Concepts.htm#numbers)

These functions return the style or extended style of the specified
control.

## Error Handling {#Error_Handling}

A [TargetError](Error.htm#TargetError) is thrown if the window or
control could not be found.

## Remarks {#Remarks}

See the [styles table](../misc/Styles.htm) for a partial listing of
styles.

ControlGetExStyle only retrieves generic extended styles, such as
WS_EX_CLIENTEDGE (0x200). To retrieve control-specific extended styles,
use [SendMessage](SendMessage.htm), e.g.
`SendMessage(0x1037, 0, 0, `*`MyListView`*`)` where 0x1037 is
[LVM_GETEXTENDEDLISTVIEWSTYLE](https://learn.microsoft.com/windows/win32/controls/lvm-getextendedlistviewstyle).
For a [ListView](ListView.htm) created via AutoHotkey, this example
returns 48 (0x30), a combination of
[LVS_EX_FULLROWSELECT](../misc/Styles.htm#LVS_EX_FULLROWSELECT) (0x20)
and [LVS_EX_HEADERDRAGDROP](../misc/Styles.htm#LVS_EX_HEADERDRAGDROP)
(0x10), provided the extended ListView styles have not been changed
beforehand.

## Related {#Related}

[ControlSetStyle / ControlSetExStyle](ControlSetStyle.htm), [WinGetStyle
/ WinGetExStyle](WinGetStyle.htm), [styles table](../misc/Styles.htm),
[Control functions](Control.htm)
