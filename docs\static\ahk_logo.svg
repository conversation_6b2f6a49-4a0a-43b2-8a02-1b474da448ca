<?xml version="1.0" encoding="UTF-8"?>
<svg width="520.72" height="83.877" version="1.1" viewBox="0 0 137.78 22.192" xmlns="http://www.w3.org/2000/svg" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink">
<title>AutoHotkey modern logo</title>
<defs>
<linearGradient id="a">
<stop stop-color="#cfd0d1" offset="0"/>
<stop stop-color="#dbdbdb" offset="1"/>
</linearGradient>
<linearGradient id="b">
<stop stop-color="#f4f5f7" offset="0"/>
<stop stop-color="#e9eaeb" offset="1"/>
</linearGradient>
<linearGradient id="k" x1="4.5837" x2="12.759" y1="11.944" y2="11.944" gradientUnits="userSpaceOnUse" xlink:href="#b" spreadMethod="reflect"/>
<linearGradient id="f" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(.055219 0 0 -.055033 64.547 43.998)" gradientUnits="userSpaceOnUse" xlink:href="#b">
<stop stop-color="#cfd0d1" offset="0"/>
<stop stop-color="#b7b7b7" offset="1"/>
</linearGradient>
<linearGradient id="e" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(-.055219 0 0 -.055033 -38.079 44.048)" gradientUnits="userSpaceOnUse" xlink:href="#b">
<stop stop-color="#cfd0d1" offset="0"/>
<stop stop-color="#b7b7b7" offset="1"/>
</linearGradient>
<linearGradient id="h" x1="3.5892" x2="5.4736" y1="17.781" y2="5.3909" gradientTransform="matrix(.77624 0 0 1.0263 .80313 -.14201)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
<linearGradient id="g" x1="3.5892" x2="5.4736" y1="17.781" y2="5.3909" gradientTransform="matrix(1.0652 0 0 1 -26.701 .029204)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
<linearGradient id="c" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(.055219 0 0 .055033 64.547 -19.403)" gradientUnits="userSpaceOnUse" xlink:href="#b">
<stop stop-color="#dbdbdb" offset="0"/>
<stop stop-color="#f3f3f3" offset="1"/>
</linearGradient>
<linearGradient id="d" x1="-1076.5" x2="-1060.2" y1="448.73" y2="425.19" gradientTransform="matrix(-.055219 0 0 .055033 -38.079 -19.453)" gradientUnits="userSpaceOnUse">
<stop stop-color="#dbdbdb" offset="0"/>
<stop stop-color="#f3f3f3" offset="1"/>
</linearGradient>
<linearGradient id="aa" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(.055219 0 0 -.055033 85.7 43.998)" gradientUnits="userSpaceOnUse" xlink:href="#f"/>
<linearGradient id="y" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(-.055219 0 0 -.055033 -16.902 44.048)" gradientUnits="userSpaceOnUse" xlink:href="#e"/>
<linearGradient id="w" x1="3.5892" x2="5.4736" y1="17.781" y2="5.3909" gradientTransform="matrix(.90071 0 0 1.0454 21.51 -.33146)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
<linearGradient id="u" x1="3.5892" x2="5.4736" y1="17.781" y2="5.3909" gradientTransform="matrix(1.0652 0 0 1 -47.876 -.028359)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
<linearGradient id="s" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(.055219 0 0 .055033 85.7 -19.403)" gradientUnits="userSpaceOnUse" xlink:href="#c"/>
<linearGradient id="q" x1="-1076.5" x2="-1060.2" y1="448.73" y2="425.19" gradientTransform="matrix(-.055219 0 0 .055033 -16.905 -19.453)" gradientUnits="userSpaceOnUse" xlink:href="#d"/>
<linearGradient id="j" x1="4.5837" x2="12.759" y1="11.944" y2="11.944" gradientTransform="matrix(1.0035 0 0 1 21.586 -3.1104e-7)" gradientUnits="userSpaceOnUse" xlink:href="#b" spreadMethod="reflect"/>
<linearGradient id="i" x1="4.5837" x2="12.759" y1="11.944" y2="11.944" gradientTransform="matrix(1.0034 0 0 1 43.022 -1.1038e-8)" gradientUnits="userSpaceOnUse" xlink:href="#b" spreadMethod="reflect"/>
<linearGradient id="v" x1="3.5892" x2="5.4736" y1="17.781" y2="5.3909" gradientTransform="matrix(.92553 0 0 1.0347 42.583 -.2739)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
<linearGradient id="r" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(.055219 0 0 .055033 106.86 -19.403)" gradientUnits="userSpaceOnUse" xlink:href="#c"/>
<linearGradient id="p" x1="-1076.5" x2="-1060.2" y1="448.73" y2="425.19" gradientTransform="matrix(-.055219 0 0 .055033 4.2582 -19.453)" gradientUnits="userSpaceOnUse" xlink:href="#d"/>
<linearGradient id="t" x1="3.5892" x2="5.4736" y1="17.781" y2="5.3909" gradientTransform="matrix(1.0652 0 0 1 -69.039 -.028359)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
<linearGradient id="x" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(-.055219 0 0 -.055033 4.2603 44.048)" gradientUnits="userSpaceOnUse" xlink:href="#e"/>
<linearGradient id="z" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(.055219 0 0 -.055033 106.86 43.998)" gradientUnits="userSpaceOnUse" xlink:href="#f"/>
<linearGradient id="o" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(.055219 0 0 -.055033 64.547 43.998)" gradientUnits="userSpaceOnUse" xlink:href="#f"/>
<linearGradient id="n" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(-.055219 0 0 -.055033 -38.079 44.048)" gradientUnits="userSpaceOnUse" xlink:href="#e"/>
<linearGradient id="m" x1="-1084.9" x2="-1062" y1="454.41" y2="438.75" gradientTransform="matrix(.055219 0 0 .055033 64.547 -19.403)" gradientUnits="userSpaceOnUse" xlink:href="#c"/>
<linearGradient id="l" x1="-1076.5" x2="-1060.2" y1="448.73" y2="425.19" gradientTransform="matrix(-.055219 0 0 .055033 -38.079 -19.453)" gradientUnits="userSpaceOnUse" xlink:href="#d"/>
<filter id="ad" color-interpolation-filters="sRGB">
<feFlood flood-color="rgb(0,0,0)" result="flood"/>
<feComposite in="flood" in2="SourceGraphic" operator="in" result="composite1"/>
<feGaussianBlur in="composite1" result="blur" stdDeviation="0.4"/>
<feOffset dx="0" dy="0.5" result="offset"/>
<feComposite in="offset" in2="offset" operator="atop" result="composite2"/>
</filter>
<filter id="ac" color-interpolation-filters="sRGB">
<feFlood flood-color="rgb(0,0,0)" result="flood"/>
<feComposite in="flood" in2="SourceGraphic" operator="in" result="composite1"/>
<feGaussianBlur in="composite1" result="blur" stdDeviation="0.4"/>
<feOffset dx="0" dy="0.5" result="offset"/>
<feComposite in="offset" in2="offset" operator="atop" result="composite2"/>
</filter>
<filter id="ab" color-interpolation-filters="sRGB">
<feFlood flood-color="rgb(0,0,0)" result="flood"/>
<feComposite in="flood" in2="SourceGraphic" operator="in" result="composite1"/>
<feGaussianBlur in="composite1" result="blur" stdDeviation="0.4"/>
<feOffset dx="0" dy="0.5" result="offset"/>
<feComposite in="offset" in2="offset" operator="atop" result="composite2"/>
</filter>
</defs>
<metadata>
<rdf:RDF>
<cc:Work rdf:about="">
<dc:format>image/svg+xml</dc:format>
<dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
<dc:title>AutoHotkey modern logo</dc:title>
<dc:description>Unfont: fonts converted to paths. Recreation of the Modern logo for AutoHotkey,
fonts used are Myriad Pro 12 pt and 36 pt, and Helvetica 16 pt.</dc:description>
<dc:creator>
<cc:Agent>
<dc:title>joedf</dc:title>
</cc:Agent>
</dc:creator>
<dc:date>July 27th 2020</dc:date>
<dc:contributor>
<cc:Agent>
<dc:title>Author unknown for original logo</dc:title>
</cc:Agent>
</dc:contributor>
<cc:license rdf:resource=""/>
</cc:Work>
</rdf:RDF>
</metadata>
<g transform="translate(-1.6603 -1.2012)" display="none" fill="#f0f">
<rect x="3.5892" y="3.0506" width="19.289" height="18.494" ry="1.5066" display="inline" opacity=".6" style="paint-order:stroke fill markers"/>
<rect x="24.742" y="3.0506" width="19.311" height="18.494" ry="1.5066" display="inline" opacity=".6" style="paint-order:stroke fill markers"/>
<rect x="45.905" y="3.0506" width="19.313" height="18.494" ry="1.5066" display="inline" opacity=".6" style="paint-order:stroke fill markers"/>
</g>
<g transform="translate(-1.6603 -1.2012)">
<g fill="#3f5770" stroke-width=".26458" aria-label="AutoHotkey">
<path d="m80.059 11.307 0.9144 2.6924h1.1811l-2.921-8.5598h-1.3335l-2.9083 8.5598h1.143l0.889-2.6924zm-2.8067-0.8636 0.8382-2.4765c0.1651-0.5207 0.3048-1.0414 0.4318-1.5494h0.0254c0.127 0.4953 0.254 1.0033 0.4445 1.5621l0.8382 2.4638z"/>
<path d="m88.505 7.8525h-1.1176v3.7719c0 0.2032-0.0381 0.4064-0.1016 0.5715-0.2032 0.4953-0.7239 1.016-1.4732 1.016-1.016 0-1.3716-0.7874-1.3716-1.9558v-3.4036h-1.1176v3.5941c0 2.159 1.1557 2.6924 2.1209 2.6924 1.0922 0 1.7399-0.6477 2.032-1.143h0.0254l0.0635 1.0033h0.9906c-0.0381-0.4826-0.0508-1.0414-0.0508-1.6764z"/>
<path d="m90.756 6.7222v1.1303h-0.9525v0.8509h0.9525v3.3528c0 0.7239 0.1143 1.27 0.4318 1.6002 0.2667 0.3048 0.6858 0.4826 1.2065 0.4826 0.4318 0 0.7747-0.0762 0.9906-0.1651l-0.0508-0.8382c-0.1651 0.0508-0.3429 0.0762-0.6477 0.0762-0.6223 0-0.8382-0.4318-0.8382-1.1938v-3.3147h1.6002v-0.8509h-1.6002v-1.4732z"/>
<path d="m97.369 7.7128c-1.7018 0-3.048 1.2065-3.048 3.2639 0 1.9431 1.2827 3.1623 2.9464 3.1623 1.4859 0 3.0607-0.9906 3.0607-3.2639 0-1.8796-1.1938-3.1623-2.9591-3.1623zm-0.0254 0.8382c1.3208 0 1.8415 1.3208 1.8415 2.3622 0 1.3843-0.8001 2.3876-1.8669 2.3876-1.0922 0-1.8669-1.016-1.8669-2.3622 0-1.1684 0.5715-2.3876 1.8923-2.3876z"/>
<path d="m101.92 5.4395v8.5598h1.1049v-4.0132h4.1402v4.0132h1.1176v-8.5598h-1.1176v3.5814h-4.1402v-3.5814z"/>
<path d="m112.91 7.7128c-1.7018 0-3.048 1.2065-3.048 3.2639 0 1.9431 1.2827 3.1623 2.9464 3.1623 1.4859 0 3.0607-0.9906 3.0607-3.2639 0-1.8796-1.1938-3.1623-2.9591-3.1623zm-0.0254 0.8382c1.3208 0 1.8415 1.3208 1.8415 2.3622 0 1.3843-0.8001 2.3876-1.8669 2.3876-1.0922 0-1.8669-1.016-1.8669-2.3622 0-1.1684 0.5715-2.3876 1.8923-2.3876z"/>
<path d="m117.68 6.7222v1.1303h-0.9525v0.8509h0.9525v3.3528c0 0.7239 0.1143 1.27 0.4318 1.6002 0.2667 0.3048 0.6858 0.4826 1.2065 0.4826 0.4318 0 0.7747-0.0762 0.9906-0.1651l-0.0508-0.8382c-0.1651 0.0508-0.3429 0.0762-0.6477 0.0762-0.6223 0-0.8382-0.4318-0.8382-1.1938v-3.3147h1.6002v-0.8509h-1.6002v-1.4732z"/>
<path d="m122.87 4.9823h-1.1049v9.017h1.1049v-2.3114l0.5715-0.635 2.1209 2.9464h1.3589l-2.7051-3.6195 2.3749-2.5273h-1.3462l-1.8034 2.1209c-0.1778 0.2159-0.3937 0.4826-0.5461 0.6985h-0.0254z"/>
<path d="m132.8 11.129c0.0127-0.1143 0.0381-0.2921 0.0381-0.5207 0-1.1303-0.5334-2.8956-2.54-2.8956-1.7907 0-2.8829 1.4605-2.8829 3.3147 0 1.8542 1.1303 3.1115 3.0226 3.1115 0.9779 0 1.651-0.2159 2.0447-0.3937l-0.1905-0.8001c-0.4191 0.1778-0.9017 0.3175-1.7018 0.3175-1.1176 0-2.0828-0.6223-2.1082-2.1336zm-4.3053-0.8001c0.0889-0.7747 0.5842-1.8161 1.7145-1.8161 1.2573 0 1.5621 1.1049 1.5494 1.8161z"/>
<path d="m133.57 7.8525 2.2733 5.6642c0.0508 0.1397 0.0762 0.2286 0.0762 0.2921s-0.0381 0.1524-0.0889 0.2667c-0.254 0.5715-0.635 1.0033-0.9398 1.2446-0.3302 0.2794-0.6985 0.4572-0.9779 0.5461l0.2794 0.9398c0.2794-0.0508 0.8255-0.2413 1.3716-0.7239 0.762-0.6604 1.3081-1.7399 2.1082-3.8481l1.6764-4.3815h-1.1811l-1.2192 3.6068c-0.1524 0.4445-0.2794 0.9144-0.3937 1.2827h-0.0254c-0.1016-0.3683-0.254-0.8509-0.3937-1.2573l-1.3462-3.6322z"/>
</g>
<g fill="#6b6b6b" stroke-width=".26458" aria-label="Automation. Hotkeys. Scripting.">
<path d="m77.355 18.794 0.3048 0.89747h0.3937l-0.97367-2.8533h-0.4445l-0.96943 2.8533h0.381l0.29633-0.89747zm-0.93557-0.28787 0.2794-0.8255c0.05503-0.17357 0.1016-0.34713 0.14393-0.51647h0.0085c0.04233 0.1651 0.08467 0.33443 0.14817 0.5207l0.2794 0.82127z"/>
<path d="m80.196 17.642h-0.37253v1.2573c0 0.06773-0.0127 0.13547-0.03387 0.1905-0.06773 0.1651-0.2413 0.33867-0.49107 0.33867-0.33867 0-0.4572-0.26247-0.4572-0.65193v-1.1345h-0.37253v1.198c0 0.71967 0.38523 0.89747 0.70697 0.89747 0.36407 0 0.57997-0.2159 0.67733-0.381h0.0085l0.02117 0.33443h0.3302c-0.0127-0.16087-0.01693-0.34713-0.01693-0.5588z"/>
<path d="m80.972 17.266v0.37677h-0.3175v0.28363h0.3175v1.1176c0 0.2413 0.0381 0.42333 0.14393 0.5334 0.0889 0.1016 0.2286 0.16087 0.40217 0.16087 0.14393 0 0.25823-0.0254 0.3302-0.05503l-0.01693-0.2794c-0.05503 0.01693-0.1143 0.0254-0.2159 0.0254-0.20743 0-0.2794-0.14393-0.2794-0.39793v-1.1049h0.5334v-0.28364h-0.5334v-0.49107z"/>
<path d="m83.202 17.596c-0.56727 0-1.016 0.40217-1.016 1.088 0 0.6477 0.42757 1.0541 0.98213 1.0541 0.4953 0 1.0202-0.3302 1.0202-1.088 0-0.62653-0.39793-1.0541-0.98637-1.0541zm-0.0085 0.2794c0.44027 0 0.61383 0.44027 0.61383 0.7874 0 0.46143-0.2667 0.79587-0.6223 0.79587-0.36407 0-0.6223-0.33867-0.6223-0.7874 0-0.38947 0.1905-0.79587 0.63077-0.79587z"/>
<path d="m84.732 19.691h0.36407v-1.2361c0-0.0635 0.0085-0.127 0.02963-0.18203 0.05927-0.18627 0.2286-0.37253 0.46143-0.37253 0.28363 0 0.42757 0.23707 0.42757 0.56303v1.2277h0.36407v-1.2658c0-0.06773 0.0127-0.13547 0.02963-0.18627 0.0635-0.18203 0.2286-0.33867 0.44027-0.33867 0.30057 0 0.4445 0.23707 0.4445 0.63077v1.1599h0.36407v-1.2065c0-0.7112-0.40217-0.889-0.6731-0.889-0.19473 0-0.3302 0.0508-0.45297 0.14393-0.08467 0.0635-0.1651 0.1524-0.2286 0.2667h-0.0085c-0.0889-0.2413-0.30057-0.41063-0.57997-0.41063-0.33867 0-0.52917 0.18203-0.64347 0.37677h-0.0127l-0.01693-0.3302h-0.32597c0.0127 0.16933 0.01693 0.3429 0.01693 0.55457z"/>
<path d="m89.776 18.434c0-0.41063-0.1524-0.8382-0.77893-0.8382-0.25823 0-0.50377 0.07197-0.6731 0.18203l0.08467 0.24553c0.14393-0.09313 0.3429-0.1524 0.5334-0.1524 0.4191 0 0.46567 0.3048 0.46567 0.47413v0.04233c-0.79163-0.0042-1.2319 0.2667-1.2319 0.762 0 0.29633 0.21167 0.58843 0.62653 0.58843 0.2921 0 0.51223-0.14393 0.62653-0.3048h0.0127l0.02963 0.25823h0.33867c-0.0254-0.1397-0.03387-0.31327-0.03387-0.49107zm-0.35983 0.56727c0 0.0381-0.0085 0.08043-0.02117 0.11853-0.05927 0.17357-0.2286 0.3429-0.4953 0.3429-0.1905 0-0.35137-0.1143-0.35137-0.3556 0-0.39793 0.46143-0.4699 0.86783-0.46143z"/>
<path d="m90.521 17.266v0.37677h-0.3175v0.28363h0.3175v1.1176c0 0.2413 0.0381 0.42333 0.14393 0.5334 0.0889 0.1016 0.2286 0.16087 0.40217 0.16087 0.14393 0 0.25823-0.0254 0.3302-0.05503l-0.01693-0.2794c-0.05503 0.01693-0.1143 0.0254-0.2159 0.0254-0.20743 0-0.2794-0.14393-0.2794-0.39793v-1.1049h0.5334v-0.28364h-0.5334v-0.49107z"/>
<path d="m92.28 19.691v-2.0489h-0.37253v2.0489zm-0.18627-2.8575c-0.13547 0-0.23283 0.1016-0.23283 0.23283 0 0.127 0.09313 0.2286 0.22437 0.2286 0.14817 0 0.2413-0.1016 0.23707-0.2286 0-0.13123-0.0889-0.23283-0.2286-0.23283z"/>
<path d="m93.842 17.596c-0.56727 0-1.016 0.40217-1.016 1.088 0 0.6477 0.42757 1.0541 0.98213 1.0541 0.4953 0 1.0202-0.3302 1.0202-1.088 0-0.62653-0.39793-1.0541-0.98637-1.0541zm-0.0085 0.2794c0.44027 0 0.61383 0.44027 0.61383 0.7874 0 0.46143-0.2667 0.79587-0.6223 0.79587-0.36407 0-0.6223-0.33867-0.6223-0.7874 0-0.38947 0.1905-0.79587 0.63077-0.79587z"/>
<path d="m95.371 19.691h0.37253v-1.2319c0-0.0635 0.0085-0.127 0.0254-0.17357 0.0635-0.20743 0.254-0.381 0.49953-0.381 0.35137 0 0.47413 0.27517 0.47413 0.60537v1.1811h0.37253v-1.2234c0-0.70273-0.44027-0.87207-0.7239-0.87207-0.33867 0-0.57573 0.1905-0.67733 0.38523h-0.0085l-0.021133-0.33867h-0.3302c0.0127 0.16933 0.01693 0.3429 0.01693 0.55457z"/>
<path d="m97.954 19.738c0.15663 0 0.254-0.1143 0.254-0.2667 0-0.15663-0.1016-0.2667-0.24977-0.2667-0.14817 0-0.254 0.11007-0.254 0.2667 0 0.1524 0.1016 0.2667 0.24553 0.2667z"/>
<path d="m99.732 16.838v2.8533h0.3683v-1.3377h1.3801v1.3377h0.37253v-2.8533h-0.37253v1.1938h-1.3801v-1.1938z"/>
<path d="m103.42 17.596c-0.56727 0-1.016 0.40217-1.016 1.088 0 0.6477 0.42756 1.0541 0.98213 1.0541 0.4953 0 1.0202-0.3302 1.0202-1.088 0-0.62653-0.39793-1.0541-0.98636-1.0541zm-8e-3 0.2794c0.44027 0 0.61383 0.44027 0.61383 0.7874 0 0.46143-0.2667 0.79587-0.6223 0.79587-0.36406 0-0.6223-0.33867-0.6223-0.7874 0-0.38947 0.1905-0.79587 0.63077-0.79587z"/>
<path d="m105.04 17.266v0.37677h-0.3175v0.28363h0.3175v1.1176c0 0.2413 0.0381 0.42333 0.14394 0.5334 0.0889 0.1016 0.2286 0.16087 0.40216 0.16087 0.14394 0 0.25824-0.0254 0.3302-0.05503l-0.0169-0.2794c-0.055 0.01693-0.1143 0.0254-0.2159 0.0254-0.20743 0-0.2794-0.14393-0.2794-0.39793v-1.1049h0.5334v-0.28364h-0.5334v-0.49107z"/>
<path d="m106.79 16.686h-0.3683v3.0057h0.3683v-0.77047l0.1905-0.21167 0.70697 0.98213h0.45297l-0.9017-1.2065 0.79163-0.84243h-0.44873l-0.60114 0.70697c-0.0593 0.07197-0.13123 0.16087-0.18203 0.23283h-8e-3z"/>
<path d="m110.13 18.735c4e-3 -0.0381 0.0127-0.09737 0.0127-0.17357 0-0.37677-0.1778-0.9652-0.84667-0.9652-0.5969 0-0.96097 0.48683-0.96097 1.1049 0 0.61807 0.37677 1.0372 1.0075 1.0372 0.32596 0 0.55033-0.07197 0.68156-0.13123l-0.0635-0.2667c-0.1397 0.05927-0.30056 0.10583-0.56726 0.10583-0.37254 0-0.69427-0.20743-0.70274-0.7112zm-1.4351-0.2667c0.0296-0.25823 0.19473-0.60537 0.5715-0.60537 0.4191 0 0.5207 0.3683 0.51646 0.60537z"/>
<path d="m110.41 17.642 0.75776 1.8881c0.0169 0.04657 0.0254 0.0762 0.0254 0.09737s-0.0127 0.0508-0.0296 0.0889c-0.0847 0.1905-0.21167 0.33443-0.31327 0.41487-0.11006 0.09313-0.23283 0.1524-0.32596 0.18203l0.0931 0.31327c0.0931-0.01693 0.27517-0.08043 0.4572-0.2413 0.254-0.22013 0.43603-0.57997 0.70273-1.2827l0.5588-1.4605h-0.3937l-0.4064 1.2023c-0.0508 0.14817-0.0931 0.3048-0.13123 0.42757h-8e-3c-0.0339-0.12277-0.0847-0.28363-0.13123-0.4191l-0.4492-1.2107z"/>
<path d="m112.58 19.594c0.14393 0.08467 0.35136 0.14393 0.57573 0.14393 0.48683 0 0.76623-0.25823 0.76623-0.61807 0-0.3048-0.18203-0.4826-0.53763-0.61807-0.2667-0.1016-0.38947-0.1778-0.38947-0.34713 0-0.1524 0.12277-0.2794 0.3429-0.2794 0.1905 0 0.33867 0.06773 0.4191 0.11853l0.0931-0.27093c-0.1143-0.06773-0.29634-0.127-0.50377-0.127-0.44027 0-0.70697 0.27093-0.70697 0.60113 0 0.24553 0.17357 0.44873 0.54187 0.57997 0.27517 0.1016 0.381 0.19897 0.381 0.37677 0 0.16933-0.127 0.3048-0.39793 0.3048-0.18627 0-0.381-0.0762-0.49107-0.14817z"/>
<path d="m114.59 19.738c0.15664 0 0.254-0.1143 0.254-0.2667 0-0.15663-0.1016-0.2667-0.24976-0.2667-0.14817 0-0.254 0.11007-0.254 0.2667 0 0.1524 0.1016 0.2667 0.24553 0.2667z"/>
<path d="m116.22 19.552c0.1524 0.1016 0.45296 0.18627 0.72813 0.18627 0.6731 0 0.99907-0.38523 0.99907-0.8255 0-0.4191-0.24554-0.65193-0.72814-0.8382-0.3937-0.1524-0.56726-0.28363-0.56726-0.55033 0-0.19473 0.14816-0.42757 0.53763-0.42757 0.25823 0 0.44873 0.08467 0.54187 0.13547l0.1016-0.30057c-0.127-0.07197-0.33867-0.1397-0.63077-0.1397-0.55457 0-0.92287 0.3302-0.92287 0.7747 0 0.40217 0.28787 0.64347 0.75354 0.80857 0.38523 0.14817 0.53763 0.30057 0.53763 0.56727 0 0.28787-0.22013 0.48683-0.5969 0.48683-0.254 0-0.4953-0.08467-0.6604-0.18627z"/>
<path d="m119.93 19.336c-0.10583 0.04657-0.24553 0.1016-0.4572 0.1016-0.4064 0-0.7112-0.2921-0.7112-0.76623 0-0.42757 0.254-0.7747 0.7239-0.7747 0.2032 0 0.3429 0.04657 0.4318 0.09737l0.0847-0.28787c-0.1016-0.0508-0.29633-0.10583-0.51647-0.10583-0.66886 0-1.1007 0.4572-1.1007 1.088 0 0.62653 0.40216 1.0499 1.0202 1.0499 0.27517 0 0.49107-0.07197 0.58843-0.12277z"/>
<path d="m120.5 19.691h0.3683v-1.0922c0-0.0635 8e-3 -0.12277 0.0169-0.17357 0.0508-0.2794 0.23707-0.47837 0.49954-0.47837 0.0508 0 0.0889 0.0042 0.127 0.0127v-0.35137c-0.0339-0.0085-0.0635-0.0127-0.10584-0.0127-0.24976 0-0.47413 0.17357-0.56726 0.44873h-0.0169l-0.0127-0.40217h-0.32596c0.0127 0.1905 0.0169 0.39793 0.0169 0.63923z"/>
<path d="m122.35 19.691v-2.0489h-0.37253v2.0489zm-0.18627-2.8575c-0.13546 0-0.23283 0.1016-0.23283 0.23283 0 0.127 0.0931 0.2286 0.22437 0.2286 0.14816 0 0.2413-0.1016 0.23706-0.2286 0-0.13123-0.0889-0.23283-0.2286-0.23283z"/>
<path d="m123.04 20.53h0.3683v-1.1134h8e-3c0.12277 0.2032 0.35984 0.32173 0.63077 0.32173 0.4826 0 0.93133-0.36407 0.93133-1.1007 0-0.6223-0.37253-1.0414-0.86783-1.0414-0.33443 0-0.57573 0.14817-0.72813 0.39793h-8e-3l-0.0169-0.35137h-0.33444c8e-3 0.19473 0.0169 0.4064 0.0169 0.66887zm0.3683-2.0235c0-0.0508 0.0127-0.10583 0.0254-0.1524 0.072-0.2794 0.30903-0.46143 0.56303-0.46143 0.3937 0 0.6096 0.35137 0.6096 0.762 0 0.4699-0.2286 0.79163-0.6223 0.79163-0.2667 0-0.49107-0.1778-0.5588-0.43603-8e-3 -0.04657-0.0169-0.09737-0.0169-0.1524z"/>
<path d="m125.6 17.266v0.37677h-0.3175v0.28363h0.3175v1.1176c0 0.2413 0.0381 0.42333 0.14393 0.5334 0.0889 0.1016 0.2286 0.16087 0.40217 0.16087 0.14393 0 0.25823-0.0254 0.3302-0.05503l-0.0169-0.2794c-0.055 0.01693-0.1143 0.0254-0.2159 0.0254-0.20744 0-0.2794-0.14393-0.2794-0.39793v-1.1049h0.5334v-0.28364h-0.5334v-0.49107z"/>
<path d="m127.36 19.691v-2.0489h-0.37253v2.0489zm-0.18626-2.8575c-0.13547 0-0.23284 0.1016-0.23284 0.23283 0 0.127 0.0931 0.2286 0.22437 0.2286 0.14817 0 0.2413-0.1016 0.23707-0.2286 0-0.13123-0.0889-0.23283-0.2286-0.23283z"/>
<path d="m128.06 19.691h0.37253v-1.2319c0-0.0635 8e-3 -0.127 0.0254-0.17357 0.0635-0.20743 0.254-0.381 0.49953-0.381 0.35137 0 0.47414 0.27517 0.47414 0.60537v1.1811h0.37253v-1.2234c0-0.70273-0.44027-0.87207-0.7239-0.87207-0.33867 0-0.57573 0.1905-0.67733 0.38523h-8e-3l-0.0212-0.33867h-0.3302c0.0127 0.16933 0.0169 0.3429 0.0169 0.55457z"/>
<path d="m132.23 18.205c0-0.24977 8e-3 -0.41487 0.0169-0.56303h-0.32597l-0.0169 0.30903h-8e-3c-0.0889-0.16933-0.28363-0.3556-0.63923-0.3556-0.4699 0-0.92287 0.3937-0.92287 1.088 0 0.56727 0.36407 0.99907 0.87207 0.99907 0.3175 0 0.53763-0.1524 0.65193-0.3429h8e-3v0.2286c0 0.51647-0.2794 0.71543-0.6604 0.71543-0.254 0-0.46567-0.0762-0.60114-0.16087l-0.0931 0.28363c0.1651 0.11007 0.43603 0.16933 0.68157 0.16933 0.25823 0 0.5461-0.05927 0.74506-0.24553 0.19897-0.1778 0.2921-0.46567 0.2921-0.93557zm-0.3683 0.61383c0 0.0635-8e-3 0.13547-0.0296 0.19897-0.0804 0.23707-0.28787 0.381-0.51647 0.381-0.40216 0-0.60536-0.33443-0.60536-0.7366 0-0.47413 0.254-0.7747 0.6096-0.7747 0.27093 0 0.45296 0.1778 0.5207 0.3937 0.0169 0.0508 0.0212 0.10583 0.0212 0.16933z"/>
<path d="m133.01 19.738c0.15663 0 0.254-0.1143 0.254-0.2667 0-0.15663-0.1016-0.2667-0.24977-0.2667-0.14816 0-0.254 0.11007-0.254 0.2667 0 0.1524 0.1016 0.2667 0.24554 0.2667z"/>
</g>
</g>
<g transform="translate(-1.6603 -1.2012)">
<rect x="3.5892" y="3.0506" width="19.289" height="18.494" ry="1.5066" filter="url(#ab)" style="paint-order:stroke fill markers"/>
<rect x="24.742" y="3.0506" width="19.311" height="18.494" ry="1.5066" filter="url(#ac)" style="paint-order:stroke fill markers"/>
<rect x="45.905" y="3.0506" width="19.313" height="18.494" ry="1.5066" filter="url(#ad)" style="paint-order:stroke fill markers"/>
</g>
<g transform="translate(-1.6603 -1.2012)">
<rect x="3.5892" y="5.3909" width="1.4627" height="12.886" fill="url(#h)" style="paint-order:stroke fill markers"/>
<rect transform="scale(-1,1)" x="-22.878" y="5.4201" width="2.0071" height="12.39" fill="url(#g)" style="paint-order:stroke fill markers"/>
<rect x="5.292" y="3.0506" width="14.967" height="1.0441" fill="#f3f3f3" style="paint-order:stroke fill markers"/>
<path d="m4.8887 5.9034-1.2995-0.43258v-1.0022c0.063957-0.91433 0.84563-1.4177 1.4495-1.4177l0.27581-2.722e-4 0.46682 1.8521z" fill="url(#m)" stroke-width=".26458"/>
<path d="m20.935 6.7218 1.943-1.2186v-1.0849c-0.04536-0.50919-0.45582-1.3248-1.4671-1.368l-1.3372 2.717e-4 -0.46142 2.2406z" fill="url(#l)" stroke-width=".26458"/>
<path d="m20.643 17.311 2.2349 0.43286v2.4326c-0.04536 0.50919-0.45582 1.3248-1.4671 1.368h-1.1966l-0.39775-2.191z" fill="url(#n)" stroke-width=".26458"/>
<path d="m5.8076 17.79-2.2184-0.09631v2.4326c0.063957 0.91433 0.84563 1.4177 1.4495 1.4177h1.2113l0.48335-2.1415z" fill="url(#o)" stroke-width=".26458"/>
<rect x="6.1979" y="18.111" width="14.043" height="3.433" fill="#b7b7b7" stroke-width=".33319"/>
<rect transform="scale(-1,1)" x="-44.053" y="5.3625" width="2.0071" height="12.39" fill="url(#u)" style="paint-order:stroke fill markers"/>
<rect x="26.455" y="3.0506" width="14.851" height="1.1844" fill="#f3f3f3" style="paint-order:stroke fill markers"/>
<path d="m26.042 5.9034-1.2995-0.43258v-1.0022c0.06396-0.91433 0.84563-1.4177 1.4495-1.4177l0.27581-2.722e-4 0.46682 1.8521z" fill="url(#s)" stroke-width=".26458"/>
<path d="m42.11 6.7218 1.943-1.2186v-1.0849c-0.04536-0.50919-0.45582-1.3248-1.4671-1.368l-1.3372 2.717e-4 -0.03147 2.6706z" fill="url(#q)" stroke-width=".26458"/>
<rect transform="scale(-1,1)" x="-65.216" y="5.3625" width="2.0071" height="12.39" fill="url(#t)" style="paint-order:stroke fill markers"/>
<rect x="47.618" y="3.0506" width="14.851" height="1.5236" fill="#f3f3f3" style="paint-order:stroke fill markers"/>
<path d="m47.621 6.4997-1.7157-1.0289v-1.0022c0.06396-0.91433 0.84563-1.4177 1.4495-1.4177l0.27581-2.722e-4 0.46682 1.8521z" fill="url(#r)" stroke-width=".26458"/>
<path d="m63.273 6.7218 1.943-1.2186v-1.0849c-0.04536-0.50919-0.45582-1.3248-1.4671-1.368l-1.3372 2.717e-4 -0.03147 2.6706z" fill="url(#p)" stroke-width=".26458"/>
<path d="m41.82 17.311 2.2349 0.43286v2.4326c-0.04536 0.50919-0.45582 1.3248-1.4671 1.368h-1.1966l-0.46389-3.2328z" fill="url(#y)" stroke-width=".26458"/>
<path d="m48.14 17.261-2.2349 0.43286v2.4326c0.06396 0.91433 0.84563 1.4177 1.4495 1.4177h1.2113l0.46682-3.2825z" fill="url(#z)" stroke-width=".26458"/>
<path d="m62.983 17.311 2.2349 0.43286v2.4326c-0.04536 0.50919-0.45582 1.3248-1.4671 1.368h-1.1966l-0.46389-3.2328z" fill="url(#x)" stroke-width=".26458"/>
<path d="m26.919 17.928-2.1764-0.23365v2.4326c0.06396 0.91433 0.84563 1.4177 1.4495 1.4177h1.2113l1.0164-2.5225z" fill="url(#aa)" stroke-width=".26458"/>
<rect x="27.403" y="18.111" width="14.241" height="3.433" fill="#b7b7b7" stroke-width=".33553"/>
<rect x="24.742" y="5.3041" width="1.6972" height="12.952" fill="url(#w)" style="paint-order:stroke fill markers"/>
<rect x="26.185" y="3.9462" width="16.409" height="15.996" ry="1.3031" fill="url(#j)" style="paint-order:stroke fill markers"/>
<rect x="48.368" y="18.111" width="14.506" height="3.433" fill="#b7b7b7" stroke-width=".33863"/>
<rect x="45.905" y="5.3041" width="1.744" height="12.82" fill="url(#v)" style="paint-order:stroke fill markers"/>
<rect x="47.621" y="3.9462" width="16.407" height="15.996" ry="1.3031" fill="url(#i)" style="paint-order:stroke fill markers"/>
<rect x="4.5837" y="3.9462" width="16.351" height="15.996" ry="1.3031" fill="url(#k)" style="paint-order:stroke fill markers"/>
</g>
<g transform="translate(-1.6603 -1.2012)">
<g aria-label="A">
<path d="m9.6509 16.809 0.40076 1.1797h0.59831l-1.4676-4.0527h-0.62089l-1.524 4.0527h0.56444l0.42333-1.1797zm-1.4563-0.48542 0.64911-1.7836h0.011289l0.59831 1.7836z" stroke-width=".26458"/>
</g>
<g aria-label="H">
<path d="m31.74 15.64h-2.1167v-1.6764h-0.54751v4.0527h0.54751v-1.8909h2.1167v1.8909h0.54751v-4.0527h-0.54751z" stroke-width=".26458"/>
</g>
<text x="50.560715" y="16.130398" fill="#000000" font-family="'Myriad Pro'" font-size="5.6444px" letter-spacing="0px" stroke-width=".26458" word-spacing="0px" style="line-height:1.25" xml:space="preserve"><tspan x="50.560715" y="21.069286" stroke-width=".26458"/></text>
<g aria-label="K">
<path d="m50.818 16.572 0.63782-0.6096 1.4168 2.032h0.7112l-1.7328-2.4158 1.6877-1.6369h-0.75636l-1.9643 1.9643v-1.9643h-0.54751v4.0527h0.54751z" stroke-width=".26458"/>
</g>
</g>
</svg>
