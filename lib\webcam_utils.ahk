#Requires AutoHotkey v2.0+
/*
webcam_utils.ahk
Reusable webcam library for WinCBT-Biometric and related applications
Provides functions for webcam initialization, control, image capture, and display

Functions:
- StartWebcam(): Initializes and starts the webcam preview
- StopWebcam(): Stops the webcam and cleans up resources
- DismissDialogs(): Automatically handles webcam-related dialog boxes
- CaptureWebcamImage(): Captures an image from the webcam feed
- ShowControl(): Shows a GUI control using Windows API
- HideControl(): Hides a GUI control using Windows API
- ToggleWebcamAndCapturedImage(): Toggles visibility between webcam feed and captured image
- ControlExist(): Helper function to check if a control exists
- Cap_CreateCaptureWindow(): Helper function to create a capture window
- InitGDIPlus(): Initialize GDI+ for image processing
- ShutdownGDIPlus(): Shutdown GDI+ and clean up resources

Usage:
1. Include this library in your script
2. Create two overlapping controls - one for webcam feed and one for captured image
3. Call StartWebcam() to initialize the webcam feed
4. Use CaptureWebcamImage() to capture an image
5. Use ToggleWebcamAndCapturedImage() to switch between live feed and captured image
6. Call StopWebcam() when done to clean up resources

Example:
```autohotkey
; Create controls
webcamControl := myGui.AddText("x10 y10 w640 h480 +Border +0x200", "No Camera Feed")
capturedImageControl := myGui.AddPicture("x10 y10 w640 h480 +Border Hidden", "")

; Start webcam
capHwnd := StartWebcam(webcamControl, "HD Pro Webcam C920")

; Capture image
result := CaptureWebcamImage(webcamControl, capHwnd)
if (result.success) {
    ; Display captured image
    capturedImageControl.Value := result.filename
    ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, false)
}

; Switch back to live feed
ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true)

; Stop webcam when done
StopWebcam(capHwnd)
```
*/

/**
 * StartWebcam - Initializes and starts the webcam preview
 * @param {Object} webcamControl - The GUI control where the webcam feed will be displayed
 * @param {String} cameraName - The name of the camera to use (used for dialog handling)
 * @param {Integer} capHwnd - Optional existing capture window handle (if restarting)
 * @param {Integer} fps - Frames per second for the preview (default: 15)
 * @returns {Integer} - The capture window handle if successful, 0 otherwise
 */
StartWebcam(webcamControl, cameraName, capHwnd := 0, fps := 15) {
    ; Stop any existing webcam feed if capHwnd is provided
    if (capHwnd) {
        OutputDebug("StartWebcam: Stopping existing webcam with handle: " capHwnd)
        StopWebcam(capHwnd)
        capHwnd := 0  ; Reset the handle after stopping
        Sleep(50)     ; Short delay to ensure resources are released
    }

    ; Validate parameters
    if (!IsObject(webcamControl)) {
        OutputDebug("StartWebcam: Invalid webcam control")
        return 0
    }

    try {
        ; Get the position and size of the webcam control
        try {
            webcamControl.GetPos(&ctrlX, &ctrlY, &ctrlW, &ctrlH)
        } catch as posErr {
            OutputDebug("StartWebcam: Error getting control position: " posErr.Message)
            return 0
        }

        ; Validate control dimensions
        if (ctrlW <= 0 || ctrlH <= 0) {
            OutputDebug("StartWebcam: Invalid control dimensions: " ctrlW "x" ctrlH)
            return 0
        }

        ; Reset the webcam control text
        if (webcamControl.HasProp("Text")) {
            try {
                webcamControl.Text := "Initializing camera..."
            } catch as textErr {
                OutputDebug("StartWebcam: Error setting control text: " textErr.Message)
                ; Continue anyway
            }
        }

        ; Always create a new capture window for better reliability when switching cameras
        ; Get the control's HWND
        ctrlHwnd := 0
        try {
            ctrlHwnd := webcamControl.Hwnd
            OutputDebug("StartWebcam: Control handle: " ctrlHwnd)
        } catch as hwndErr {
            OutputDebug("StartWebcam: Error getting control HWND: " hwndErr.Message)
            return 0
        }

        ; Validate the control HWND
        if (!ctrlHwnd || !DllCall("IsWindow", "Ptr", ctrlHwnd)) {
            OutputDebug("StartWebcam: Invalid control HWND: " ctrlHwnd)
            return 0
        }

        ; Create the capture window as a child of the control
        capHwnd := Cap_CreateCaptureWindow(ctrlHwnd, 0, 0, ctrlW, ctrlH)

        if (!capHwnd) {
            OutputDebug("StartWebcam: Failed to create capture window")
            ; Update the webcam control text to show error
            if (webcamControl.HasProp("Text")) {
                try {
                    webcamControl.Text := "Failed to create capture window"
                } catch {
                    ; Ignore text update errors
                }
            }
            return 0
        }

        ; Get the absolute position of the control within the GUI
        webcamControl.GetPos(&ctrlX, &ctrlY, &ctrlW, &ctrlH)

        ; Get the GUI window handle
        guiHwnd := webcamControl.Gui.Hwnd

        ; Create a POINT structure for the coordinates
        point := Buffer(8)
        NumPut("Int", ctrlX, point, 0)
        NumPut("Int", ctrlY, point, 4)

        ; Convert to screen coordinates
        DllCall("ClientToScreen", "Ptr", guiHwnd, "Ptr", point)

        ; Extract the converted coordinates
        ctrlX := NumGet(point, 0, "Int")
        ctrlY := NumGet(point, 4, "Int")

        OutputDebug("StartWebcam: Control position: " ctrlX "," ctrlY " size: " ctrlW "x" ctrlH)

        ; Position the capture window over the control
        DllCall("SetWindowPos", "Ptr", capHwnd, "Ptr", 0,
               "Int", 0, "Int", 0,  ; Position at 0,0 relative to parent
               "Int", ctrlW, "Int", ctrlH,
               "UInt", 0x0040)  ; SWP_SHOWWINDOW

        ; Make sure the window is visible
        WinSetStyle("+0x10000000", "ahk_id " capHwnd)  ; Add WS_VISIBLE style

        OutputDebug("StartWebcam: Created and positioned capture window with handle: " capHwnd)

        ; Define message constants
        WM_CAP := 0x400
        WM_CAP_DRIVER_CONNECT := WM_CAP + 10
        WM_CAP_SET_PREVIEW := WM_CAP + 50
        WM_CAP_SET_PREVIEWRATE := WM_CAP + 52
        WM_CAP_SET_SCALE := WM_CAP + 53
        WM_CAP_DLG_VIDEOSOURCE := WM_CAP + 42

        ; Log the camera name we're trying to connect to
        OutputDebug("StartWebcam: Attempting to connect to camera: " cameraName)

        ; Try to connect to the camera at index 0
        global dismissDialogsTimer := 0

        OutputDebug("StartWebcam: Connecting to camera at index 0")
        result := 0
        try {
            ; Validate the capture window still exists before sending messages
            if (!DllCall("IsWindow", "Ptr", capHwnd)) {
                throw Error("Capture window was destroyed before camera connection")
            }

            result := SendMessage(WM_CAP_DRIVER_CONNECT, 0, 0, , "ahk_id " capHwnd)
        } catch as connectErr {
            OutputDebug("StartWebcam: Error connecting to camera: " connectErr.Message)
            ; Clean up the capture window
            if (capHwnd && DllCall("IsWindow", "Ptr", capHwnd)) {
                DllCall("DestroyWindow", "Ptr", capHwnd)
            }
            return 0
        }

        if (result) {
            ; Successfully connected to the camera
            OutputDebug("StartWebcam: Successfully connected to camera")

            try {
                ; Set the preview scale
                SendMessage(WM_CAP_SET_SCALE, 1, 0, , "ahk_id " capHwnd)

                ; Set the preview rate in milliseconds
                fps_ms := Round((1/fps)*1000)
                SendMessage(WM_CAP_SET_PREVIEWRATE, fps_ms, 0, , "ahk_id " capHwnd)

                ; Start previewing the image from the camera
                SendMessage(WM_CAP_SET_PREVIEW, 1, 0, , "ahk_id " capHwnd)

                ; Move window to correct position and size
                ; Use HWND_TOP (0) to ensure it's on top, and set SWP_SHOWWINDOW to make it visible
                DllCall("SetWindowPos", "Ptr", capHwnd, "Ptr", 0  ; HWND_TOP
                    , "Int", 0, "Int", 0  ; x,y position (0,0 relative to parent)
                    , "Int", ctrlW, "Int", ctrlH  ; Width and height
                    , "UInt", 0x0040)  ; SWP_SHOWWINDOW

                ; Output debug info about the window
                OutputDebug("StartWebcam: Window positioned at 0,0 with size " ctrlW "x" ctrlH)

                ; Clear any text in the webcam control
                if (webcamControl.HasProp("Text")) {
                    try {
                        webcamControl.Text := ""
                    } catch as textErr {
                        OutputDebug("StartWebcam: Error clearing control text: " textErr.Message)
                        ; Continue anyway
                    }
                }

                OutputDebug("StartWebcam: Webcam started successfully")
                return capHwnd

            } catch as configErr {
                OutputDebug("StartWebcam: Error configuring camera: " configErr.Message)
                ; Try to disconnect and clean up
                try {
                    SendMessage(WM_CAP_DRIVER_DISCONNECT, 0, 0, , "ahk_id " capHwnd)
                } catch {
                    ; Ignore disconnect errors
                }
                if (capHwnd && DllCall("IsWindow", "Ptr", capHwnd)) {
                    DllCall("DestroyWindow", "Ptr", capHwnd)
                }
                return 0
            }
        } else {
            OutputDebug("StartWebcam: Failed to connect to camera")

            ; Clean up
            if (capHwnd) {
                DllCall("DestroyWindow", "Ptr", capHwnd)
            }

            ; Update the webcam control text
            if (webcamControl.HasProp("Text")) {
                webcamControl.Text := "Failed to connect to camera"
            }

            return 0
        }
    } catch as err {
        OutputDebug("StartWebcam: Error - " err.Message)

        ; Clean up
        if (capHwnd) {
            DllCall("DestroyWindow", "Ptr", capHwnd)
        }

        ; Update the webcam control text
        if (webcamControl.HasProp("Text")) {
            webcamControl.Text := "Error: " err.Message
        }

        return 0
    }
}

/**
 * StopWebcam - Stops the webcam and cleans up resources
 * @param {Integer} capHwnd - The capture window handle
 * @returns {Boolean} - True if successful, false otherwise
 */
StopWebcam(capHwnd) {
    if (!capHwnd) {
        OutputDebug("StopWebcam: No capture window handle provided")
        return false
    }

    try {
        ; Check if the window still exists
        if (DllCall("IsWindow", "Ptr", capHwnd)) {
            ; Disconnect from the driver
            WM_CAP := 0x400
            WM_CAP_DRIVER_DISCONNECT := WM_CAP + 11
            WM_CAP_SET_PREVIEW := WM_CAP + 50

            ; First stop the preview
            SendMessage(WM_CAP_SET_PREVIEW, 0, 0, , "ahk_id " capHwnd)
            OutputDebug("StopWebcam: Stopped preview")

            ; Short delay to ensure preview is stopped
            Sleep(50)

            ; Try to disconnect from the driver
            SendMessage(WM_CAP_DRIVER_DISCONNECT, 0, 0, , "ahk_id " capHwnd)
            OutputDebug("StopWebcam: Disconnected from webcam driver")

            ; Short delay to ensure driver is disconnected
            Sleep(50)

            ; Destroy the window
            DllCall("DestroyWindow", "Ptr", capHwnd)
            OutputDebug("StopWebcam: Destroyed webcam window")

            ; Force garbage collection to release resources
            try {
                DllCall("GdiFlush")
                DllCall("Ole32\CoFreeUnusedLibraries")
            } catch as err {
                OutputDebug("StopWebcam: Error during resource cleanup - " err.Message)
            }

            return true
        } else {
            OutputDebug("StopWebcam: Capture window no longer exists")
            return false
        }
    } catch as err {
        OutputDebug("StopWebcam: Error - " err.Message)
        return false
    }
}

/**
 * DismissDialogs - Automatically handles webcam-related dialog boxes
 * @param {String} cameraName - The name of the camera to select in dialogs
 * @returns {Boolean} - True if a dialog was handled, false otherwise
 */
DismissDialogs(cameraName) {
    static dialogClasses := ["#32770", "SysDialogClass", "DirectUIHWND"]

    ; Look for dialog windows
    for _, className in dialogClasses {
        dialogHwnd := WinExist("ahk_class " className)
        if (dialogHwnd) {
            ; Get the window title
            windowTitle := WinGetTitle("ahk_id " dialogHwnd)
            OutputDebug("DismissDialogs: Found dialog with title: " windowTitle)

            ; Specifically check for "Video Source" dialog
            if (windowTitle = "Video Source") {
                OutputDebug("DismissDialogs: Found Video Source dialog")

                ; Try to select the correct camera in the dropdown if available
                if (ControlExist("ComboBox1", "ahk_id " dialogHwnd) && cameraName != "") {
                    ; Get the list of items in the dropdown
                    try {
                        ; Activate the window first
                        WinActivate("ahk_id " dialogHwnd)
                        Sleep(100)

                        ; Try to select our camera by name
                        Control := "ComboBox1"
                        ControlFocus(Control, "ahk_id " dialogHwnd)

                        ; Get the number of items in the ComboBox
                        itemCount := SendMessage(0x0146, 0, 0, Control, "ahk_id " dialogHwnd) ; CB_GETCOUNT
                        OutputDebug("DismissDialogs: ComboBox has " itemCount " items")

                        ; Try to find our camera in the list
                        found := false
                        Loop itemCount {
                            ; Get the text of each item
                            itemIndex := A_Index - 1
                            textLen := SendMessage(0x0149, itemIndex, 0, Control, "ahk_id " dialogHwnd) ; CB_GETLBTEXTLEN

                            if (textLen > 0) {
                                ; Create a buffer for the text
                                itemText := Buffer((textLen + 1) * 2, 0)
                                SendMessage(0x0148, itemIndex, itemText.Ptr, Control, "ahk_id " dialogHwnd) ; CB_GETLBTEXT

                                ; Convert to string
                                itemStr := StrGet(itemText.Ptr)
                                OutputDebug("DismissDialogs: Item " itemIndex ": " itemStr)

                                ; Check if this matches our saved camera
                                if (itemStr = cameraName || InStr(itemStr, cameraName) || InStr(cameraName, itemStr)) {
                                    ; Select this item
                                    OutputDebug("DismissDialogs: Found matching camera at index " itemIndex)
                                    SendMessage(0x014E, itemIndex, 0, Control, "ahk_id " dialogHwnd) ; CB_SETCURSEL
                                    found := true
                                    break
                                }
                            }
                        }

                        ; If we couldn't find a match, just select the first item
                        if (!found && itemCount > 0) {
                            OutputDebug("DismissDialogs: No match found, selecting first item")
                            SendMessage(0x014E, 0, 0, Control, "ahk_id " dialogHwnd) ; CB_SETCURSEL
                        }
                    } catch as err {
                        OutputDebug("DismissDialogs: Error selecting camera in dropdown - " err.Message)
                    }
                }

                ; Click the OK button (Button2)
                if (ControlExist("Button2", "ahk_id " dialogHwnd)) {
                    Sleep(100)  ; Give time for the selection to register
                    ControlClick("Button2", "ahk_id " dialogHwnd)
                    OutputDebug("DismissDialogs: Clicked OK button (Button2)")
                    return true
                }

                ; If Button2 doesn't exist, try Button1
                if (ControlExist("Button1", "ahk_id " dialogHwnd)) {
                    Sleep(100)
                    ControlClick("Button1", "ahk_id " dialogHwnd)
                    OutputDebug("DismissDialogs: Clicked Button1")
                    return true
                }

                ; If no buttons found, try Enter key
                WinActivate("ahk_id " dialogHwnd)
                Sleep(100)
                SendInput("{Enter}")
                OutputDebug("DismissDialogs: Sent Enter key to Video Source dialog")
                return true
            }

            ; Check for other video-related dialogs
            else if (windowTitle ~= "i)video|camera|source|format|display") {
                OutputDebug("DismissDialogs: Found video-related dialog - " windowTitle)

                ; Try to find and click OK button - try Button2 first (common for OK)
                if (ControlExist("Button2", "ahk_id " dialogHwnd)) {
                    ControlClick("Button2", "ahk_id " dialogHwnd)
                    OutputDebug("DismissDialogs: Clicked Button2 (OK)")
                    return true
                }

                ; Then try Button1
                if (ControlExist("Button1", "ahk_id " dialogHwnd)) {
                    ControlClick("Button1", "ahk_id " dialogHwnd)
                    OutputDebug("DismissDialogs: Clicked Button1")
                    return true
                }

                ; If no buttons found, try Enter key
                WinActivate("ahk_id " dialogHwnd)
                Sleep(50)
                SendInput("{Enter}")
                OutputDebug("DismissDialogs: Sent Enter key to dialog")
                return true
            }
        }
    }

    return false
}

/**
 * ControlExist - Helper function to check if a control exists
 * @param {String} Control - The control to check for
 * @param {String} WinTitle - The window title to check in
 * @param {String} WinText - Optional window text to match
 * @param {String} ExcludeTitle - Optional title to exclude
 * @param {String} ExcludeText - Optional text to exclude
 * @returns {Boolean} - True if the control exists, false otherwise
 */
ControlExist(Control, WinTitle := "", WinText := "", ExcludeTitle := "", ExcludeText := "") {
    try {
        ControlGetPos(, , , , Control, WinTitle, WinText, ExcludeTitle, ExcludeText)
        return true
    } catch {
        return false
    }
}

/**
 * Cap_CreateCaptureWindow - Helper function to create a capture window
 * @param {Integer} hWndParent - The handle of the parent window
 * @param {Integer} x - The x-coordinate for the capture window
 * @param {Integer} y - The y-coordinate for the capture window
 * @param {Integer} w - The width for the capture window
 * @param {Integer} h - The height for the capture window
 * @returns {Integer} - The handle (HWND) of the created capture window, or 0 on failure
 */
Cap_CreateCaptureWindow(hWndParent, x, y, w, h) {
    ; Validate input parameters
    if (!hWndParent || !DllCall("IsWindow", "Ptr", hWndParent)) {
        OutputDebug("Cap_CreateCaptureWindow: Invalid parent window handle")
        return 0
    }

    if (w <= 0 || h <= 0) {
        OutputDebug("Cap_CreateCaptureWindow: Invalid dimensions: " w "x" h)
        return 0
    }

    try {
        ; Check if avicap32.dll is available
        hModule := DllCall("GetModuleHandle", "Str", "avicap32.dll", "Ptr")
        if (!hModule) {
            ; Try to load the library
            hModule := DllCall("LoadLibrary", "Str", "avicap32.dll", "Ptr")
            if (!hModule) {
                OutputDebug("Cap_CreateCaptureWindow: Failed to load avicap32.dll")
                return 0
            }
        }

        ; Window styles
        WS_CHILD := 0x40000000
        WS_VISIBLE := 0x10000000
        WS_BORDER := 0x00800000
        WS_OVERLAPPED := 0x00000000

        ; Extended window styles
        WS_EX_CLIENTEDGE := 0x00000200

        lpszWindowName := "capture"

        ; Create the capture window with appropriate styles and error handling
        capHandle := 0
        try {
            capHandle := DllCall("avicap32.dll\capCreateCaptureWindowW"
                          , "Str", lpszWindowName
                          , "UInt", WS_VISIBLE | WS_CHILD | WS_BORDER ; dwStyle
                          , "Int", x
                          , "Int", y
                          , "Int", w
                          , "Int", h
                          , "Ptr", hWndParent  ; Changed from UInt to Ptr for better compatibility
                          , "Int", 0
                          , "Ptr")  ; Specify return type as Ptr
        } catch as dllErr {
            OutputDebug("Cap_CreateCaptureWindow: DLL call failed: " dllErr.Message)
            return 0
        }

        if (capHandle && capHandle != 0) {
            ; Validate the created window
            if (DllCall("IsWindow", "Ptr", capHandle)) {
                ; Set the window to be visible
                try {
                    DllCall("ShowWindow", "Ptr", capHandle, "Int", 1) ; SW_SHOWNORMAL = 1
                    OutputDebug("Cap_CreateCaptureWindow: Created window with handle " capHandle)
                } catch as showErr {
                    OutputDebug("Cap_CreateCaptureWindow: Error showing window: " showErr.Message)
                    ; Continue anyway as the window was created
                }
            } else {
                OutputDebug("Cap_CreateCaptureWindow: Created handle is not a valid window")
                return 0
            }
        } else {
            OutputDebug("Cap_CreateCaptureWindow: Failed to create window - returned handle: " capHandle)
            return 0
        }

        return capHandle

    } catch as err {
        OutputDebug("Cap_CreateCaptureWindow: Unexpected error: " err.Message)
        return 0
    }
}

/**
 * InitGDIPlus - Initialize GDI+ for image processing
 * @returns {Integer} - GDI+ token if successful, 0 otherwise
 */
InitGDIPlus() {
    try {
        ; Initialize GDI+
        gdipStartup := Buffer(24, 0)        ; sizeof(GdiplusStartupInput) = 16
        NumPut("UInt", 1, gdipStartup, 0)   ; GdiplusVersion = 1

        ; Start GDI+
        gdipToken := 0
        result := DllCall("gdiplus\GdiplusStartup", "Ptr*", &gdipToken, "Ptr", gdipStartup, "Ptr", 0)

        if (result = 0) {
            return gdipToken
        } else {
            return 0
        }
    } catch {
        return 0
    }
}

/**
 * ShutdownGDIPlus - Shutdown GDI+ and clean up resources
 * @param {Integer} gdipToken - The GDI+ token returned by InitGDIPlus
 * @returns {Integer} - Result code, 0 on success
 */
ShutdownGDIPlus(gdipToken) {
    ; Add error handling to prevent crashes
    try {
        if (gdipToken && gdipToken != 0) {
            result := DllCall("gdiplus\GdiplusShutdown", "Ptr", gdipToken)
            return result
        } else {
            return 0
        }
    } catch {
        return 0
    }
}

/**
 * CaptureWebcamImage - Captures an image from the webcam feed
 * @param {Object} webcamControl - The GUI control where the webcam feed is displayed
 * @param {Integer} capHwnd - The capture window handle
 * @param {String} outputPath - Optional: The full path where the image should be saved (default: auto-generated in images folder)
 * @param {String} imageFormat - Optional: The format of the image (png or jpg, default: png)
 * @param {Object} statusBar - Optional: Status bar control to display messages
 * @returns {Object} - Object with success status and filename or error message
 */
CaptureWebcamImage(webcamControl, capHwnd, outputPath := "", imageFormat := "png", statusBar := 0) {
    ; Variables to hold resources that need cleanup
    gdipToken := 0
    pBitmap := 0
    pGraphics := 0
    pBitmapFromScreen := 0
    hScreenDC := 0
    hMemDC := 0
    hBitmap := 0
    hOldBitmap := 0

    ; Result object
    result := {success: false, filename: "", error: ""}

    ; Don't capture if webcam handle is invalid
    if (!capHwnd || !DllCall("IsWindow", "Ptr", capHwnd)) {
        result.error := "Webcam is not active"
        if (statusBar)
            statusBar.SetText(result.error, 1)
        return result
    }

    ; Update status if statusBar is provided
    if (statusBar)
        statusBar.SetText("Capturing image...", 1)

    try {
        ; Generate a filename with timestamp if not provided
        if (outputPath = "") {
            ; Create the images directory if it doesn't exist
            imagesDir := A_ScriptDir "\images"
            if (!DirExist(imagesDir))
                DirCreate(imagesDir)

            ; Generate filename with timestamp
            timestamp := FormatTime(, "yyyyMMdd_HHmmss")
            outputPath := imagesDir "\capture_" timestamp "." imageFormat
        }

        ; Store the filename in the result
        result.filename := outputPath

        ; Get the position and size of the webcam control
        webcamControl.GetPos(&ctrlX, &ctrlY, &ctrlW, &ctrlH)

        ; Convert control coordinates to screen coordinates
        point := Buffer(8)
        NumPut("Int", ctrlX, point, 0)
        NumPut("Int", ctrlY, point, 4)
        DllCall("ClientToScreen", "Ptr", webcamControl.Gui.Hwnd, "Ptr", point)
        screenX := NumGet(point, 0, "Int")
        screenY := NumGet(point, 4, "Int")

        ; Initialize GDI+
        gdipToken := InitGDIPlus()

        ; Check if GDI+ initialization was successful
        if (!gdipToken) {
            result.error := "Failed to initialize GDI+"
            if (statusBar)
                statusBar.SetText(result.error, 1)
            return result
        }

        ; Create a bitmap to hold the screenshot
        DllCall("gdiplus\GdipCreateBitmapFromScan0", "Int", ctrlW, "Int", ctrlH, "Int", 0, "Int", 0x26200A, "Ptr", 0, "Ptr*", &pBitmap:=0)

        ; Get the graphics context from the bitmap
        DllCall("gdiplus\GdipGetImageGraphicsContext", "Ptr", pBitmap, "Ptr*", &pGraphics:=0)

        ; Get the device context of the screen
        hScreenDC := DllCall("GetDC", "Ptr", 0, "Ptr")

        ; Create a memory device context compatible with the screen
        hMemDC := DllCall("CreateCompatibleDC", "Ptr", hScreenDC, "Ptr")

        ; Create a bitmap compatible with the screen
        hBitmap := DllCall("CreateCompatibleBitmap", "Ptr", hScreenDC, "Int", ctrlW, "Int", ctrlH, "Ptr")

        ; Select the bitmap into the memory device context
        hOldBitmap := DllCall("SelectObject", "Ptr", hMemDC, "Ptr", hBitmap, "Ptr")

        ; Copy the screen to the memory device context
        DllCall("BitBlt", "Ptr", hMemDC, "Int", 0, "Int", 0, "Int", ctrlW, "Int", ctrlH, "Ptr", hScreenDC, "Int", screenX, "Int", screenY, "UInt", 0x00CC0020) ; SRCCOPY

        ; Create a GDI+ bitmap from the GDI bitmap
        DllCall("gdiplus\GdipCreateBitmapFromHBITMAP", "Ptr", hBitmap, "Ptr", 0, "Ptr*", &pBitmapFromScreen:=0)

        ; Create a CLSID for the appropriate encoder
        CLSID := Buffer(16)

        ; Select the correct encoder based on the image format
        if (imageFormat = "jpg" || imageFormat = "jpeg") {
            ; JPEG encoder CLSID
            DllCall("ole32\CLSIDFromString", "WStr", "{557CF401-1A04-11D3-9A73-0000F81EF32E}", "Ptr", CLSID)
        } else {
            ; Default to PNG encoder CLSID
            DllCall("ole32\CLSIDFromString", "WStr", "{557CF406-1A04-11D3-9A73-0000F81EF32E}", "Ptr", CLSID)
        }

        ; Save the bitmap to a file
        DllCall("gdiplus\GdipSaveImageToFile", "Ptr", pBitmapFromScreen, "WStr", outputPath, "Ptr", CLSID, "Ptr", 0)

        ; Check if the file was created
        if (FileExist(outputPath)) {
            result.success := true
            if (statusBar)
                statusBar.SetText("Image saved: " outputPath " (Open manually to view)", 1)
        } else {
            result.error := "Failed to save image"
            if (statusBar)
                statusBar.SetText(result.error, 1)
        }

    } catch as err {
        result.error := "Error saving image: " err.Message
        if (statusBar)
            statusBar.SetText(result.error, 1)
    } finally {
        ; Clean up GDI+ resources - always execute this code

        ; Clean up GDI+ resources
        if (pBitmapFromScreen) {
            try {
                DllCall("gdiplus\GdipDisposeImage", "Ptr", pBitmapFromScreen)
            }
        }

        if (pGraphics) {
            try {
                DllCall("gdiplus\GdipDeleteGraphics", "Ptr", pGraphics)
            }
        }

        if (pBitmap) {
            try {
                DllCall("gdiplus\GdipDisposeImage", "Ptr", pBitmap)
            }
        }

        ; Clean up GDI resources
        if (hMemDC && hOldBitmap) {
            try {
                DllCall("SelectObject", "Ptr", hMemDC, "Ptr", hOldBitmap)
            }
        }

        if (hBitmap) {
            try {
                DllCall("DeleteObject", "Ptr", hBitmap)
            }
        }

        if (hMemDC) {
            try {
                DllCall("DeleteDC", "Ptr", hMemDC)
            }
        }

        if (hScreenDC) {
            try {
                DllCall("ReleaseDC", "Ptr", 0, "Ptr", hScreenDC)
            }
        }

        ; Skip GDI+ shutdown in the capture function to prevent crashes
        ; The system will clean up GDI+ resources when the application exits
        if (gdipToken) {
            gdipToken := 0  ; Clear the token to prevent accidental shutdown attempts
        }
    }

    return result
}

/**
 * ShowControl - Shows a GUI control using Windows API
 *
 * This function makes a GUI control visible using the Windows API ShowWindow function.
 * It's more reliable than using the Visible property or Opt method in some cases.
 *
 * @param {Object} control - The GUI control to show
 * @param {Boolean} debug - Whether to output debug information (default: false)
 * @returns {Boolean} - True if successful, false otherwise
 *
 * @example
 * ; Show a control
 * ShowControl(myPictureControl)
 */
ShowControl(control, debug := false) {
    if (!IsObject(control) || !control.HasProp("Hwnd") || !control.Hwnd) {
        if (debug)
            OutputDebug("ShowControl: Invalid control")
        return false
    }

    try {
        ; SW_SHOW = 5
        DllCall("ShowWindow", "Ptr", control.Hwnd, "Int", 5)

        if (debug)
            OutputDebug("ShowControl: Showing control with handle: " control.Hwnd)

        return true
    } catch as err {
        if (debug)
            OutputDebug("ShowControl: Error - " err.Message)
        return false
    }
}

/**
 * HideControl - Hides a GUI control using Windows API
 *
 * This function hides a GUI control using the Windows API ShowWindow function.
 * It's more reliable than using the Visible property or Opt method in some cases.
 *
 * @param {Object} control - The GUI control to hide
 * @param {Boolean} debug - Whether to output debug information (default: false)
 * @returns {Boolean} - True if successful, false otherwise
 *
 * @example
 * ; Hide a control
 * HideControl(myPictureControl)
 */
HideControl(control, debug := false) {
    if (!IsObject(control) || !control.HasProp("Hwnd") || !control.Hwnd) {
        if (debug)
            OutputDebug("HideControl: Invalid control")
        return false
    }

    try {
        ; SW_HIDE = 0
        DllCall("ShowWindow", "Ptr", control.Hwnd, "Int", 0)

        if (debug)
            OutputDebug("HideControl: Hiding control with handle: " control.Hwnd)

        return true
    } catch as err {
        if (debug)
            OutputDebug("HideControl: Error - " err.Message)
        return false
    }
}

/**
 * ToggleWebcamAndCapturedImage - Toggles visibility between webcam feed and captured image
 *
 * This function toggles the visibility of two controls - typically a webcam feed control
 * and a captured image control. It's used to switch between showing the live webcam feed
 * and a captured image.
 *
 * @param {Object} webcamControl - The GUI control displaying the webcam feed
 * @param {Object} capturedImageControl - The GUI control displaying the captured image
 * @param {Boolean} showWebcam - Whether to show the webcam feed (true) or the captured image (false)
 * @param {Boolean} debug - Whether to output debug information (default: false)
 * @returns {Boolean} - True if successful, false otherwise
 *
 * @example
 * ; Show webcam feed
 * ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true)
 *
 * ; Show captured image
 * ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, false)
 */
ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, showWebcam, debug := false) {
    if (!IsObject(webcamControl) || !IsObject(capturedImageControl)) {
        if (debug)
            OutputDebug("ToggleWebcamAndCapturedImage: Invalid controls")
        return false
    }

    try {
        if (debug)
            OutputDebug("ToggleWebcamAndCapturedImage: " (showWebcam ? "Showing webcam" : "Showing captured image"))

        if (showWebcam) {
            ; Show webcam feed, hide captured image
            ShowControl(webcamControl, debug)
            HideControl(capturedImageControl, debug)

            ; Clear any text in the webcam control
            if (webcamControl.HasProp("Text"))
                webcamControl.Text := "No Camera Feed"
        } else {
            ; Hide webcam feed, show captured image
            HideControl(webcamControl, debug)
            ShowControl(capturedImageControl, debug)

            ; Clear any text in the webcam control
            if (webcamControl.HasProp("Text"))
                webcamControl.Text := ""
        }

        return true
    } catch as err {
        if (debug)
            OutputDebug("ToggleWebcamAndCapturedImage: Error - " err.Message)
        return false
    }
}

/**
 * DetectCameras - Detects available camera devices using FFmpeg
 *
 * This function uses FFmpeg to detect all available webcams on the system.
 * It returns an array of camera names that can be used with StartWebcam.
 *
 * @param {Boolean} debug - Whether to output debug information (default: false)
 * @returns {Array} - Array of camera names
 *
 * @example
 * ; Get a list of available cameras
 * cameras := DetectCameras()
 * for i, camera in cameras {
 *     MsgBox("Camera " i ": " camera)
 * }
 */
DetectWebCameras(debug := false) {
    cameras := []

    ; Use FFmpeg to list DirectShow devices
    ffmpegPath := A_ScriptDir "\bin\ffmpeg.exe"
    if (!FileExist(ffmpegPath)) {
        if (debug)
            OutputDebug("DetectCameras: FFmpeg executable not found at: " ffmpegPath)
        return cameras
    }

    ; Create a temporary file to store the output
    tempFile := A_Temp "\camera_list.txt"

    ; Ensure the temporary file doesn't exist
    if (FileExist(tempFile))
        FileDelete(tempFile)

    ; Run FFmpeg with the DirectShow device list command
    ffmpegCmd := ffmpegPath . " -list_devices true -f dshow -i dummy"
    cmdLine := A_ComSpec . " /c " . ffmpegCmd . " > `"" . tempFile . "`" 2>&1"

    try {
        ; Run the command and wait for it to complete
        if (debug)
            OutputDebug("DetectCameras: Running command: " cmdLine)
        RunWait(cmdLine, , "Hide")

        ; Read the output file
        if (FileExist(tempFile)) {
            fileContent := FileRead(tempFile)

            ; Parse the output to find video devices
            Loop Parse, fileContent, "`n", "`r" {
                ; Look for lines with "(video)" - this is the format in FFmpeg output
                if (InStr(A_LoopField, "(video)")) {
                    ; Extract the camera name between quotes
                    startPos := InStr(A_LoopField, "`"")
                    if (startPos > 0) {
                        endPos := InStr(A_LoopField, "`"", false, startPos + 1)
                        if (endPos > 0) {
                            videoDeviceName := SubStr(A_LoopField, startPos + 1, endPos - startPos - 1)

                            ; Check if this camera is already in our list
                            alreadyAdded := false
                            for cam in cameras {
                                if (cam = videoDeviceName) {
                                    alreadyAdded := true
                                    break
                                }
                            }

                            ; Add the camera if it's not already in the list
                            if (!alreadyAdded) {
                                cameras.Push(videoDeviceName)
                                if (debug)
                                    OutputDebug("DetectCameras: Found camera: " videoDeviceName)
                            }
                        }
                    }
                }
            }

            ; Delete the temporary file
            try {
                FileDelete(tempFile)
            } catch Error as e {
                if (debug)
                    OutputDebug("DetectCameras: Error deleting temporary file: " e.Message)
            }
        }
    } catch Error as e {
        if (debug)
            OutputDebug("DetectCameras: Error detecting cameras: " e.Message)
    }

    ; Log the results
    if (debug) {
        OutputDebug("DetectCameras: Found " cameras.Length " cameras")
        for i, camera in cameras {
            OutputDebug("DetectCameras: Camera " i ": " camera)
        }
    }

    return cameras
}