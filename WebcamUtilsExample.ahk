#Requires AutoHotkey v2.0+
#SingleInstance Force
/*
WebcamUtilsExample.ahk
Example script demonstrating how to use the webcam_utils.ahk library

Features:
- Start/stop webcam feed
- Capture images from the webcam
- Display captured images in place of live feed
- Read camera name from INI file

This example demonstrates the use of the webcam_utils.ahk library, which provides
functions for webcam initialization, control, image capture, and display.
*/

; Include the webcam_utils library
#Include lib\webcam_utils.ahk

; Global variables
global configFile := A_ScriptDir "\WebcamUtilsExample.ini"
global cameraName := ""        ; Currently selected camera name
global myGui := 0
global webcamControl := 0      ; Control for live webcam feed
global capturedImageControl := 0  ; Control for displaying captured image
global cameraCombo := 0        ; ComboBox for camera selection
global refreshBtn := 0         ; Button to refresh camera list
global capHwnd := 0
global isWebcamActive := false
global dismissDialogsTimer := 0
global capturedImagePath := ""  ; Path to the most recently captured image
global statusBar := 0
global startBtn := 0
global cameras := []           ; Array of detected cameras
global hModule := 0            ; Handle to the avicap32.dll library

; Documentation on how to use the webcam_utils.ahk library:
/*
The webcam_utils.ahk library provides functions for webcam initialization, control,
image capture, and display. Here's how to use it in your own applications:

1. Include the library:
   #Include lib\webcam_utils.ahk

2. Create two overlapping controls:
   - A Text control for the webcam feed
   - A Picture control for displaying captured images

   Example:
   webcamControl := myGui.AddText("x10 y10 w640 h480 +Border +0x200", "No Camera Feed")
   capturedImageControl := myGui.AddPicture("x10 y10 w640 h480 +Border", "")

   Then hide the captured image control:
   HideControl(capturedImageControl)

3. Start the webcam:
   capHwnd := StartWebcam(webcamControl, cameraName)

   Store the returned handle for later use.

4. Capture an image:
   result := CaptureWebcamImage(webcamControl, capHwnd)

   Check if the capture was successful:
   if (result.success) {
       capturedImagePath := result.filename
   }

5. Display the captured image:
   capturedImageControl.Value := capturedImagePath
   ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, false)

6. Switch back to the webcam feed:
   ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true)

7. Stop the webcam when done:
   StopWebcam(capHwnd)

See the functions below for examples of how to use these functions in practice.
*/

; Initialize the application
Init()

/**
 * Main initialization function
 *
 * This function initializes the application by:
 * 1. Loading the Video for Windows library (avicap32.dll)
 * 2. Loading camera settings from the INI file
 * 3. Creating the GUI with webcam and captured image controls
 * 4. Registering cleanup on exit
 * 5. Starting the webcam after a short delay
 */
Init() {
    global hModule

    ; Load the Video for Windows library
    hModule := DllCall("LoadLibrary", "Str", "avicap32.dll")
    if (!hModule) {
        MsgBox("Failed to load avicap32.dll. This is required for webcam functionality.", "Error", "Icon! 262144")
        ExitApp()
    }

    OutputDebug("Init: Loaded avicap32.dll with handle: " hModule)

    ; Load camera settings from INI file
    LoadSettings()

    ; Create the GUI first to ensure controls are initialized
    CreateGUI()

    ; Register cleanup on exit
    OnExit(ExitHandler)
	myGui.OnEvent("Close", (*) => ExitHandler())

    ; Wait a moment for GUI to initialize completely
    SetTimer(StartWebcamDelayed, 100)
}

/**
 * Function to start webcam after a short delay
 *
 * This function is called by a timer to start the webcam after a short delay,
 * allowing the GUI to fully initialize before starting the webcam.
 */
StartWebcamDelayed(*) {
    ; Clear the timer
    SetTimer(StartWebcamDelayed, 0)

    ; Refresh the camera list first
    RefreshWebcams()

    ; If we have a saved camera, start the webcam
    if (cameraName != "") {
        StartWebcamAuto()
    }
}

/**
 * Function to auto-start the webcam
 *
 * This function automatically starts the webcam using the camera name from the INI file.
 * It shows the webcam control, hides the captured image control, and starts the webcam.
 * It also sets up a timer to automatically dismiss any dialogs that appear.
 */
StartWebcamAuto() {
    global webcamControl, capturedImageControl, capHwnd, isWebcamActive, dismissDialogsTimer, cameraName, statusBar

    ; Don't start if already active
    if (isWebcamActive) {
        return
    }

    ; Check if we have a valid camera name
    if (cameraName = "") {
        statusBar.SetText("No camera selected. Please select a camera from the dropdown.", 1)
        return
    }

    ; Update status
    statusBar.SetText("Starting webcam...", 1)
    SetTimer(DismissDialogsCallback, 100)

    ; Make sure the webcam control is visible and captured image is hidden
    ; Use the library function to toggle visibility
    ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true, true)

    ; Start the webcam
    capHwnd := StartWebcam(webcamControl, cameraName)

    if (capHwnd) {
        ; Mark webcam as active
        isWebcamActive := true

        ; Set up timer for dialog dismissal
        SetTimer(DismissDialogsCallback, 50)

        ; Update status
        statusBar.SetText("Webcam active", 1)
    } else {
        statusBar.SetText("Failed to auto-start webcam", 1)
    }
}

/**
 * Load settings from INI file
 *
 * This function loads the camera name from the INI file.
 * If the file doesn't exist, it creates it with default settings.
 */
LoadSettings() {
    global cameraName, configFile

    ; Create default settings if the file doesn't exist
    if (!FileExist(configFile)) {
        IniWrite("", configFile, "Camera", "CameraName")
    }

    ; Read camera name from INI file
    cameraName := IniRead(configFile, "Camera", "CameraName", "")
}

/**
 * Save the selected camera to INI file
 *
 * This function saves the currently selected camera name to the INI file.
 *
 * @param {String} camera - The camera name to save
 */
SaveSelectedCamera(camera) {
    global cameraName, configFile, statusBar

    if (camera && camera != "" && camera != "No webcams detected" && camera != "Click Refresh to detect cameras") {
        ; Save to INI file
        try {
            IniWrite(camera, configFile, "Camera", "CameraName")
            cameraName := camera
            statusBar.SetText("Camera saved: " camera, 1)
            OutputDebug("Camera saved to INI: " camera)
        } catch as err {
            statusBar.SetText("Error saving camera: " err.Message, 1)
            OutputDebug("Error saving camera: " err.Message)
        }
    } else {
        statusBar.SetText("No valid camera selected", 1)
        OutputDebug("No valid camera to save")
    }
}

/**
 * Refresh the list of available webcams
 *
 * This function refreshes the list of available webcams in the dropdown.
 * It detects all available webcams and adds them to the dropdown.
 * If the previously saved camera is found, it is selected.
 *
 * @param {Any} params - Event parameters (not used)
 */
RefreshWebcams(*) {
    global myGui, cameraCombo, cameraName, cameras, statusBar

    ; Update status
    statusBar.SetText("Detecting webcams...", 1)

    ; Disable the combo box and refresh button during detection
    cameraCombo.Enabled := false
    refreshBtn.Enabled := false

    ; Clear existing items
    cameraCombo.Delete()

    ; Add a loading indicator
    cameraCombo.Add(["Detecting cameras... Please wait..."])
    cameraCombo.Choose(1)

    ; Force GUI update to show the loading indicator
    Sleep(50)

    ; Detect available webcams
    cameras := DetectWebCameras(true)

    ; Clear the loading indicator
    cameraCombo.Delete()

    ; Add cameras to dropdown
    if (cameras.Length > 0) {
        selectedIndex := 1  ; Default to first camera

        for i, camera in cameras {
            cameraCombo.Add([camera])

            ; Check if this is the saved camera
            if (camera = cameraName) {
                selectedIndex := i
                OutputDebug("Found saved camera at index " i)
            }
        }

        ; Select the saved camera or the first one
        cameraCombo.Choose(selectedIndex)

        ; Update status
        statusBar.SetText("Found " cameras.Length " webcams", 1)
    } else {
        ; No cameras found
        cameraCombo.Add(["No webcams detected"])
        cameraCombo.Choose(1)
        statusBar.SetText("No webcams detected", 1)
    }

    ; Re-enable the controls
    cameraCombo.Enabled := true
    refreshBtn.Enabled := true
}

/**
 * Create the main GUI
 *
 * This function creates the main GUI with:
 * - Camera selection dropdown with refresh button
 * - Webcam feed placeholder (Text control)
 * - Captured image control (Picture control) overlaid on the webcam feed
 * - Control buttons for starting/stopping the webcam and capturing images
 * - Status bar for displaying status messages
 *
 * The captured image control is initially hidden using the HideControl function
 * from the webcam_utils.ahk library.
 */
CreateGUI() {
    global myGui, webcamControl, capturedImageControl, statusBar, cameraName, startBtn
    global cameraCombo, refreshBtn, cameras

    ; Create the main GUI with fixed size
    myGui := Gui("+Resize", "Webcam Utils Example")
    myGui.SetFont("s10")
    myGui.MarginX := 10
    myGui.MarginY := 10

    ; Add camera selection dropdown
    cameraLabel := myGui.AddText("x10 y10 w100", "Camera:")
    cameraCombo := myGui.AddComboBox("x120 y10 w300 vCameraCombo")

    ; Add refresh button
    refreshBtn := myGui.AddButton("x430 y10 w100 h24 +0x1000", "Refresh")
    refreshBtn.OnEvent("Click", RefreshWebcams)

    ; Add event handler for camera selection change
    cameraCombo.OnEvent("Change", CameraSelectionChanged)

    ; Initialize the dropdown with a placeholder
    cameraCombo.Add(["Click Refresh to detect cameras"])
    cameraCombo.Choose(1)

    ; Add webcam feed placeholder
    feedLabel := myGui.AddText("x10 y40 w200", "Webcam Feed:")

    ; Create a placeholder for the webcam feed
    webcamControl := myGui.AddText("x10 y70 w640 h360 +Border +0x200 BackgroundSilver +Center", "No Camera Feed")

    ; Create an overlay picture control for displaying captured images
    capturedImageControl := myGui.AddPicture("x10 y70 w640 h360 +Border", "")

    ; Hide the captured image control using the library function
    HideControl(capturedImageControl, true)

    ; Add control buttons with 3D styling
    startBtn := myGui.AddButton("x10 y440 w150 h30 +0x1000", "Start Webcam")
    startBtn.OnEvent("Click", StartWebcamClick)

    stopBtn := myGui.AddButton("x170 y440 w150 h30 +0x1000", "Stop Webcam")
    stopBtn.OnEvent("Click", StopWebcamClick)

    captureBtn := myGui.AddButton("x330 y440 w150 h30 +0x1000", "Capture Image")
    captureBtn.OnEvent("Click", CaptureImage)

    exitBtn := myGui.AddButton("x490 y440 w150 h30 +0x1000", "Exit")
    exitBtn.OnEvent("Click", (*) => ExitApp())

    ; Add status bar
    statusBar := myGui.AddStatusBar()
    statusBar.SetParts(300, 300)
    statusBar.SetText("Ready", 1)
    statusBar.SetText("Camera: " (cameraName ? cameraName : "None"), 2)

    ; Show the GUI with fixed size
    myGui.Show("w660 h520")
}

/**
 * Event handler for camera selection change
 *
 * This function is called when the user selects a different camera from the dropdown.
 * It stops the current webcam feed if active, updates the camera name, and starts
 * the webcam with the newly selected camera. It also saves the selected camera to
 * the INI file.
 *
 * @param {Object} ctrl - The control that triggered the event
 * @param {String} info - Additional information about the event
 */
CameraSelectionChanged(ctrl, info) {
    global cameraCombo, cameraName, capHwnd, isWebcamActive, statusBar
    global webcamControl, capturedImageControl, dismissDialogsTimer, startBtn, hModule

    ; Get the selected camera
    selectedCamera := cameraCombo.Text

    ; Skip if no valid camera is selected
    if (selectedCamera = "" || selectedCamera = "No webcams detected" || selectedCamera = "Click Refresh to detect cameras" || selectedCamera = "Detecting cameras... Please wait...") {
        return
    }

    ; Skip if the selected camera is the same as the current one
    if (selectedCamera = cameraName) {
        return
    }

    ; Update status
    statusBar.SetText("Switching to camera: " selectedCamera, 1)
    OutputDebug("Switching to camera: " selectedCamera)

    ; Completely stop the current webcam if active
    if (isWebcamActive && capHwnd) {
        ; Stop dialog dismissal timer
        try {
            SetTimer(DismissDialogsCallback, 0)
        } catch {
            ; Ignore errors
        }

        ; Stop the webcam directly (not using the click handler)
        if (StopWebcam(capHwnd)) {
            ; Mark webcam as inactive
            isWebcamActive := false
            capHwnd := 0

            ; Reset the button text
            startBtn.Text := "Start Webcam"

            ; Reset webcam control text
            webcamControl.Text := "Switching cameras..."

            OutputDebug("Successfully stopped previous webcam")
        } else {
            OutputDebug("Failed to stop previous webcam")
        }
    }

    ; Update the camera name
    cameraName := selectedCamera

    ; Save the selected camera to INI file
    SaveSelectedCamera(cameraName)

    ; Update status bar
    statusBar.SetText("Camera: " cameraName, 2)

    ; Short delay to ensure resources are released
    Sleep(100)

    ; Unload and reload the avicap32.dll library to force the Video Source dialog to appear
    try {
        ; First, get the module handle
        if (hModule := DllCall("GetModuleHandle", "Str", "avicap32.dll")) {
            ; Free the library
            OutputDebug("Unloading avicap32.dll library")
            DllCall("FreeLibrary", "Ptr", hModule)

            ; Short delay to ensure the library is fully unloaded
            Sleep(100)
        }

        ; Reload the library
        OutputDebug("Reloading avicap32.dll library")
        hModule := DllCall("LoadLibrary", "Str", "avicap32.dll")
        if (!hModule) {
            OutputDebug("Failed to reload avicap32.dll library")
            statusBar.SetText("Error: Failed to reload camera library", 1)
        } else {
            OutputDebug("Successfully reloaded avicap32.dll library")
        }
    } catch as err {
        OutputDebug("Error during library reload: " err.Message)
    }

    ; Make sure the webcam control is visible and captured image is hidden
    ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true, true)

    ; Start the webcam with the new camera
    statusBar.SetText("Starting new camera: " cameraName, 1)
    SetTimer(DismissDialogsCallback, 100)

    ; Start the webcam directly (not using the click handler)
    capHwnd := StartWebcam(webcamControl, cameraName)

    ; If the webcam started successfully, explicitly show the Video Source dialog
    if (capHwnd) {
        ; Define message constants
        WM_CAP := 0x400
        WM_CAP_DLG_VIDEOSOURCE := WM_CAP + 42

        ; Show the Video Source dialog
        OutputDebug("Explicitly showing Video Source dialog")
        SendMessage(WM_CAP_DLG_VIDEOSOURCE, 0, 0, , "ahk_id " capHwnd)

        ; Give time for the dialog to appear and be handled
        Sleep(500)
    }

    if (capHwnd) {
        ; Mark webcam as active
        isWebcamActive := true

        ; Set up timer for dialog dismissal
        SetTimer(DismissDialogsCallback, 50)

        ; Update status
        statusBar.SetText("Camera switched successfully", 1)
        OutputDebug("Successfully started new webcam")
    } else {
        statusBar.SetText("Failed to start new camera", 1)
        OutputDebug("Failed to start new webcam")
    }
}

/**
 * Event handler for Start/Restart Webcam button
 *
 * This function is called when the user clicks the Start/Restart Webcam button.
 * It shows the webcam control, hides the captured image control, and starts the webcam.
 * It also sets up a timer to automatically dismiss any dialogs that appear.
 *
 * @param {Any} params - Event parameters (not used)
 */
StartWebcamClick(*) {
    global webcamControl, capturedImageControl, capHwnd, isWebcamActive, dismissDialogsTimer, cameraName, statusBar, startBtn, cameraCombo, hModule

    ; Don't start if already active
    if (isWebcamActive) {
        statusBar.SetText("Webcam is already active", 1)
        return
    }

    ; Get the selected camera from the dropdown
    selectedCamera := cameraCombo.Text

    ; Check if we have a valid camera
    if (selectedCamera = "" || selectedCamera = "No webcams detected" || selectedCamera = "Click Refresh to detect cameras" || selectedCamera = "Detecting cameras... Please wait...") {
        statusBar.SetText("No valid camera selected. Please select a camera from the dropdown.", 1)
        return
    }

    ; Update the camera name if it's different
    if (selectedCamera != cameraName) {
        cameraName := selectedCamera
        SaveSelectedCamera(cameraName)
        statusBar.SetText("Camera: " cameraName, 2)
    }

    ; Update status
    statusBar.SetText("Starting webcam...", 1)
    SetTimer(DismissDialogsCallback, 100)

    ; Reset the button text to "Start Webcam"
    startBtn.Text := "Start Webcam"

    ; Unload and reload the avicap32.dll library to force the Video Source dialog to appear
    try {
        ; First, get the module handle
        if (hModule := DllCall("GetModuleHandle", "Str", "avicap32.dll")) {
            ; Free the library
            OutputDebug("StartWebcamClick: Unloading avicap32.dll library")
            DllCall("FreeLibrary", "Ptr", hModule)

            ; Short delay to ensure the library is fully unloaded
            Sleep(100)
        }

        ; Reload the library
        OutputDebug("StartWebcamClick: Reloading avicap32.dll library")
        hModule := DllCall("LoadLibrary", "Str", "avicap32.dll")
        if (!hModule) {
            OutputDebug("StartWebcamClick: Failed to reload avicap32.dll library")
            statusBar.SetText("Error: Failed to reload camera library", 1)
        } else {
            OutputDebug("StartWebcamClick: Successfully reloaded avicap32.dll library")
        }
    } catch as err {
        OutputDebug("StartWebcamClick: Error during library reload: " err.Message)
    }

    ; Hide the captured image control and show the webcam control
    ; Use the library function to toggle visibility
    ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true, true)

    ; Start the webcam
    capHwnd := StartWebcam(webcamControl, cameraName)

    ; If the webcam started successfully, explicitly show the Video Source dialog
    if (capHwnd) {
        ; Define message constants
        WM_CAP := 0x400
        WM_CAP_DLG_VIDEOSOURCE := WM_CAP + 42

        ; Show the Video Source dialog
        OutputDebug("StartWebcamClick: Explicitly showing Video Source dialog")
        SendMessage(WM_CAP_DLG_VIDEOSOURCE, 0, 0, , "ahk_id " capHwnd)

        ; Give time for the dialog to appear and be handled
        Sleep(500)
    }

    if (capHwnd) {
        ; Mark webcam as active
        isWebcamActive := true

        ; Set up timer for dialog dismissal
        SetTimer(DismissDialogsCallback, 50)

        ; Update status
        statusBar.SetText("Webcam active", 1)
    } else {
        statusBar.SetText("Failed to start webcam", 1)
    }
}

/**
 * Event handler for Stop Webcam button
 *
 * This function is called when the user clicks the Stop Webcam button.
 * It stops the webcam, updates the status, and resets the button text.
 *
 * @param {Any} params - Event parameters (not used)
 */
StopWebcamClick(*) {
    global capHwnd, isWebcamActive, dismissDialogsTimer, statusBar, startBtn, webcamControl

    ; Don't stop if not active
    if (!isWebcamActive) {
        statusBar.SetText("Webcam is not active", 1)
        return
    }

    ; Update status
    statusBar.SetText("Stopping webcam...", 1)

    ; Stop dialog dismissal timer
    try {
        SetTimer(DismissDialogsCallback, 0)
    } catch {
        ; Ignore errors
    }

    ; Stop the webcam
    if (StopWebcam(capHwnd)) {
        ; Mark webcam as inactive
        isWebcamActive := false
        capHwnd := 0

        ; Reset the button text to "Start Webcam"
        startBtn.Text := "Start Webcam"

        ; Update webcam control text to indicate it's stopped
        webcamControl.Text := "Webcam Stopped"

        ; Update status
        statusBar.SetText("Webcam stopped", 1)
    } else {
        statusBar.SetText("Failed to stop webcam", 1)
    }
}

/**
 * Function to capture an image from the webcam
 *
 * This function captures an image from the webcam using the CaptureWebcamImage function
 * from the webcam_utils.ahk library. If the capture is successful, it:
 * 1. Stops the webcam to free resources
 * 2. Sets the captured image to the captured image control
 * 3. Toggles visibility to show the captured image and hide the webcam feed
 * 4. Changes the Start Webcam button text to Restart Webcam
 *
 * @param {Any} params - Event parameters (not used)
 */
CaptureImage(*) {
    global capHwnd, isWebcamActive, statusBar, webcamControl, capturedImageControl, startBtn, capturedImagePath

    ; Don't capture if webcam is not active
    if (!isWebcamActive || !capHwnd) {
        statusBar.SetText("Webcam is not active", 1)
        return
    }

    ; Call the consolidated function from webcam_utils.ahk
    result := CaptureWebcamImage(webcamControl, capHwnd, "", "png", statusBar)

    ; If image was captured successfully
    if (result.success) {
        ; Store the captured image path
        capturedImagePath := result.filename

        ; Stop the webcam to free resources
        StopWebcam(capHwnd)

        ; Mark webcam as inactive
        isWebcamActive := false
        capHwnd := 0

        ; Display the captured image using the overlay approach
        try {
            ; Set the image to the captured image control
            capturedImageControl.Value := capturedImagePath

            ; Use the library function to toggle visibility
            ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, false, true)

            ; Change the Start Webcam button text to Restart Webcam
            startBtn.Text := "Restart Webcam"

            ; Update status
            statusBar.SetText("Image captured and displayed. Click 'Restart Webcam' to resume live feed.", 1)
        } catch as err {
            statusBar.SetText("Error displaying image: " err.Message, 1)
        }
    }
}

/**
 * Function to automatically dismiss any dialog boxes that appear
 *
 * This function is called by a timer to automatically dismiss any dialog boxes
 * that appear during webcam initialization or operation. It calls the DismissDialogs
 * function from the webcam_utils.ahk library.
 *
 * @param {Any} params - Event parameters (not used)
 */
DismissDialogsCallback(*) {
    global cameraName

    ; Call the DismissDialogs function directly
    DismissDialogs(cameraName)
}

/**
 * Clean up when exiting
 *
 * This function is called when the application is exiting. It:
 * 1. Stops the dialog dismissal timer
 * 2. Stops the webcam
 * 3. Frees the avicap32.dll library
 * 4. Optionally cleans up temporary captured images
 *
 * @param {Any} params - Event parameters (not used)
 */
ExitHandler(*) {
    global capHwnd, dismissDialogsTimer, capturedImagePath

    ; Stop dialog dismissal timer
    try {
        SetTimer(DismissDialogsCallback, 0)
    } catch {
        ; Ignore errors
    }

    ; Stop the webcam
    if (capHwnd) {
        try {
            StopWebcam(capHwnd)
        } catch {
            ; Ignore errors
        }
    }

    ; Free the avicap32.dll library
    try {
        if (hModule := DllCall("GetModuleHandle", "Str", "avicap32.dll")) {
            DllCall("FreeLibrary", "Ptr", hModule)
        }
    } catch {
        ; Ignore errors
    }

    ; Clean up temporary captured image if it exists
    try {
        if (capturedImagePath && FileExist(capturedImagePath)) {
            ; Optionally delete temporary captured image
            ; FileDelete(capturedImagePath)
        }
    } catch {
        ; Ignore errors
    }

    ; Let the system handle GDI+ cleanup
}


