# #DllLoad

[Loads](DllCall.htm#load) a DLL or EXE file before the script starts
executing.

``` Syntax
#DllLoad FileOrDirName
```

## Parameters {#Parameters}

FileOrDirName

:   Type: [String](../Concepts.htm#strings)

    The path of a file or directory as explained below. This must
    [not]{.underline} contain double quotes (except for an optional pair
    of double quotes surrounding the parameter), wildcards
    (`*`{.no-highlight} and `?`{.no-highlight}) or [escape
    sequences](../misc/EscapeChar.htm) other than semicolon
    (`` `; ``{.no-highlight}).

    Built-in variables may be used by enclosing them in percent signs
    (for example, `#DllLoad "%A_ScriptDir%"`). Percent signs which are
    not part of a valid variable reference are interpreted literally.
    All built-in variables are valid, except for
    [A_Args](../Variables.htm#Args) and built-in classes.

    Known limitation: When compiling a script, variables are evaluated
    by the compiler and may differ from what the script would return
    when it is finally executed. The following variables are supported:
    [A_AhkPath](../Variables.htm#AhkPath),
    [A_AppData](../Variables.htm#AppData),
    [A_AppDataCommon](../Variables.htm#AppDataCommon),
    [A_ComputerName](../Variables.htm#ComputerName),
    [A_ComSpec](../Variables.htm#ComSpec),
    [A_Desktop](../Variables.htm#Desktop),
    [A_DesktopCommon](../Variables.htm#DesktopCommon),
    [A_IsCompiled](../Variables.htm#IsCompiled),
    [A_LineFile](../Variables.htm#LineFile),
    [A_MyDocuments](../Variables.htm#MyDocuments),
    [A_ProgramFiles](../Variables.htm#ProgramFiles),
    [A_Programs](../Variables.htm#Programs),
    [A_ProgramsCommon](../Variables.htm#ProgramsCommon),
    [A_ScriptDir](../Variables.htm#ScriptDir),
    [A_ScriptFullPath](../Variables.htm#ScriptFullPath),
    [A_ScriptName](../Variables.htm#ScriptName),
    [A_Space](../Variables.htm#Space),
    [A_StartMenu](../Variables.htm#StartMenu),
    [A_StartMenuCommon](../Variables.htm#StartMenuCommon),
    [A_Startup](../Variables.htm#Startup),
    [A_StartupCommon](../Variables.htm#StartupCommon),
    [A_Tab](../Variables.htm#Tab), [A_Temp](../Variables.htm#Temp),
    [A_UserName](../Variables.htm#UserName),
    [A_WinDir](../Variables.htm#WinDir).

    **File:** The absolute or relative path to the DLL or EXE file to be
    loaded. If a relative path is specified, the directive searches for
    the file using the same search strategy as the system\'s function
    [LoadLibraryW](https://learn.microsoft.com/windows/win32/api/libloaderapi/nf-libloaderapi-loadlibraryw).
    Note: [SetWorkingDir](SetWorkingDir.htm) has no effect on #DllLoad
    because #DllLoad is processed before the script begins executing.

    **Directory:** Specify a directory instead of a file to alter the
    search strategy by all subsequent occurrences of #DllLoad which do
    not specify an absolute path to a DLL or EXE. The new search
    strategy is the same as if *Directory* was passed to the system\'s
    function
    [SetDllDirectoryW](https://learn.microsoft.com/windows/win32/api/winbase/nf-winbase-setdlldirectoryw).
    If this parameter is omitted, the default search strategy is
    restored.

    **Note:** This parameter is not an expression, but can be enclosed
    in quote marks (either \'single\' or \"double\").

## Remarks {#Remarks}

Once a DLL or EXE has been loaded by this directive it cannot be
unloaded by calling the system\'s function
[FreeLibrary](https://learn.microsoft.com/windows/win32/api/libloaderapi/nf-libloaderapi-freelibrary).
When the script is terminated, all loaded files are unloaded
automatically.

The file path may optionally be preceded by `*i`{.no-highlight} and a
single space, which causes the program to ignore any failure to load the
file. This option should be used only if the script is capable of
executing despite the failure, such as if the DLL or EXE is
non-essential, or if the script is designed to detect the failure. For
example:

    #DllLoad "*i MyDLL"
    if !DllCall("GetModuleHandle", "str", "MyDLL")
        MsgBox "Failed to load MyDLL!"

If the *FileOrDirName* parameter specifies a DLL name without a path and
the file name extension is omitted, *.dll* is appended to the file name.
To prevent this, include a trailing period (.) in the file name.

Like other directives, #DllLoad cannot be executed conditionally.

## Related {#Related}

[DllCall](DllCall.htm)

## Examples {#Examples}

::: {#ExBasic .ex}
[](#ExBasic){.ex_number} Loads a DLL file located in the current user\'s
\"My Documents\" folder before the script starts executing.

    #DllLoad "%A_MyDocuments%\MyDLL.dll"
:::
