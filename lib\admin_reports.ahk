#Requires AutoHotkey v2.0

; Reports functionality for WinCBT-Admin
; This module handles report generation for the admin application

; Define paths
global REPORTS_DB_PATH := IniRead(A_ScriptDir "\config.ini", "Paths", "dbPath", "db")
; Convert relative to absolute path if needed
if (SubStr(REPORTS_DB_PATH, 1, 1) != "\" && SubStr(REPORTS_DB_PATH, 2, 1) != ":") {
    REPORTS_DB_PATH := A_ScriptDir "\" REPORTS_DB_PATH "\"
} else if (SubStr(REPORTS_DB_PATH, -1) != "\") {
    REPORTS_DB_PATH := REPORTS_DB_PATH "\"
}

; Define database file paths
global REPORTS_CANDIDATES_PATH := REPORTS_DB_PATH "candidates.ini"
global REPORTS_HARDWARE_PATH := REPORTS_DB_PATH "hardware.ini"
global REPORTS_ROOMS_PATH := REPORTS_DB_PATH "rooms.ini"
global REPORTS_SEAT_ASSIGNMENTS_PATH := REPORTS_DB_PATH "seat_assignments.ini"
global REPORTS_OPERATORS_PATH := REPORTS_DB_PATH "operators.ini"
global REPORTS_DIR := REPORTS_DB_PATH "reports\"

; Create reports directory if it doesn't exist
if (!DirExist(REPORTS_DIR)) {
    try {
        DirCreate(REPORTS_DIR)
        ErrorHandler.LogMessage("INFO", "Created reports directory: " REPORTS_DIR)
    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Failed to create reports directory: " err.Message)
    }
}

; ; ReportsManager class
; ; Handles report generation for the admin application
class ReportsManager {
    ; Report types
    static REPORT_TYPES := Map(
        "verification", Map(
            "name", "Verification Report",
            "description", "Report on candidate verification status",
            "fields", ["RollNumber", "Name", "BiometricStatus", "PhotoStatus", "FingerprintStatus", "RightFingerprintStatus", "SignatureStatus"]
        ),
        "seat_assignment", Map(
            "name", "Seat Assignment Report",
            "description", "Report on seat assignments",
            "fields", ["SeatID", "RollNumber", "Name", "AssignedTime", "AssignedBy"]
        ),
        "special_candidates", Map(
            "name", "Special Candidates Report",
            "description", "Report on candidates with special needs",
            "fields", ["RollNumber", "Name", "ThumbPreference", "BiometricStatus"]
        ),
        "verification_statistics", Map(
            "name", "Verification Statistics",
            "description", "Statistical report on verification status",
            "fields", ["TotalCandidates", "VerifiedCandidates", "VerificationRate", "SpecialCandidates"]
        )
    )

    ; Constructor
    __New() {
        ; Nothing to initialize
    }

    ; Generate a verification report
    ; Parameters:
    ; - options: Object with report options
    ;   - outputFormat: Format of the report (csv, html, pdf)
    ;   - filter: Function to filter candidates
    ;   - includeHeaders: Whether to include headers (default: true)
    ; Returns: Object with report results
    GenerateVerificationReport(options := "") {
        ; Initialize results
        results := {
            reportType: "verification",
            totalCandidates: 0,
            includedCandidates: 0,
            outputPath: "",
            errors: []
        }

        ; Set default options
        if (!IsObject(options)) {
            options := {}
        }
        options.outputFormat := options.HasOwnProp("outputFormat") ? options.outputFormat : "csv"
        options.includeHeaders := options.HasOwnProp("includeHeaders") ? options.includeHeaders : true

        try {
            ; Get all candidates
            candidates := this.GetAllCandidates(options.HasOwnProp("filter") ? options.filter : "")
            results.totalCandidates := candidates.Length

            ; Generate report based on format
            if (options.outputFormat = "csv") {
                results.outputPath := this.GenerateCSVReport(candidates, ReportsManager.REPORT_TYPES["verification"]["fields"], options)
            } else if (options.outputFormat = "html") {
                results.outputPath := this.GenerateHTMLReport(candidates, ReportsManager.REPORT_TYPES["verification"]["fields"], options)
            } else if (options.outputFormat = "pdf") {
                results.outputPath := this.GeneratePDFReport(candidates, ReportsManager.REPORT_TYPES["verification"]["fields"], options)
            } else {
                throw Error("Unsupported output format: " options.outputFormat)
            }

            results.includedCandidates := candidates.Length
            return results
        } catch as err {
            results.errors.Push("Error generating verification report: " err.Message)
            return results
        }
    }

    ; Generate a seat assignment report
    ; Parameters:
    ; - options: Object with report options
    ;   - outputFormat: Format of the report (csv, html, pdf)
    ;   - filter: Function to filter seat assignments
    ;   - includeHeaders: Whether to include headers (default: true)
    ; Returns: Object with report results
    GenerateSeatAssignmentReport(options := "") {
        ; Initialize results
        results := {
            reportType: "seat_assignment",
            totalAssignments: 0,
            includedAssignments: 0,
            outputPath: "",
            errors: []
        }

        ; Set default options
        if (!IsObject(options)) {
            options := {}
        }
        options.outputFormat := options.HasOwnProp("outputFormat") ? options.outputFormat : "csv"
        options.includeHeaders := options.HasOwnProp("includeHeaders") ? options.includeHeaders : true

        try {
            ; Get all seat assignments
            seatAssignments := this.GetAllSeatAssignments(options.HasOwnProp("filter") ? options.filter : "")
            results.totalAssignments := seatAssignments.Length

            ; Generate report based on format
            if (options.outputFormat = "csv") {
                results.outputPath := this.GenerateCSVReport(seatAssignments, ReportsManager.REPORT_TYPES["seat_assignment"]["fields"], options)
            } else if (options.outputFormat = "html") {
                results.outputPath := this.GenerateHTMLReport(seatAssignments, ReportsManager.REPORT_TYPES["seat_assignment"]["fields"], options)
            } else if (options.outputFormat = "pdf") {
                results.outputPath := this.GeneratePDFReport(seatAssignments, ReportsManager.REPORT_TYPES["seat_assignment"]["fields"], options)
            } else {
                throw Error("Unsupported output format: " options.outputFormat)
            }

            results.includedAssignments := seatAssignments.Length
            return results
        } catch as err {
            results.errors.Push("Error generating seat assignment report: " err.Message)
            return results
        }
    }

    ; Generate a special candidates report
    ; Parameters:
    ; - options: Object with report options
    ;   - outputFormat: Format of the report (csv, html, pdf)
    ;   - includeHeaders: Whether to include headers (default: true)
    ; Returns: Object with report results
    GenerateSpecialCandidatesReport(options := "") {
        ; Initialize results
        results := {
            reportType: "special_candidates",
            totalCandidates: 0,
            specialCandidates: 0,
            outputPath: "",
            errors: []
        }

        ; Set default options
        if (!IsObject(options)) {
            options := {}
        }
        options.outputFormat := options.HasOwnProp("outputFormat") ? options.outputFormat : "csv"
        options.includeHeaders := options.HasOwnProp("includeHeaders") ? options.includeHeaders : true

        try {
            ; Get all candidates
            allCandidates := this.GetAllCandidates()
            results.totalCandidates := allCandidates.Length

            ; Filter special candidates
            specialCandidates := []
            for candidate in allCandidates {
                if (candidate.Special = "1") {
                    specialCandidates.Push(candidate)
                }
            }
            results.specialCandidates := specialCandidates.Length

            ; Generate report based on format
            if (options.outputFormat = "csv") {
                results.outputPath := this.GenerateCSVReport(specialCandidates, ReportsManager.REPORT_TYPES["special_candidates"]["fields"], options)
            } else if (options.outputFormat = "html") {
                results.outputPath := this.GenerateHTMLReport(specialCandidates, ReportsManager.REPORT_TYPES["special_candidates"]["fields"], options)
            } else if (options.outputFormat = "pdf") {
                results.outputPath := this.GeneratePDFReport(specialCandidates, ReportsManager.REPORT_TYPES["special_candidates"]["fields"], options)
            } else {
                throw Error("Unsupported output format: " options.outputFormat)
            }

            return results
        } catch as err {
            results.errors.Push("Error generating special candidates report: " err.Message)
            return results
        }
    }

    ; Generate a verification statistics report
    ; Parameters:
    ; - options: Object with report options
    ;   - outputFormat: Format of the report (csv, html, pdf)
    ;   - includeHeaders: Whether to include headers (default: true)
    ; Returns: Object with report results
    GenerateVerificationStatisticsReport(options := "") {
        ; Initialize results
        results := {
            reportType: "verification_statistics",
            outputPath: "",
            errors: []
        }

        ; Set default options
        if (!IsObject(options)) {
            options := {}
        }
        options.outputFormat := options.HasOwnProp("outputFormat") ? options.outputFormat : "csv"
        options.includeHeaders := options.HasOwnProp("includeHeaders") ? options.includeHeaders : true

        try {
            ; Calculate statistics
            stats := this.CalculateVerificationStatistics()

            ; Generate report based on format
            if (options.outputFormat = "csv") {
                results.outputPath := this.GenerateStatisticsCSVReport(stats, options)
            } else if (options.outputFormat = "html") {
                results.outputPath := this.GenerateStatisticsHTMLReport(stats, options)
            } else if (options.outputFormat = "pdf") {
                results.outputPath := this.GenerateStatisticsPDFReport(stats, options)
            } else {
                throw Error("Unsupported output format: " options.outputFormat)
            }

            return results
        } catch as err {
            results.errors.Push("Error generating verification statistics report: " err.Message)
            return results
        }
    }

    ; Generate a CSV report
    ; Parameters:
    ; - data: Array of objects to include in the report
    ; - fields: Array of field names to include
    ; - options: Object with report options
    ; Returns: Path to the generated report
    GenerateCSVReport(data, fields, options) {
        ; Create report filename
        timestamp := FormatTime(, "yyyyMMdd_HHmmss")
        reportFile := REPORTS_DIR options.reportType "_report_" timestamp ".csv"

        ; Create CSV content
        csvContent := ""

        ; Add headers if requested
        if (options.includeHeaders) {
            headers := []
            for field in fields {
                headers.Push(field)
            }
            csvContent := StrJoin(headers, ",") "`n"
        }

        ; Add data rows
        for item in data {
            row := []
            for field in fields {
                row.Push(item.HasOwnProp(field) ? item[field] : "")
            }
            csvContent .= StrJoin(row, ",") "`n"
        }

        ; Write to file
        FileOpen(reportFile, "w").Write(csvContent)

        return reportFile
    }

    ; Generate an HTML report
    ; Parameters:
    ; - data: Array of objects to include in the report
    ; - fields: Array of field names to include
    ; - options: Object with report options
    ; Returns: Path to the generated report
    GenerateHTMLReport(data, fields, options) {
        ; Create report filename
        timestamp := FormatTime(, "yyyyMMdd_HHmmss")
        reportFile := REPORTS_DIR options.reportType "_report_" timestamp ".html"

        ; Create HTML content
        htmlContent := "<!DOCTYPE html>`n"
        htmlContent .= "<html>`n<head>`n"
        htmlContent .= "<title>" ReportsManager.REPORT_TYPES[options.reportType]["name"] "</title>`n"
        htmlContent .= "<style>`n"
        htmlContent .= "body { font-family: Arial, sans-serif; margin: 20px; }`n"
        htmlContent .= "table { border-collapse: collapse; width: 100%; }`n"
        htmlContent .= "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }`n"
        htmlContent .= "th { background-color: #f2f2f2; }`n"
        htmlContent .= "tr:nth-child(even) { background-color: #f9f9f9; }`n"
        htmlContent .= "h1 { color: #333; }`n"
        htmlContent .= "</style>`n"
        htmlContent .= "</head>`n<body>`n"
        htmlContent .= "<h1>" ReportsManager.REPORT_TYPES[options.reportType]["name"] "</h1>`n"
        htmlContent .= "<p>Generated on: " FormatTime(, "dd-MMM-yyyy hh:mm:ss tt") "</p>`n"
        htmlContent .= "<table>`n<tr>`n"

        ; Add headers
        for field in fields {
            htmlContent .= "<th>" field "</th>`n"
        }
        htmlContent .= "</tr>`n"

        ; Add data rows
        for item in data {
            htmlContent .= "<tr>`n"
            for field in fields {
                value := item.HasOwnProp(field) ? item[field] : ""
                htmlContent .= "<td>" value "</td>`n"
            }
            htmlContent .= "</tr>`n"
        }

        htmlContent .= "</table>`n"
        htmlContent .= "</body>`n</html>"

        ; Write to file
        FileOpen(reportFile, "w").Write(htmlContent)

        return reportFile
    }

    ; Generate a PDF report (placeholder - would require PDF library)
    ; Parameters:
    ; - data: Array of objects to include in the report
    ; - fields: Array of field names to include
    ; - options: Object with report options
    ; Returns: Path to the generated report
    GeneratePDFReport(data, fields, options) {
        ; Create report filename
        timestamp := FormatTime(, "yyyyMMdd_HHmmss")
        reportFile := REPORTS_DIR options.reportType "_report_" timestamp ".html"

        ; For now, generate HTML as a placeholder
        ; In a real implementation, use a PDF library or convert HTML to PDF
        return this.GenerateHTMLReport(data, fields, options)
    }

    ; Generate a statistics CSV report
    ; Parameters:
    ; - stats: Object with statistics
    ; - options: Object with report options
    ; Returns: Path to the generated report
    GenerateStatisticsCSVReport(stats, options) {
        ; Create report filename
        timestamp := FormatTime(, "yyyyMMdd_HHmmss")
        reportFile := REPORTS_DIR "verification_statistics_" timestamp ".csv"

        ; Create CSV content
        csvContent := ""

        ; Add headers if requested
        if (options.includeHeaders) {
            csvContent := "Statistic,Value`n"
        }

        ; Add statistics
        for stat, value in stats {
            csvContent .= stat "," value "`n"
        }

        ; Write to file
        FileOpen(reportFile, "w").Write(csvContent)

        return reportFile
    }

    ; Generate a statistics HTML report
    ; Parameters:
    ; - stats: Object with statistics
    ; - options: Object with report options
    ; Returns: Path to the generated report
    GenerateStatisticsHTMLReport(stats, options) {
        ; Create report filename
        timestamp := FormatTime(, "yyyyMMdd_HHmmss")
        reportFile := REPORTS_DIR "verification_statistics_" timestamp ".html"

        ; Create HTML content
        htmlContent := "<!DOCTYPE html>`n"
        htmlContent .= "<html>`n<head>`n"
        htmlContent .= "<title>Verification Statistics Report</title>`n"
        htmlContent .= "<style>`n"
        htmlContent .= "body { font-family: Arial, sans-serif; margin: 20px; }`n"
        htmlContent .= "table { border-collapse: collapse; width: 100%; }`n"
        htmlContent .= "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }`n"
        htmlContent .= "th { background-color: #f2f2f2; }`n"
        htmlContent .= "tr:nth-child(even) { background-color: #f9f9f9; }`n"
        htmlContent .= "h1 { color: #333; }`n"
        htmlContent .= "</style>`n"
        htmlContent .= "</head>`n<body>`n"
        htmlContent .= "<h1>Verification Statistics Report</h1>`n"
        htmlContent .= "<p>Generated on: " FormatTime(, "dd-MMM-yyyy hh:mm:ss tt") "</p>`n"
        htmlContent .= "<table>`n"
        htmlContent .= "<tr><th>Statistic</th><th>Value</th></tr>`n"

        ; Add statistics
        for stat, value in stats {
            htmlContent .= "<tr><td>" stat "</td><td>" value "</td></tr>`n"
        }

        htmlContent .= "</table>`n"
        htmlContent .= "</body>`n</html>"

        ; Write to file
        FileOpen(reportFile, "w").Write(htmlContent)

        return reportFile
    }

    ; Generate a statistics PDF report (placeholder - would require PDF library)
    ; Parameters:
    ; - stats: Object with statistics
    ; - options: Object with report options
    ; Returns: Path to the generated report
    GenerateStatisticsPDFReport(stats, options) {
        ; Create report filename
        timestamp := FormatTime(, "yyyyMMdd_HHmmss")
        reportFile := REPORTS_DIR "verification_statistics_" timestamp ".html"

        ; For now, generate HTML as a placeholder
        ; In a real implementation, use a PDF library or convert HTML to PDF
        return this.GenerateStatisticsHTMLReport(stats, options)
    }

    ; Calculate verification statistics
    ; Returns: Object with statistics
    CalculateVerificationStatistics() {
        stats := {
            TotalCandidates: 0,
            VerifiedCandidates: 0,
            VerificationRate: 0,
            SpecialCandidates: 0,
            SpecialVerified: 0,
            SpecialVerificationRate: 0,
            PhotoVerified: 0,
            FingerprintVerified: 0,
            RightFingerprintVerified: 0,
            SignatureVerified: 0
        }

        try {
            ; Get all candidates
            candidates := this.GetAllCandidates()
            stats.TotalCandidates := candidates.Length

            ; Calculate statistics
            for candidate in candidates {
                if (candidate.BiometricStatus = "Verified")
                    stats.VerifiedCandidates++

                if (candidate.Special = "1") {
                    stats.SpecialCandidates++
                    if (candidate.BiometricStatus = "Verified")
                        stats.SpecialVerified++
                }

                if (candidate.PhotoStatus = "Verified")
                    stats.PhotoVerified++

                if (candidate.FingerprintStatus = "Verified")
                    stats.FingerprintVerified++

                if (candidate.RightFingerprintStatus = "Verified")
                    stats.RightFingerprintVerified++

                if (candidate.SignatureStatus = "Verified")
                    stats.SignatureVerified++
            }

            ; Calculate rates
            if (stats.TotalCandidates > 0)
                stats.VerificationRate := Round((stats.VerifiedCandidates / stats.TotalCandidates) * 100)

            if (stats.SpecialCandidates > 0)
                stats.SpecialVerificationRate := Round((stats.SpecialVerified / stats.SpecialCandidates) * 100)

            return stats
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to calculate verification statistics: " err.Message)
            return stats
        }
    }

    ; Get all candidates
    ; Parameters:
    ; - filter: Function to filter candidates (optional)
    ; Returns: Array of candidate objects
    GetAllCandidates(filter := "") {
        candidates := []

        try {
            ; Read all sections from the INI file
            fileContent := FileRead(REPORTS_CANDIDATES_PATH)
            sections := []

            ; Parse the file to extract section names
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    sections.Push(match[1])
                }
            }

            ; Read data for each candidate
            for rollNumber in sections {
                candidateData := {}
                candidateData.RollNumber := rollNumber
                candidateData.Name := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "Name", "")
                candidateData.FatherName := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "FatherName", "")
                candidateData.DateOfBirth := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "DateOfBirth", "")
                candidateData.Gender := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "Gender", "")
                candidateData.Email := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "Email", "")
                candidateData.Mobile := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "Mobile", "")
                candidateData.Status := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "Status", "Active")
                candidateData.Special := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "Special", "0")
                candidateData.PhotoStatus := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "PhotoStatus", "")
                candidateData.BiometricStatus := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "BiometricStatus", "")
                candidateData.FingerprintStatus := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "FingerprintStatus", "")
                candidateData.RightFingerprintStatus := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "RightFingerprintStatus", "")
                candidateData.SignatureStatus := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "SignatureStatus", "")
                candidateData.ThumbPreference := IniRead(REPORTS_CANDIDATES_PATH, rollNumber, "ThumbPreference", "")

                ; Apply filter if provided
                if (IsObject(filter)) {
                    if (filter(candidateData))
                        candidates.Push(candidateData)
                } else {
                    candidates.Push(candidateData)
                }
            }

            return candidates
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get candidates: " err.Message)
            return []
        }
    }

    ; Get all seat assignments
    ; Parameters:
    ; - filter: Function to filter seat assignments (optional)
    ; Returns: Array of seat assignment objects
    GetAllSeatAssignments(filter := "") {
        seatAssignments := []

        try {
            ; Read all sections from the INI file
            fileContent := FileRead(REPORTS_SEAT_ASSIGNMENTS_PATH)
            sections := []

            ; Parse the file to extract section names
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    sections.Push(match[1])
                }
            }

            ; Read data for each seat assignment
            for seatId in sections {
                assignmentData := {}
                assignmentData.SeatID := seatId
                assignmentData.RollNumber := IniRead(REPORTS_SEAT_ASSIGNMENTS_PATH, seatId, "RollNumber", "")
                assignmentData.Name := IniRead(REPORTS_SEAT_ASSIGNMENTS_PATH, seatId, "Name", "")
                assignmentData.AssignedTime := IniRead(REPORTS_SEAT_ASSIGNMENTS_PATH, seatId, "AssignedTime", "")
                assignmentData.AssignedBy := IniRead(REPORTS_SEAT_ASSIGNMENTS_PATH, seatId, "AssignedBy", "")
                assignmentData.ExamID := IniRead(REPORTS_SEAT_ASSIGNMENTS_PATH, seatId, "ExamID", "")

                ; Apply filter if provided
                if (IsObject(filter)) {
                    if (filter(assignmentData))
                        seatAssignments.Push(assignmentData)
                } else {
                    seatAssignments.Push(assignmentData)
                }
            }

            return seatAssignments
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get seat assignments: " err.Message)
            return []
        }
    }
}
