# Acknowledgements

In addition to the AutoIt authors already [mentioned](../index.htm):

**<PERSON>:** A lot of tireless testing to isolate bugs, a great
first draft of the installer, as well as great suggestions for how
commands **ought** to work :)

**<PERSON>:** For suggesting and describing floating point
support.

**<PERSON>:** For discovering many Win9x problems with the Send
command, CapsLock, and hotkey modifiers; and for generously sharing his
wealth of code and wisdom for hotkeys, hot-strings, hook usage, and
typing acceleration.

**Raja<PERSON>:** For creating stylish replacements for the original AHK icons;
a great product logo; making the syntax customizations for TextPad;
discovering some bugs with the registry commands and AutoIt v2
compatibility; making SmartGUI Creator; and many other things.

**<PERSON><PERSON><PERSON><PERSON> (Beardboy):** For NT4 testing to fix GetKeyState and the
Send command; for a lot of help on the forum; and for many suggestions
and bug reports.

**<PERSON> of <PERSON>gg\'s Software:** For writing the source code for
multi-monitor support in the [SysGet](../lib/SysGet.htm) command.

**<PERSON><PERSON><PERSON>:** For writing the source code for
[ImageSearch](../lib/ImageSearch.htm) and the faster
[PixelSearch](../lib/PixelSearch.htm).

**Joost Mulders:** Whose source code provided the foundation for
[expressions](../Variables.htm#Expressions).

**Laszlo Hars:** For advice about data structures and algorithms, which
helped greatly speed up arrays and dynamic variables.

**Marcus Sonntag (Ultra):** For the research, design, coding, and
testing of [DllCall](../lib/DllCall.htm).

**Gena Shimanovich:** For debugging and coding assistance; and for some
advanced prototype scripts upon which future features may be based.

**Eric Morin (numEric):** For advice on mathematics; for steadfast
debugging of areas like [OnMessage](../lib/OnMessage.htm), floating
point computations, and mouse movement; and for improvements to overall
quality.

**Philip Hazel:** For [Perl-Compatible Regular Expressions
(PCRE)](http://www.pcre.org/).

**Titan/polyethene:** For providing community hosting on autohotkey.net,
creating many useful scripts and libraries, creating the JavaScript to
colorize code-comments in the forum, and many other things.

**Philippe Lhoste (PhiLho):** For tireless moderation and support in the
forum, RegEx advice and testing, syntax and design ideas, and many other
things.

**John Biederman:** For greatly improving the presentation and
ergonomics of the documentation.

**Jonathan Rennison (JGR):** For developing RegisterCallback (now called
[CallbackCreate](../lib/CallbackCreate.htm)), and for beneficial
suggestions.

**Steve Gray (Lexikos):** For developing dynamic function calling and
other new functionality; analyzing and fixing bugs; and valuable support
and advice for hundreds of individual visitors at the forum.

[AutoHotkey_L:]{.underline}

**jackieku:** For developing Unicode support and other new
functionality.

**fincs:** For developing native 64-bit support and try/catch/throw,
writing a replacement for the old compiler, analyzing and fixing bugs.

**Sean:** For developing built-in COM functionality and providing
valuable insight into COM.

**TheGood:** For merging the documentation and adapting the installer
script.

**ac:** For developing #Warn.

**Russell Davis:** For developing A_PriorKey, ahk_path (the basis of
ahk_exe in WinTitle parameters), #InputLevel and SendLevel.

**Christian Sander:** For developing support for SysLink controls.

 

*And to everyone else who\'s contributed or sent in bug reports or
suggestions: Thanks!*
