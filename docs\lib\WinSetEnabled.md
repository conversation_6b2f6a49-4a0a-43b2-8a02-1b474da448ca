# WinSetEnabled

Enables or disables the specified window.

``` Syntax
WinSetEnabled NewSetting , WinTitle, WinText, ExcludeTitle, ExcludeText
```

## Parameters {#Parameters}

NewSetting

:   Type: [Integer](../Concepts.htm#numbers)

    One of the following values:

    - `1` or `True` turns on the setting
    - `0` or `False` turns off the setting
    - `-1` toggles the setting (sets it to the opposite of its current
      state)

WinTitle, WinText, ExcludeTitle, ExcludeText

:   Type: [String](../Concepts.htm#strings),
    [Integer](../Concepts.htm#numbers) or
    [Object](../Concepts.htm#objects)

    If each of these is blank or omitted, the [Last Found
    Window](../misc/WinTitle.htm#LastFoundWindow) will be used.
    Otherwise, specify for *WinTitle* a [window title or other
    criteria](../misc/WinTitle.htm) to identify the target window and/or
    for *WinText* a substring from a single text element of the target
    window (as revealed by the included Window Spy utility).

    *ExcludeTitle* and *ExcludeText* can be used to exclude one or more
    windows by their title or text. Their specification is similar to
    *WinTitle* and *WinText*, except that *ExcludeTitle* does not
    recognize any criteria other than the window title.

    Window titles and text are case-sensitive. By default, hidden
    windows are not detected and hidden text elements are detected,
    unless changed with [DetectHiddenWindows](DetectHiddenWindows.htm)
    and [DetectHiddenText](DetectHiddenText.htm); however, when using
    [pure HWNDs](../misc/WinTitle.htm#ahk_id), hidden windows are always
    detected regardless of DetectHiddenWindows. By default, a window
    title can contain *WinTitle* or *ExcludeTitle* anywhere inside it to
    be a match, unless changed with
    [SetTitleMatchMode](SetTitleMatchMode.htm).

## Error Handling {#Error_Handling}

A [TargetError](Error.htm#TargetError) is thrown if the window could not
be found.

An [OSError](Error.htm#OSError) is thrown if the change could not be
applied.

## Remarks {#Remarks}

When a window is disabled, the user cannot move it or interact with its
controls. In addition, disabled windows are omitted from the alt-tab
list.

[WinGetStyle example #1](WinGetStyle.htm#ExStyle) can be used to
determine whether a window is disabled.

The ID of the window under the mouse cursor can be retrieved with
[MouseGetPos](MouseGetPos.htm).

## Related {#Related}

[ControlSetEnabled](ControlSetEnabled.htm), [Win functions](Win.htm),
[Control functions](Control.htm)
