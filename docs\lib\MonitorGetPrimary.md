# MonitorGetPrimary

Returns the number of the primary monitor.

``` Syntax
Primary := MonitorGetPrimary()
```

## Parameters {#Parameters}

This function has no parameters.

## Return Value {#Return_Value}

Type: [Integer](../Concepts.htm#numbers)

This function returns the number of the primary monitor. In a
single-monitor system, this will be always 1.

## Related {#Related}

[SysGet](SysGet.htm), [Monitor functions](Monitor.htm)

## Examples {#Examples}

See [example #1](Monitor.htm#ExLoopAll) on the [Monitor
Functions](Monitor.htm) page for a demonstration of this function.
