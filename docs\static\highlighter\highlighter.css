.highlight span>a,
.highlight span>a:hover {
  color: inherit !important;
}

.highlight.line-numbers-hide .line-numbers-rows {
  display: none;
}

.highlight.line-numbers {
  white-space: pre;
  overflow-x: auto;
}

.highlight.line-numbers .line-numbers-rows {
  position: absolute;
  pointer-events: none;
  top: 0;
  font-size: 100%;
  left: 0;
  width: 3em;
  letter-spacing: -1px;
  border-right: 1px solid;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.highlight.line-numbers .line-numbers-rows>span {
  display: block;
  counter-increment: linenumber;
}

.highlight.line-numbers .line-numbers-rows>span:before {
  content: counter(linenumber);
  display: block;
  padding-right: 0.8em;
  text-align: right;
}

.highlight code {
  position: relative;
  display: block;
  background-color: inherit;
  color: inherit;
}

.highlight.line-numbers code {
  padding-left: 3.8em;
}
