#Requires AutoHotkey v2.0

; CSV Manager for WinCBT-Biometric using static methods
; This module handles importing and exporting candidate data

; Define paths directly without including db_functions.ahk
global CSV_DB_PATH := IniRead(A_ScriptDir "\WinCBT-Biometric.ini", "Paths", "dbPath", "db")
; Convert relative to absolute path if needed
if (SubStr(CSV_DB_PATH, 1, 1) != "\" && SubStr(CSV_DB_PATH, 2, 1) != ":") {
    CSV_DB_PATH := A_ScriptDir "\" CSV_DB_PATH "\"
} else if (SubStr(CSV_DB_PATH, -1) != "\") {
    CSV_DB_PATH := CSV_DB_PATH "\"
}

; Define database file paths using the CSV_DB_PATH
global CSV_CANDIDATES_PATH := CSV_DB_PATH "candidates.ini"
global CSV_CANDIDATES_IMG_PATH := CSV_DB_PATH "img\candidates\"
global CSV_FINGERPRINT_PATH := CSV_DB_PATH "fpt\"

; Helper function to join array elements with a delimiter
StrJoin(array, delimiter) {
    result := ""
    for i, item in array {
        if (i > 1) {
            result .= delimiter
        }
        result .= item
    }
    return result
}

; Function to parse various date formats
DateParse(dateStr) {
    ; Try common date formats
    formats := [
        "yyyy-MM-dd",
        "MM/dd/yyyy",
        "dd/MM/yyyy",
        "yyyy/MM/dd",
        "MMM dd, yyyy",
        "dd MMM yyyy",
        "MMMM dd, yyyy",
        "dd MMMM yyyy",
        "ddMMyyyy"
    ]

    ; Try to parse with each format
    for format in formats {
        try {
            date := ParseDate(dateStr, format)
            if date
                return date
        } catch {
            continue
        }
    }

    return ""
}

; Function to parse date with specific format
ParseDate(dateStr, format) {
    ; Simple implementation for common formats
    if (format = "ddMMyyyy") {
        if (StrLen(dateStr) = 8 && RegExMatch(dateStr, "^\d{8}$")) {
            day := SubStr(dateStr, 1, 2)
            month := SubStr(dateStr, 3, 2)
            year := SubStr(dateStr, 5, 4)
            return {day: day, month: month, year: year}
        }
    }

    ; For other formats, just check if it looks like a date
    if (RegExMatch(dateStr, "^\d{1,2}[/-]\d{1,2}[/-]\d{2,4}$") ||
        RegExMatch(dateStr, "^\d{4}[/-]\d{1,2}[/-]\d{1,2}$")) {
        return {valid: true}
    }

    return ""
}

; CSV Manager class with static methods
class CSVStatic {
    ; Configuration
    static tempDir := A_ScriptDir "\temp"

    ; Required fields for candidate import
    static requiredFields := ["RollNumber", "Name"]

    ; Field mappings for CSV import
    static defaultFieldMappings := Map(
        "RollNumber", "RollNumber",
        "Name", "Name",
        "FatherName", "FatherName",
        "DateOfBirth", "DOB",
        "Email", "Email",
        "Mobile", "Mobile",
        "Gender", "Gender",
        "CenterID", "CenterID",
        "ExamID", "ExamID",
        "StudentID", "StudentID",
        "Status", "Status",
        "Special", "Special"
    )

    ; Field mappings for CSV export
    static defaultExportFields := [
        {field: "RollNumber", header: "RollNumber"},
        {field: "Name", header: "Name"},
        {field: "FatherName", header: "FatherName"},
        {field: "DateOfBirth", header: "DOB"},
        {field: "Email", header: "Email"},
        {field: "Mobile", header: "Mobile"},
        {field: "Gender", header: "Gender"},
        {field: "CenterID", header: "CenterID"},
        {field: "ExamID", header: "ExamID"},
        {field: "StudentID", header: "StudentID"},
        {field: "Status", header: "Status"},
        {field: "Special", header: "Special"},
        {field: "PhotoStatus", header: "PhotoStatus"},
        {field: "BiometricStatus", header: "BiometricStatus"},
        {field: "FingerprintStatus", header: "FingerprintStatus"},
        {field: "RightFingerprintStatus", header: "RightFingerprintStatus"},
        {field: "SignatureStatus", header: "SignatureStatus"},
        {field: "ThumbPreference", header: "ThumbPreference"}
    ]

    ; Initialize directories
    static Init() {
        ; Create directories if they don't exist
        for dir in [this.importDir, this.exportDir, this.tempDir] {
            if (!DirExist(dir)) {
                DirCreate(dir)
            }
        }
    }

    ; Import candidates from CSV file
    static ImportCandidatesFromCSV(csvFile, fieldMappings := "", options := "") {
        ; Initialize directories
        this.Init()

        ; Initialize results
        results := {
            totalRows: 0,
            imported: 0,
            skipped: 0,
            errors: [],
            validationErrors: Map()
        }

        ; Set default options
        if (!IsObject(options)) {
            options := {}
        }
        options.overwrite := options.HasOwnProp("overwrite") ? options.overwrite : false
        options.validateOnly := options.HasOwnProp("validateOnly") ? options.validateOnly : false
        options.delimiter := options.HasOwnProp("delimiter") ? options.delimiter : ","

        ; Use default field mappings if not provided
        if (!IsObject(fieldMappings) || !fieldMappings.Count) {
            fieldMappings := this.defaultFieldMappings.Clone()
        }

        ; Check if file exists
        if (!FileExist(csvFile)) {
            results.errors.Push("File not found: " csvFile)
            return results
        }

        try {
            ; Read CSV file
            fileContent := FileRead(csvFile)

            ; Split into lines
            lines := StrSplit(fileContent, "`n", "`r")

            ; Get header line
            if (lines.Length < 2) {
                results.errors.Push("CSV file must have at least a header row and one data row")
                return results
            }

            ; Parse header
            headerLine := lines[1]
            headers := StrSplit(headerLine, options.delimiter)

            ; Validate required fields
            missingFields := []
            for field in this.requiredFields {
                fieldFound := false
                for mappedField, originalField in fieldMappings {
                    for i, header in headers {
                        if (header = originalField) {
                            fieldFound := true
                            break
                        }
                    }
                    if (fieldFound) {
                        break
                    }
                }
                if (!fieldFound) {
                    missingFields.Push(field)
                }
            }

            if (missingFields.Length > 0) {
                results.errors.Push("Missing required fields: " StrJoin(missingFields, ", "))
                return results
            }

            ; Process data rows
            for i, line in lines {
                ; Skip header row
                if (i = 1 || Trim(line) = "") {
                    continue
                }

                results.totalRows++

                ; Parse line
                fields := StrSplit(line, options.delimiter)

                ; Create candidate data object
                candidateData := {}

                ; Map fields
                for j, header in headers {
                    if (j <= fields.Length) {
                        ; Find the internal field name for this header
                        for mappedField, originalField in fieldMappings {
                            if (header = originalField) {
                                candidateData[mappedField] := fields[j]
                                break
                            }
                        }
                    }
                }

                ; Validate candidate data
                validationErrors := this.ValidateCandidateData(candidateData)

                ; Store validation errors
                if (validationErrors.Length > 0) {
                    results.validationErrors[candidateData.RollNumber] := validationErrors
                    results.skipped++
                    continue
                }

                ; Skip validation-only mode
                if (options.validateOnly) {
                    continue
                }

                ; Check if candidate already exists
                candidateExists := this.CandidateExists(candidateData.RollNumber)

                ; Skip if candidate exists and overwrite is false
                if (candidateExists && !options.overwrite) {
                    results.skipped++
                    continue
                }

                ; Import candidate
                try {
                    this.ImportCandidate(candidateData)
                    results.imported++
                } catch Error as e {
                    results.errors.Push("Error importing candidate " candidateData.RollNumber ": " e.Message)
                    results.skipped++
                }
            }

            return results
        } catch Error as e {
            results.errors.Push("Error processing CSV file: " e.Message)
            return results
        }
    }

    ; Validate candidate data
    static ValidateCandidateData(candidateData) {
        errors := []

        ; Check required fields
        for field in this.requiredFields {
            if (!candidateData.HasOwnProp(field) || candidateData[field] = "") {
                errors.Push("Missing required field: " field)
            }
        }

        ; Validate roll number format (alphanumeric)
        if (candidateData.HasOwnProp("RollNumber") && candidateData.RollNumber != "") {
            if (!RegExMatch(candidateData.RollNumber, "^[A-Za-z0-9]+$")) {
                errors.Push("Roll Number must be alphanumeric")
            }
        }

        ; Validate date of birth format if present
        if (candidateData.HasOwnProp("DateOfBirth") && candidateData.DateOfBirth != "") {
            ; Try to parse the date
            parsedDate := DateParse(candidateData.DateOfBirth)
            if (!parsedDate) {
                errors.Push("Invalid Date of Birth format")
            }
        }

        ; Validate Special field if present (must be 0 or 1)
        if (candidateData.HasOwnProp("Special") && candidateData.Special != "") {
            if (candidateData.Special != "0" && candidateData.Special != "1") {
                errors.Push("Special field must be 0 or 1")
            }
        }

        return errors
    }

    ; Check if candidate exists
    static CandidateExists(rollNumber) {
        global CSV_CANDIDATES_PATH
        try {
            name := IniRead(CSV_CANDIDATES_PATH, rollNumber, "Name", "")
            return name != ""
        } catch {
            return false
        }
    }

    ; Import a single candidate
    static ImportCandidate(candidateData) {
        global CSV_CANDIDATES_PATH

        ; Ensure roll number exists
        if (!candidateData.HasOwnProp("RollNumber") || candidateData.RollNumber = "") {
            throw Error("Roll Number is required")
        }

        ; Write candidate data to INI file
        for field, value in candidateData.OwnProps() {
            IniWrite(value, CSV_CANDIDATES_PATH, candidateData.RollNumber, field)
        }

        ; Set default values for missing fields
        defaultFields := Map(
            "Status", "Active",
            "PhotoStatus", "",
            "BiometricStatus", "",
            "FingerprintStatus", "",
            "RightFingerprintStatus", "",
            "SignatureStatus", ""
        )

        for field, defaultValue in defaultFields {
            if (!candidateData.HasOwnProp(field)) {
                IniWrite(defaultValue, CSV_CANDIDATES_PATH, candidateData.RollNumber, field)
            }
        }
    }

    ; Export candidates to CSV file
    static ExportCandidatesToCSV(csvFile, options := "") {
        ; Initialize directories
        this.Init()

        ; Initialize results
        results := {
            totalCandidates: 0,
            exported: 0,
            errors: []
        }

        ; Set default options
        if (!IsObject(options)) {
            options := {}
        }
        options.delimiter := options.HasOwnProp("delimiter") ? options.delimiter : ","
        options.includeHeaders := options.HasOwnProp("includeHeaders") ? options.includeHeaders : true
        options.fields := options.HasOwnProp("fields") ? options.fields : this.defaultExportFields

        try {
            ; Get all candidates
            candidates := this.GetAllCandidates(options.HasOwnProp("filter") ? options.filter : "")

            results.totalCandidates := candidates.Length

            ; Create CSV content
            csvContent := ""

            ; Add headers if requested
            if (options.includeHeaders) {
                headers := []
                for field in options.fields {
                    headers.Push(field.header)
                }
                csvContent := StrJoin(headers, options.delimiter) "`n"
            }

            ; Add candidate data
            for candidate in candidates {
                row := []
                for field in options.fields {
                    row.Push(candidate.HasOwnProp(field.field) ? candidate[field.field] : "")
                }
                csvContent .= StrJoin(row, options.delimiter) "`n"
                results.exported++
            }

            ; Write to file
            FileOpen(csvFile, "w").Write(csvContent)

            return results
        } catch Error as e {
            results.errors.Push("Error exporting candidates: " e.Message)
            return results
        }
    }

    ; Get all candidates
    static GetAllCandidates(filter := "") {
        global CSV_CANDIDATES_PATH
        candidates := []

        try {
            ; Read all sections from the INI file
            fileContent := FileRead(CSV_CANDIDATES_PATH)
            sections := []

            ; Parse the file to extract section names
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    sections.Push(match[1])
                }
            }

            ; Process each candidate
            for rollNumber in sections {
                ; Read candidate data
                candidate := {
                    RollNumber: rollNumber,
                    Name: IniRead(CSV_CANDIDATES_PATH, rollNumber, "Name", ""),
                    FatherName: IniRead(CSV_CANDIDATES_PATH, rollNumber, "FatherName", ""),
                    DateOfBirth: IniRead(CSV_CANDIDATES_PATH, rollNumber, "DateOfBirth", ""),
                    Email: IniRead(CSV_CANDIDATES_PATH, rollNumber, "Email", ""),
                    Mobile: IniRead(CSV_CANDIDATES_PATH, rollNumber, "Mobile", ""),
                    Gender: IniRead(CSV_CANDIDATES_PATH, rollNumber, "Gender", ""),
                    CenterID: IniRead(CSV_CANDIDATES_PATH, rollNumber, "CenterID", ""),
                    ExamID: IniRead(CSV_CANDIDATES_PATH, rollNumber, "ExamID", ""),
                    StudentID: IniRead(CSV_CANDIDATES_PATH, rollNumber, "StudentID", ""),
                    Status: IniRead(CSV_CANDIDATES_PATH, rollNumber, "Status", ""),
                    Special: IniRead(CSV_CANDIDATES_PATH, rollNumber, "Special", "0"),
                    PhotoStatus: IniRead(CSV_CANDIDATES_PATH, rollNumber, "PhotoStatus", ""),
                    BiometricStatus: IniRead(CSV_CANDIDATES_PATH, rollNumber, "BiometricStatus", ""),
                    FingerprintStatus: IniRead(CSV_CANDIDATES_PATH, rollNumber, "FingerprintStatus", ""),
                    RightFingerprintStatus: IniRead(CSV_CANDIDATES_PATH, rollNumber, "RightFingerprintStatus", ""),
                    SignatureStatus: IniRead(CSV_CANDIDATES_PATH, rollNumber, "SignatureStatus", ""),
                    ThumbPreference: IniRead(CSV_CANDIDATES_PATH, rollNumber, "ThumbPreference", "Both")
                }

                ; Apply filter if provided
                if (IsObject(filter)) {
                    if (filter(candidate)) {
                        candidates.Push(candidate)
                    }
                } else {
                    candidates.Push(candidate)
                }
            }
        } catch Error as e {
            throw Error("Error getting candidates: " e.Message)
        }

        return candidates
    }
}
