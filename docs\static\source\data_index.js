indexData = [
  ["__Call meta-function","Objects.htm#Meta_Functions"],
  ["__Delete meta-function","Objects.htm#Custom_NewDelete"],
  ["__Enum method","Objects.htm#__Enum"],
  ["__Enum method (Array)","lib/Array.htm#__Enum",7],
  ["__Enum method (Gui)","lib/Gui.htm#__Enum",7],
  ["__Enum method (Map)","lib/Map.htm#__Enum",7],
  ["__Get meta-function","Objects.htm#Meta_Functions"],
  ["__Init method","Objects.htm#Custom_Classes_var"],
  ["__Item property","Objects.htm#__Item"],
  ["__Item property (Array)","lib/Array.htm#__Item",7],
  ["__Item property (Gui)","lib/Gui.htm#__Item",7],
  ["__Item property (Map)","lib/Map.htm#__Item",7],
  ["__New meta-function","Objects.htm#Custom_NewDelete"],
  ["__New method (Array)","lib/Array.htm#__New",7],
  ["__New method (Buffer)","lib/Buffer.htm#__New",7],
  ["__New method (Gui)","lib/Gui.htm#__New",7],
  ["__New method (Map)","lib/Map.htm#__New",7],
  ["__Set meta-function","Objects.htm#Meta_Functions"],
  ["-","Variables.htm#AddSub",4],
  ["- (sign)","Variables.htm#unary",4],
  ["--","Variables.htm#IncDec",4],
  ["-=","Variables.htm#AssignOp",4],
  [",","Variables.htm#comma",4],
  ["; (single-line comments)","Language.htm#comments"],
  [";@Ahk2Exe-","misc/Ahk2ExeDirectives.htm"],
  [":","Variables.htm#ternary",4],
  [":=","Variables.htm#AssignOp",4],
  ["!","Variables.htm#unary",4],
  ["!=","Variables.htm#equal",4],
  ["!==","Variables.htm#equal",4],
  ["?","Variables.htm#ternary",4],
  ["??","Variables.htm#or-maybe",4],
  [".","Variables.htm#concat",4],
  [".=","Variables.htm#AssignOp",4],
  ["' (quoted strings)","Language.htm#strings"],
  ["\" (quoted strings)","Language.htm#strings"],
  ["[]","Variables.htm#square-brackets"],
  ["{}","Variables.htm#curly-braces"],
  ["{Blind}","lib/Send.htm#blind"],
  ["@Ahk2Exe-","misc/Ahk2ExeDirectives.htm"],
  ["*","Variables.htm#MulDiv",4],
  ["**","Variables.htm#pow",4],
  ["*/ (multi-line comments)","Language.htm#comments"],
  ["*=","Variables.htm#AssignOp",4],
  ["/","Variables.htm#MulDiv",4],
  ["/* (multi-line comments)","Language.htm#comments"],
  ["//","Variables.htm#MulDiv",4],
  ["//=","Variables.htm#AssignOp",4],
  ["/=","Variables.htm#AssignOp",4],
  ["&","Variables.htm#ref",4],
  ["& (bitwise-and)","Variables.htm#bitwise",4],
  ["&&","Variables.htm#and",4],
  ["&=","Variables.htm#AssignOp",4],
  ["#ClipboardTimeout","lib/_ClipboardTimeout.htm",0,"S"],
  ["#DllLoad","lib/_DllLoad.htm",0,"S"],
  ["#ErrorStdOut","lib/_ErrorStdOut.htm",0,"S"],
  ["#HotIf","lib/_HotIf.htm",0,"E"],
  ["#HotIfTimeout","lib/_HotIfTimeout.htm",0,"S"],
  ["#Hotstring","lib/_Hotstring.htm",0,"S"],
  ["#Include","lib/_Include.htm",0,"S"],
  ["#IncludeAgain","lib/_Include.htm",0,"S"],
  ["#InputLevel","lib/_InputLevel.htm",0,"S"],
  ["#MaxThreads","lib/_MaxThreads.htm",0,"S"],
  ["#MaxThreadsBuffer","lib/_MaxThreadsBuffer.htm",0,"S"],
  ["#MaxThreadsPerHotkey","lib/_MaxThreadsPerHotkey.htm",0,"S"],
  ["#NoTrayIcon","lib/_NoTrayIcon.htm",0,""],
  ["#Requires","lib/_Requires.htm",0,"S"],
  ["#SingleInstance","lib/_SingleInstance.htm",0,"S"],
  ["#SuspendExempt","lib/_SuspendExempt.htm",0,"S"],
  ["#UseHook","lib/_UseHook.htm",0,"S"],
  ["#Warn","lib/_Warn.htm",0,"SS"],
  ["#WinActivateForce","lib/_WinActivateForce.htm",0,""],
  ["%Expr%","Variables.htm#deref"],
  ["` (escape sequences)","misc/EscapeChar.htm"],
  ["^","Variables.htm#bitwise",4],
  ["^=","Variables.htm#AssignOp",4],
  ["+","Variables.htm#AddSub",4],
  ["++","Variables.htm#IncDec",4],
  ["+=","Variables.htm#AssignOp",4],
  ["<","Variables.htm#compare",4],
  ["<<","Variables.htm#bitshift",4],
  ["<<=","Variables.htm#AssignOp",4],
  ["<=","Variables.htm#compare",4],
  ["=","Variables.htm#equal",4],
  ["==","Variables.htm#equal",4],
  ["=>","Variables.htm#fat-arrow",4],
  [">","Variables.htm#compare",4],
  [">=","Variables.htm#compare",4],
  [">>","Variables.htm#bitshift",4],
  [">>=","Variables.htm#AssignOp",4],
  [">>>","Variables.htm#bitshift",4],
  [">>>=","Variables.htm#AssignOp",4],
  ["|","Variables.htm#bitwise",4],
  ["|=","Variables.htm#AssignOp",4],
  ["||","Variables.htm#or",4],
  ["~","Variables.htm#unary",4],
  ["~=","Variables.htm#regex",4],
  ["A_AhkPath","Variables.htm#AhkPath",1],
  ["A_AhkVersion","Variables.htm#AhkVersion",1],
  ["A_AllowMainWindow","Variables.htm#AllowMainWindow",1],
  ["A_AppData","Variables.htm#AppData",1],
  ["A_AppDataCommon","Variables.htm#AppDataCommon",1],
  ["A_Args","Variables.htm#Args",1],
  ["A_BasePath (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#BasePath",99],
  ["A_Clipboard","lib/A_Clipboard.htm",1],
  ["A_ComputerName","Variables.htm#ComputerName",1],
  ["A_ComSpec","Variables.htm#ComSpec",1],
  ["A_ControlDelay","Variables.htm#ControlDelay",1],
  ["A_CoordModeCaret","Variables.htm#CoordMode",1],
  ["A_CoordModeMenu","Variables.htm#CoordMode",1],
  ["A_CoordModeMouse","Variables.htm#CoordMode",1],
  ["A_CoordModePixel","Variables.htm#CoordMode",1],
  ["A_CoordModeToolTip","Variables.htm#CoordMode",1],
  ["A_Cursor","Variables.htm#Cursor",1],
  ["A_DD","Variables.htm#DD",1],
  ["A_DDD","Variables.htm#DDDD",1],
  ["A_DDDD","Variables.htm#DDDD",1],
  ["A_DefaultMouseSpeed","Variables.htm#DefaultMouseSpeed",1],
  ["A_Desktop","Variables.htm#Desktop",1],
  ["A_DesktopCommon","Variables.htm#DesktopCommon",1],
  ["A_DetectHiddenText","Variables.htm#DetectHiddenText",1],
  ["A_DetectHiddenWindows","Variables.htm#DetectHiddenWindows",1],
  ["A_EndChar","Variables.htm#EndChar",1],
  ["A_EventInfo","Variables.htm#EventInfo",1],
  ["A_FileEncoding","Variables.htm#FileEncoding",1],
  ["A_HotkeyInterval","lib/A_MaxHotkeysPerInterval.htm",1],
  ["A_HotkeyModifierTimeout","lib/A_HotkeyModifierTimeout.htm",1],
  ["A_Hour","Variables.htm#Hour",1],
  ["A_IconFile","Variables.htm#IconFile",1],
  ["A_IconHidden","Variables.htm#IconHidden",1],
  ["A_IconNumber","Variables.htm#IconNumber",1],
  ["A_IconTip","Variables.htm#IconTip",1],
  ["A_Index","Variables.htm#Index",1],
  ["A_InitialWorkingDir","Variables.htm#InitialWorkingDir",1],
  ["A_Is64bitOS","Variables.htm#Is64bitOS",1],
  ["A_IsAdmin","Variables.htm#IsAdmin",1],
  ["A_IsCompiled","Variables.htm#IsCompiled",1],
  ["A_IsCritical","Variables.htm#IsCritical",1],
  ["A_IsPaused","Variables.htm#IsPaused",1],
  ["A_IsSuspended","Variables.htm#IsSuspended",1],
  ["A_KeyDelay","Variables.htm#KeyDelay",1],
  ["A_KeyDelayPlay","Variables.htm#KeyDelayPlay",1],
  ["A_KeyDuration","Variables.htm#KeyDelay",1],
  ["A_KeyDurationPlay","Variables.htm#KeyDelayPlay",1],
  ["A_Language","Variables.htm#Language",1],
  ["A_Language Values","misc/Languages.htm"],
  ["A_LastError","Variables.htm#LastError",1],
  ["A_LineFile","Variables.htm#LineFile",1],
  ["A_LineNumber","Variables.htm#LineNumber",1],
  ["A_ListLines","Variables.htm#ListLines",1],
  ["A_LoopField","lib/LoopParse.htm#LoopField",1],
  ["A_LoopFileAttrib","lib/LoopFiles.htm#LoopFileAttrib",1],
  ["A_LoopFileDir","lib/LoopFiles.htm#LoopFileDir",1],
  ["A_LoopFileExt","lib/LoopFiles.htm#LoopFileExt",1],
  ["A_LoopFileFullPath","lib/LoopFiles.htm#LoopFileFullPath",1],
  ["A_LoopFileName","lib/LoopFiles.htm#LoopFileName",1],
  ["A_LoopFilePath","lib/LoopFiles.htm#LoopFilePath",1],
  ["A_LoopFileShortName","lib/LoopFiles.htm#LoopFileShortName",1],
  ["A_LoopFileShortPath","lib/LoopFiles.htm#LoopFileShortPath",1],
  ["A_LoopFileSize","lib/LoopFiles.htm#LoopFileSize",1],
  ["A_LoopFileSizeKB","lib/LoopFiles.htm#LoopFileSizeKB",1],
  ["A_LoopFileSizeMB","lib/LoopFiles.htm#LoopFileSizeMB",1],
  ["A_LoopFileTimeAccessed","lib/LoopFiles.htm#LoopFileTimeAccessed",1],
  ["A_LoopFileTimeCreated","lib/LoopFiles.htm#LoopFileTimeCreated",1],
  ["A_LoopFileTimeModified","lib/LoopFiles.htm#LoopFileTimeModified",1],
  ["A_LoopReadLine","lib/LoopRead.htm#LoopReadLine",1],
  ["A_LoopRegKey","lib/LoopReg.htm#vars",1],
  ["A_LoopRegName","lib/LoopReg.htm#vars",1],
  ["A_LoopRegTimeModified","lib/LoopReg.htm#vars",1],
  ["A_LoopRegType","lib/LoopReg.htm#vars",1],
  ["A_MaxHotkeysPerInterval","lib/A_MaxHotkeysPerInterval.htm",1],
  ["A_MDay","Variables.htm#DD",1],
  ["A_MenuMaskKey","lib/A_MenuMaskKey.htm",1],
  ["A_Min","Variables.htm#Min",1],
  ["A_MM","Variables.htm#MM",1],
  ["A_MMM","Variables.htm#MMM",1],
  ["A_MMMM","Variables.htm#MMMM",1],
  ["A_Mon","Variables.htm#MM",1],
  ["A_MouseDelay","Variables.htm#MouseDelay",1],
  ["A_MouseDelayPlay","Variables.htm#MouseDelay",1],
  ["A_MSec","Variables.htm#MSec",1],
  ["A_MyDocuments","Variables.htm#MyDocuments",1],
  ["A_Now","Variables.htm#Now",1],
  ["A_NowUTC","Variables.htm#NowUTC",1],
  ["A_OSVersion","Variables.htm#OSVersion",1],
  ["A_PriorHotkey","Variables.htm#PriorHotkey",1],
  ["A_PriorKey","Variables.htm#PriorKey",1],
  ["A_PriorLine (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#PriorLine",99],
  ["A_ProgramFiles","Variables.htm#ProgramFiles",1],
  ["A_Programs","Variables.htm#Programs",1],
  ["A_ProgramsCommon","Variables.htm#ProgramsCommon",1],
  ["A_PtrSize","Variables.htm#PtrSize",1],
  ["A_RegView","Variables.htm#RegView",1],
  ["A_ScreenDPI","Variables.htm#ScreenDPI",1],
  ["A_ScreenHeight","Variables.htm#Screen",1],
  ["A_ScreenWidth","Variables.htm#Screen",1],
  ["A_ScriptDir","Variables.htm#ScriptDir",1],
  ["A_ScriptFullPath","Variables.htm#ScriptFullPath",1],
  ["A_ScriptHwnd","Variables.htm#ScriptHwnd",1],
  ["A_ScriptName","Variables.htm#ScriptName",1],
  ["A_Sec","Variables.htm#Sec",1],
  ["A_SendLevel","Variables.htm#SendLevel",1],
  ["A_SendMode","Variables.htm#SendMode",1],
  ["A_Space","Variables.htm#Space",1],
  ["A_StartMenu","Variables.htm#StartMenu",1],
  ["A_StartMenuCommon","Variables.htm#StartMenuCommon",1],
  ["A_Startup","Variables.htm#Startup",1],
  ["A_StartupCommon","Variables.htm#StartupCommon",1],
  ["A_StoreCapsLockMode","Variables.htm#StoreCapsLockMode",1],
  ["A_Tab","Variables.htm#Tab",1],
  ["A_Temp","Variables.htm#Temp",1],
  ["A_ThisFunc","Variables.htm#ThisFunc",1],
  ["A_ThisHotkey","Variables.htm#ThisHotkey",1],
  ["A_TickCount","Variables.htm#TickCount",1],
  ["A_TimeIdle","Variables.htm#TimeIdle",1],
  ["A_TimeIdleKeyboard","Variables.htm#TimeIdleKeyboard",1],
  ["A_TimeIdleMouse","Variables.htm#TimeIdleMouse",1],
  ["A_TimeIdlePhysical","Variables.htm#TimeIdlePhysical",1],
  ["A_TimeSincePriorHotkey","Variables.htm#TimeSincePriorHotkey",1],
  ["A_TimeSinceThisHotkey","Variables.htm#TimeSinceThisHotkey",1],
  ["A_TitleMatchMode","Variables.htm#TitleMatchMode",1],
  ["A_TitleMatchModeSpeed","Variables.htm#TitleMatchModeSpeed",1],
  ["A_TrayMenu","Variables.htm#TrayMenu",1],
  ["A_UserName","Variables.htm#UserName",1],
  ["A_WDay","Variables.htm#WDay",1],
  ["A_WinDelay","Variables.htm#WinDelay",1],
  ["A_WinDir","Variables.htm#WinDir",1],
  ["A_WorkFileName (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#WorkFileName",99],
  ["A_WorkingDir","Variables.htm#WorkingDir",1],
  ["A_YDay","Variables.htm#YDay",1],
  ["A_Year","Variables.htm#YYYY",1],
  ["A_YWeek","Variables.htm#YWeek",1],
  ["A_YYYY","Variables.htm#YYYY",1],
  ["abbreviation expansion","Hotstrings.htm"],
  ["Abs","lib/Math.htm#Abs",2],
  ["absolute value, Abs","lib/Math.htm#Abs"],
  ["Acknowledgements","misc/Acknowledgements.htm"],
  ["ACos","lib/Math.htm#ACos",2],
  ["activate a window","lib/WinActivate.htm"],
  ["ActiveX controls (GUI)","lib/GuiControls.htm#ActiveX"],
  ["Add method (Gui)","lib/Gui.htm#Add",7],
  ["Add method (Gui.List)","lib/GuiControl.htm#Add",7],
  ["Add method (Gui.ListView)","lib/ListView.htm#Add",7],
  ["Add method (Gui.TreeView)","lib/TreeView.htm#Add",7],
  ["Add method (Menu)","lib/Menu.htm#Add",7],
  ["AddResource directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#AddResource",99],
  ["address of a variable","lib/StrPtr.htm"],
  ["AddStandard method (Menu)","lib/Menu.htm#AddStandard",7],
  ["administrator privileges for scripts","Variables.htm#RequireAdmin"],
  ["ahk_class","misc/WinTitle.htm#ahk_class"],
  ["ahk_exe","misc/WinTitle.htm#ahk_exe"],
  ["ahk_group","misc/WinTitle.htm#ahk_group"],
  ["ahk_id","misc/WinTitle.htm#ahk_id"],
  ["ahk_pid","misc/WinTitle.htm#ahk_pid"],
  ["Ahk2Exe","Scripts.htm#ahk2exe"],
  ["alnum","lib/Is.htm"],
  ["alpha","lib/Is.htm"],
  ["AltGr","Hotkeys.htm#AltGr"],
  ["AltTab","Hotkeys.htm#alttab"],
  ["and","Variables.htm#and",4],
  ["Any","lib/Any.htm",6],
  ["append to file","lib/FileAppend.htm"],
  ["Array","lib/Array.htm",6],
  ["Array","lib/Array.htm#Call",2],
  ["Arrays (general information)","Objects.htm"],
  ["ASin","lib/Math.htm#ASin",2],
  ["assigning values to variables","Variables.htm#AssignOp"],
  ["ATan","lib/Math.htm#ATan",2],
  ["AtEOF property (File)","lib/File.htm#AtEOF",7],
  ["attributes of files and folders","lib/FileGetAttrib.htm"],
  ["auto-execute thread","Scripts.htm#auto"],
  ["auto-replace text as you type it","Hotstrings.htm"],
  ["BackColor property (Gui)","lib/Gui.htm#BackColor",7],
  ["BackspaceIsUndo property (InputHook)","lib/InputHook.htm#BackspaceIsUndo",7],
  ["balloon tip","lib/TrayTip.htm"],
  ["base (Objects)","Objects.htm#Custom_Objects"],
  ["Base directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#Bin",99],
  ["Base property (Any)","lib/Any.htm#Base",7],
  ["Base property (Object)","lib/Object.htm#Base",7],
  ["beep the PC speaker","lib/SoundBeep.htm"],
  ["Bin directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#Bin",99],
  ["Bind method (Func)","lib/Func.htm#Bind",7],
  ["BinMod script (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#PostExec",99],
  ["bitwise operations","Variables.htm#bitshift"],
  ["blind-mode Send","lib/Send.htm#blind"],
  ["BlockInput","lib/BlockInput.htm",2],
  ["blocks (lines enclosed in braces)","lib/Block.htm"],
  ["boolean (concepts)","Concepts.htm#boolean"],
  ["BoundFunc","misc/Functor.htm#BoundFunc",6],
  ["Break","lib/Break.htm",3,"S"],
  ["Buffer","lib/Buffer.htm",6],
  ["Buffer","lib/Buffer.htm#Call",2],
  ["buffering","lib/_MaxThreadsBuffer.htm"],
  ["built-in classes","ObjList.htm"],
  ["built-in functions","Functions.htm#BuiltIn"],
  ["built-in variables","Variables.htm#BuiltIn"],
  ["Button controls (GUI)","lib/GuiControls.htm#Button"],
  ["button list (mouse and controller)","KeyList.htm"],
  ["button state","lib/GetKeyState.htm"],
  ["caching (concepts)","Concepts.htm#caching"],
  ["Call method (Array)","lib/Array.htm#Call",7],
  ["Call method (Buffer)","lib/Buffer.htm#Call",7],
  ["Call method (Class)","lib/Class.htm#Call",7],
  ["Call method (Enumerator)","lib/Enumerator.htm#Call",7],
  ["Call method (Func)","lib/Func.htm#Call",7],
  ["Call method (Gui)","lib/Gui.htm#Call",7],
  ["Call method (Map)","lib/Map.htm#Call",7],
  ["Call method (Menu)","lib/Menu.htm#Call",7],
  ["Call method (Object)","lib/Object.htm#Call",7],
  ["CallbackCreate","lib/CallbackCreate.htm",2],
  ["CallbackFree","lib/CallbackCreate.htm#CallbackFree",2],
  ["Capacity property (Array)","lib/Array.htm#Capacity",7],
  ["Capacity property (Map)","lib/Map.htm#Capacity",7],
  ["CaretGetPos","lib/CaretGetPos.htm",2],
  ["Case","lib/Switch.htm",3,"E",true],
  ["CaseSense property (Map)","lib/Map.htm#CaseSense",7],
  ["CaseSensitive property (InputHook)","lib/InputHook.htm#CaseSensitive",7],
  ["Catch","lib/Catch.htm",3,"E"],
  ["Ceil","lib/Math.htm#Ceil",2],
  ["Changelog","ChangeLog.htm"],
  ["Check method (Menu)","lib/Menu.htm#Check",7],
  ["CheckBox controls (GUI)","lib/GuiControls.htm#CheckBox"],
  ["choose file","lib/FileSelect.htm"],
  ["choose folder","lib/DirSelect.htm"],
  ["Choose method (Gui.List)","lib/GuiControl.htm#Choose",7],
  ["Chr","lib/Chr.htm",2],
  ["class","Objects.htm#Custom_Classes",5],
  ["Class","lib/Class.htm",6],
  ["Class","lib/Class.htm#Call",2],
  ["class name of a window","misc/WinTitle.htm#ahk_class"],
  ["Class object","lib/Class.htm"],
  ["classes, built-in","ObjList.htm"],
  ["ClassNN (of a control)","lib/ControlGetClassNN.htm"],
  ["ClassNN property (Gui.Control)","lib/GuiControl.htm#ClassNN",7],
  ["Clear method (Map)","lib/Map.htm#Clear",7],
  ["Click","lib/Click.htm",2],
  ["Click a mouse button","lib/Click.htm"],
  ["ClickCount property (Menu)","lib/Menu.htm#ClickCount",7],
  ["Clipboard","lib/A_Clipboard.htm"],
  ["ClipboardAll","lib/ClipboardAll.htm",2],
  ["ClipboardAll","lib/ClipboardAll.htm",6],
  ["ClipWait","lib/ClipWait.htm",2],
  ["Clone method (Array)","lib/Array.htm#Clone",7],
  ["Clone method (Map)","lib/Map.htm#Clone",7],
  ["Clone method (Object)","lib/Object.htm#Clone",7],
  ["Close (Gui event)","lib/GuiOnEvent.htm#Close"],
  ["close a window","lib/WinClose.htm"],
  ["Close method (File)","lib/File.htm#Close",7],
  ["Closure","Functions.htm#closures",6],
  ["CLSID List (Recycle Bin, etc.)","misc/CLSID-List.htm"],
  ["coalescing operator","Variables.htm#or-maybe"],
  ["color names, RGB/HTML","misc/Colors.htm"],
  ["color of pixels","lib/PixelSearch.htm"],
  ["COM","lib/ComObject.htm"],
  ["ComboBox controls (GUI)","lib/GuiControls.htm#ComboBox"],
  ["ComCall","lib/ComCall.htm",2],
  ["comma operator (multi-statement)","Variables.htm#comma"],
  ["command line parameters","Scripts.htm#cmd"],
  ["comments in scripts","Language.htm#comments"],
  ["ComObjActive","lib/ComObjActive.htm",2],
  ["ComObjArray","lib/ComObjArray.htm",2],
  ["ComObjArray","lib/ComObjArray.htm",6],
  ["ComObjConnect","lib/ComObjConnect.htm",2],
  ["ComObject","lib/ComObject.htm",2],
  ["ComObject","lib/ComObject.htm",6],
  ["ComObjFlags","lib/ComObjFlags.htm",2],
  ["ComObjFromPtr","lib/ComObjFromPtr.htm",2],
  ["ComObjGet","lib/ComObjGet.htm",2],
  ["ComObjQuery","lib/ComObjQuery.htm",2],
  ["ComObjType","lib/ComObjType.htm",2],
  ["ComObjValue","lib/ComObjValue.htm",2],
  ["Compatibility","Compat.htm"],
  ["compile a script","Scripts.htm#ahk2exe"],
  ["Compiler Directives","misc/Ahk2ExeDirectives.htm"],
  ["Compression","Scripts.htm#mpress"],
  ["ComValue","lib/ComValue.htm",2],
  ["ComValue","lib/ComValue.htm",6],
  ["ComValueRef","lib/ComValue.htm",6],
  ["concatenate, in expressions","Variables.htm#concat"],
  ["concatenate, script lines","Scripts.htm#continuation"],
  ["concepts and conventions","Concepts.htm"],
  ["ConsoleApp directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#ConsoleApp",99],
  ["Cont directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#Cont",99],
  ["ContextMenu (Gui event)","lib/GuiOnEvent.htm#ContextMenu"],
  ["continuation sections","Scripts.htm#continuation"],
  ["Continue","lib/Continue.htm",3,"S"],
  ["control flow","Language.htm#control-flow"],
  ["Control functions","lib/Control.htm"],
  ["ControlAddItem","lib/ControlAddItem.htm",2],
  ["ControlChooseIndex","lib/ControlChooseIndex.htm",2],
  ["ControlChooseString","lib/ControlChooseString.htm",2],
  ["ControlClick","lib/ControlClick.htm",2],
  ["ControlDeleteItem","lib/ControlDeleteItem.htm",2],
  ["ControlFindItem","lib/ControlFindItem.htm",2],
  ["ControlFocus","lib/ControlFocus.htm",2],
  ["ControlGetChecked","lib/ControlGetChecked.htm",2],
  ["ControlGetChoice","lib/ControlGetChoice.htm",2],
  ["ControlGetClassNN","lib/ControlGetClassNN.htm",2],
  ["ControlGetEnabled","lib/ControlGetEnabled.htm",2],
  ["ControlGetExStyle","lib/ControlGetStyle.htm",2],
  ["ControlGetFocus","lib/ControlGetFocus.htm",2],
  ["ControlGetHwnd","lib/ControlGetHwnd.htm",2],
  ["ControlGetIndex","lib/ControlGetIndex.htm",2],
  ["ControlGetItems","lib/ControlGetItems.htm",2],
  ["ControlGetPos","lib/ControlGetPos.htm",2],
  ["ControlGetStyle","lib/ControlGetStyle.htm",2],
  ["ControlGetText","lib/ControlGetText.htm",2],
  ["ControlGetVisible","lib/ControlGetVisible.htm",2],
  ["ControlHide","lib/ControlHide.htm",2],
  ["ControlHideDropDown","lib/ControlHideDropDown.htm",2],
  ["ControlMove","lib/ControlMove.htm",2],
  ["ControlSend","lib/ControlSend.htm",2],
  ["ControlSendText","lib/ControlSend.htm",2],
  ["ControlSetChecked","lib/ControlSetChecked.htm",2],
  ["ControlSetEnabled","lib/ControlSetEnabled.htm",2],
  ["ControlSetExStyle","lib/ControlSetStyle.htm",2],
  ["ControlSetStyle","lib/ControlSetStyle.htm",2],
  ["ControlSetText","lib/ControlSetText.htm",2],
  ["ControlShow","lib/ControlShow.htm",2],
  ["ControlShowDropDown","lib/ControlShowDropDown.htm",2],
  ["convert a script to an EXE","Scripts.htm#ahk2exe"],
  ["coordinates","lib/CoordMode.htm"],
  ["CoordMode","lib/CoordMode.htm",2],
  ["copy file","lib/FileCopy.htm"],
  ["copy folder/directory","lib/DirCopy.htm"],
  ["Cos","lib/Math.htm#Cos",2],
  ["Count property (Map)","lib/Map.htm#Count",7],
  ["Count property (Match)","lib/RegExMatch.htm#MatchObject",7],
  ["create file","lib/FileAppend.htm"],
  ["create folder/directory","lib/DirCreate.htm"],
  ["Critical","lib/Critical.htm",2],
  ["current directory","lib/SetWorkingDir.htm"],
  ["current thread","misc/Threads.htm"],
  ["cursor shape","Variables.htm#Cursor"],
  ["custom combination hotkeys","Hotkeys.htm#combo"],
  ["Custom controls (GUI)","lib/GuiControls.htm#Custom"],
  ["Dash","Program.htm#dash"],
  ["DateAdd","lib/DateAdd.htm",2],
  ["DateDiff","lib/DateDiff.htm",2],
  ["dates and times (of files)","lib/FileSetTime.htm"],
  ["DateTime controls (GUI)","lib/GuiControls.htm#DateTime"],
  ["Debug directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#Debug",99],
  ["debugger","lib/OutputDebug.htm"],
  ["debugging a script","Scripts.htm#debug"],
  ["decimal places","lib/Format.htm"],
  ["Default","lib/Switch.htm",3,"",true],
  ["Default property (Array)","lib/Array.htm#Default",7],
  ["Default property (Map)","lib/Map.htm#Default",7],
  ["Default property (Menu)","lib/Menu.htm#Default",7],
  ["DefineProp method (Object)","lib/Object.htm#DefineProp",7],
  ["delete files","lib/FileDelete.htm"],
  ["delete folder/directory","lib/DirDelete.htm"],
  ["Delete method (Array)","lib/Array.htm#Delete",7],
  ["Delete method (Gui.List)","lib/GuiControl.htm#Delete",7],
  ["Delete method (Gui.ListView)","lib/ListView.htm#Delete",7],
  ["Delete method (Gui.TreeView)","lib/TreeView.htm#Delete",7],
  ["Delete method (Map)","lib/Map.htm#Delete",7],
  ["Delete method (Menu)","lib/Menu.htm#Delete",7],
  ["DeleteCol method (Gui.ListView)","lib/ListView.htm#DeleteCol",7],
  ["DeleteProp method (Object)","lib/Object.htm#DeleteProp",7],
  ["dereference","Variables.htm#deref"],
  ["Destroy method (Gui)","lib/Gui.htm#Destroy",7],
  ["DetectHiddenText","lib/DetectHiddenText.htm",2],
  ["DetectHiddenWindows","lib/DetectHiddenWindows.htm",2],
  ["dialog DirSelect","lib/DirSelect.htm"],
  ["dialog FileSelect","lib/FileSelect.htm"],
  ["dialog InputBox","lib/InputBox.htm"],
  ["dialog MsgBox","lib/MsgBox.htm"],
  ["digit","lib/Is.htm"],
  ["DirCopy","lib/DirCopy.htm",2],
  ["DirCreate","lib/DirCreate.htm",2],
  ["DirDelete","lib/DirDelete.htm",2],
  ["DirExist","lib/DirExist.htm",2],
  ["DirMove","lib/DirMove.htm",2],
  ["DirSelect","lib/DirSelect.htm",2],
  ["Disable method (Menu)","lib/Menu.htm#Disable",7],
  ["disk space","lib/DriveGetSpaceFree.htm"],
  ["divide (math)","Variables.htm#divide"],
  ["DllCall","lib/DllCall.htm",2],
  ["double-deref","Variables.htm#deref"],
  ["Download","lib/Download.htm",2],
  ["DPI scaling","misc/DPIScaling.htm"],
  ["drag and drop (GUI windows)","lib/GuiOnEvent.htm#DropFiles"],
  ["drag the mouse","lib/MouseClickDrag.htm"],
  ["Drive functions","lib/Drive.htm"],
  ["DriveEject","lib/DriveEject.htm",2],
  ["DriveGetCapacity","lib/DriveGetCapacity.htm",2],
  ["DriveGetFileSystem","lib/DriveGetFileSystem.htm",2],
  ["DriveGetLabel","lib/DriveGetLabel.htm",2],
  ["DriveGetList","lib/DriveGetList.htm",2],
  ["DriveGetSerial","lib/DriveGetSerial.htm",2],
  ["DriveGetSpaceFree","lib/DriveGetSpaceFree.htm",2],
  ["DriveGetStatus","lib/DriveGetStatus.htm",2],
  ["DriveGetStatusCD","lib/DriveGetStatusCD.htm",2],
  ["DriveGetType","lib/DriveGetType.htm",2],
  ["DriveLock","lib/DriveLock.htm",2],
  ["DriveRetract","lib/DriveEject.htm",2],
  ["DriveSetLabel","lib/DriveSetLabel.htm",2],
  ["DriveUnlock","lib/DriveUnlock.htm",2],
  ["DropDownList controls (GUI)","lib/GuiControls.htm#DropDownList"],
  ["DropFiles (Gui event)","lib/GuiOnEvent.htm#DropFiles"],
  ["Dynamic function calls","Functions.htm#DynCall"],
  ["dynamic variables","Language.htm#dynamic-variables"],
  ["Edit","lib/Edit.htm",2],
  ["Edit controls (GUI)","lib/GuiControls.htm#Edit"],
  ["EditGetCurrentCol","lib/EditGetCurrentCol.htm",2],
  ["EditGetCurrentLine","lib/EditGetCurrentLine.htm",2],
  ["EditGetLine","lib/EditGetLine.htm",2],
  ["EditGetLineCount","lib/EditGetLineCount.htm",2],
  ["EditGetSelectedText","lib/EditGetSelectedText.htm",2],
  ["editors","misc/Editors.htm"],
  ["EditPaste","lib/EditPaste.htm",2],
  ["Else","lib/Else.htm",3,""],
  ["Enable method (Menu)","lib/Menu.htm#Enable",7],
  ["Enabled property (Gui.Control)","lib/GuiControl.htm#Enabled",7],
  ["Encoding property (File)","lib/File.htm#Encoding",7],
  ["EndChars (Hotstring)","lib/Hotstring.htm#EndChars"],
  ["EndKey property (InputHook)","lib/InputHook.htm#EndKey",7],
  ["EndMods property (InputHook)","lib/InputHook.htm#EndMods",7],
  ["EndReason property (InputHook)","lib/InputHook.htm#EndReason",7],
  ["Enumerator","lib/Enumerator.htm",6],
  ["EnvGet","lib/EnvGet.htm",2],
  ["environment variables","Concepts.htm#environment-variables"],
  ["environment variables (change them)","lib/EnvSet.htm"],
  ["EnvSet","lib/EnvSet.htm",2],
  ["Error","lib/Error.htm",6],
  ["Error","lib/Error.htm#new",2],
  ["ErrorStdOut","lib/_ErrorStdOut.htm"],
  ["Escape (Gui event)","lib/GuiOnEvent.htm#Escape"],
  ["escape sequence","misc/EscapeChar.htm"],
  ["ExeName directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#ExeName",99],
  ["Exit","lib/Exit.htm",2],
  ["ExitApp","lib/ExitApp.htm",2],
  ["Exp","lib/Math.htm#Exp",2],
  ["expressions","Variables.htm#Expressions"],
  ["Extra property (Error)","lib/Error.htm#Extra",7],
  ["False","Variables.htm#Boolean",1],
  ["FAQ (Frequently Asked Questions)","FAQ.htm"],
  ["fat arrow functions","Variables.htm#fat-arrow"],
  ["File","lib/File.htm",6],
  ["file attributes","lib/FileSetAttrib.htm"],
  ["file or folder (does it exist)","lib/FileExist.htm"],
  ["file pattern","lib/LoopFiles.htm"],
  ["File property (Error)","lib/Error.htm#File",7],
  ["file, creating","lib/FileAppend.htm"],
  ["file, reading","lib/LoopRead.htm"],
  ["file, writing/appending","lib/FileAppend.htm"],
  ["FileAppend","lib/FileAppend.htm",2],
  ["FileCopy","lib/FileCopy.htm",2],
  ["FileCreateShortcut","lib/FileCreateShortcut.htm",2],
  ["FileDelete","lib/FileDelete.htm",2],
  ["FileEncoding","lib/FileEncoding.htm",2],
  ["FileExist","lib/FileExist.htm",2],
  ["FileGetAttrib","lib/FileGetAttrib.htm",2],
  ["FileGetShortcut","lib/FileGetShortcut.htm",2],
  ["FileGetSize","lib/FileGetSize.htm",2],
  ["FileGetTime","lib/FileGetTime.htm",2],
  ["FileGetVersion","lib/FileGetVersion.htm",2],
  ["FileInstall","lib/FileInstall.htm",2],
  ["FileMove","lib/FileMove.htm",2],
  ["FileOpen","lib/FileOpen.htm",2],
  ["FileRead","lib/FileRead.htm",2],
  ["FileRecycle","lib/FileRecycle.htm",2],
  ["FileRecycleEmpty","lib/FileRecycleEmpty.htm",2],
  ["FileSelect","lib/FileSelect.htm",2],
  ["FileSetAttrib","lib/FileSetAttrib.htm",2],
  ["FileSetTime","lib/FileSetTime.htm",2],
  ["Finally","lib/Finally.htm",3,""],
  ["find a file","lib/LoopFiles.htm"],
  ["find a string","lib/InStr.htm"],
  ["find a window","lib/WinExist.htm"],
  ["FindAnywhere property (InputHook)","lib/InputHook.htm#FindAnywhere",7],
  ["Flash method (Gui)","lib/Gui.htm#Flash",7],
  ["Float","lib/Float.htm",2],
  ["Float","lib/Float.htm",6],
  ["floating point (check if it is one)","lib/Is.htm"],
  ["floating point (Format)","lib/Format.htm"],
  ["Floor","lib/Math.htm#Floor",2],
  ["focus","lib/ControlFocus.htm"],
  ["Focus method (Gui.Control)","lib/GuiControl.htm#Focus",7],
  ["Focused property (Gui.Control)","lib/GuiControl.htm#Focused",7],
  ["FocusedCtrl property (Gui)","lib/Gui.htm#FocusedCtrl",7],
  ["folder/directory copy","lib/DirCopy.htm"],
  ["folder/directory create","lib/DirCreate.htm"],
  ["folder/directory move","lib/DirMove.htm"],
  ["folder/directory remove","lib/DirDelete.htm"],
  ["folder/directory select","lib/DirSelect.htm"],
  ["Fonts","misc/FontsStandard.htm"],
  ["For","lib/For.htm",3,"IE"],
  ["Format","lib/Format.htm",2],
  ["FormatTime","lib/FormatTime.htm",2],
  ["free space","lib/DriveGetSpaceFree.htm"],
  ["FTP uploading example","lib/FileAppend.htm#ExFTP"],
  ["Func","lib/Func.htm",6],
  ["function objects","misc/Functor.htm"],
  ["functions (concepts)","Concepts.htm#functions"],
  ["functions (defining and calling)","Functions.htm"],
  ["functions, alphabetical list","lib/index.htm"],
  ["game automation","lib/PixelSearch.htm"],
  ["Get method (Array)","lib/Array.htm#Get",7],
  ["Get method (Map)","lib/Map.htm#Get",7],
  ["Get method (Gui.TreeView)","lib/TreeView.htm#Get",7],
  ["GetChild method (Gui.TreeView)","lib/TreeView.htm#GetChild",7],
  ["GetClientPos method (Gui)","lib/Gui.htm#GetClientPos",7],
  ["GetCount method (Gui.ListView)","lib/ListView.htm#GetCount",7],
  ["GetCount method (Gui.TreeView)","lib/TreeView.htm#GetCount",7],
  ["GetKeyName","lib/GetKeyName.htm",2],
  ["GetKeySC","lib/GetKeySC.htm",2],
  ["GetKeyState","lib/GetKeyState.htm",2],
  ["GetKeyVK","lib/GetKeyVK.htm",2],
  ["GetMethod","lib/GetMethod.htm",2],
  ["GetMethod method (Any)","lib/Any.htm#GetMethod",7],
  ["GetNext method (Gui.ListView)","lib/ListView.htm#GetNext",7],
  ["GetNext method (Gui.TreeView)","lib/TreeView.htm#GetNext",7],
  ["GetOwnPropDesc method (Object)","lib/Object.htm#GetOwnPropDesc",7],
  ["GetParent method (Gui.TreeView)","lib/TreeView.htm#GetParent",7],
  ["GetPos method (Gui)","lib/Gui.htm#GetPos",7],
  ["GetPos method (Gui.Control)","lib/GuiControl.htm#GetPos",7],
  ["GetPrev method (Gui.TreeView)","lib/TreeView.htm#GetPrev",7],
  ["GetSelection method (Gui.TreeView)","lib/TreeView.htm#GetSelection",7],
  ["GetText method (Gui.ListView)","lib/ListView.htm#GetText",7],
  ["GetText method (Gui.TreeView)","lib/TreeView.htm#GetText",7],
  ["global","Functions.htm#Global",5],
  ["global code","Language.htm#global-code"],
  ["global variables in functions","Functions.htm#Global"],
  ["Goto","lib/Goto.htm",3,"S"],
  ["GroupActivate","lib/GroupActivate.htm",2],
  ["GroupAdd","lib/GroupAdd.htm",2],
  ["GroupBox controls (GUI)","lib/GuiControls.htm#GroupBox"],
  ["GroupClose","lib/GroupClose.htm",2],
  ["GroupDeactivate","lib/GroupDeactivate.htm",2],
  ["Gui","lib/Gui.htm",6],
  ["Gui","lib/Gui.htm#Call",2],
  ["Gui control options","lib/Gui.htm#Add"],
  ["Gui control types","lib/GuiControls.htm"],
  ["Gui events","lib/GuiOnEvent.htm"],
  ["Gui property (Gui.Control)","lib/GuiControl.htm#Gui",7],
  ["Gui styles reference","misc/Styles.htm"],
  ["GuiControl object","lib/GuiControl.htm"],
  ["GuiCtrlFromHwnd","lib/GuiCtrlFromHwnd.htm",2],
  ["GuiFromHwnd","lib/GuiFromHwnd.htm",2],
  ["Handle property (File)","lib/File.htm#Handle",7],
  ["Handle property (Menu)","lib/Menu.htm#Handle",7],
  ["Has method (Array)","lib/Array.htm#Has",7],
  ["Has method (Map)","lib/Map.htm#Has",7],
  ["HasBase","lib/HasBase.htm",2],
  ["HasBase method (Any)","lib/Any.htm#HasBase",7],
  ["HasMethod","lib/HasMethod.htm",2],
  ["HasMethod method (Any)","lib/Any.htm#HasMethod",7],
  ["HasOwnProp method (Object)","lib/Object.htm#HasOwnProp",7],
  ["HasProp","lib/HasProp.htm",2],
  ["HasProp method (Any)","lib/Any.htm#HasProp",7],
  ["HBITMAP:","misc/ImageHandles.htm"],
  ["hexadecimal format","lib/Format.htm"],
  ["hibernate or suspend","lib/Shutdown.htm#ExSuspend"],
  ["HICON:","misc/ImageHandles.htm"],
  ["hidden text","lib/DetectHiddenText.htm"],
  ["hidden windows","lib/DetectHiddenWindows.htm"],
  ["Hide method (Gui)","lib/Gui.htm#Hide",7],
  ["HKEY_CLASSES_ROOT (HKCR)","lib/RegRead.htm"],
  ["HKEY_CURRENT_CONFIG (HKCC)","lib/RegRead.htm"],
  ["HKEY_CURRENT_USER (HKCU)","lib/RegRead.htm"],
  ["HKEY_LOCAL_MACHINE (HKLM)","lib/RegRead.htm"],
  ["HKEY_USERS (HKU)","lib/RegRead.htm"],
  ["hook","lib/InstallKeybdHook.htm"],
  ["HotIf","lib/HotIf.htm",2],
  ["HotIfWinActive","lib/HotIf.htm#IfWin",2],
  ["HotIfWinExist","lib/HotIf.htm#IfWin",2],
  ["HotIfWinNotActive","lib/HotIf.htm#IfWin",2],
  ["HotIfWinNotExist","lib/HotIf.htm#IfWin",2],
  ["Hotkey","lib/Hotkey.htm",2],
  ["Hotkey controls (GUI)","lib/GuiControls.htm#Hotkey"],
  ["Hotkey, ListHotkeys","lib/ListHotkeys.htm"],
  ["Hotkey, other features","HotkeyFeatures.htm"],
  ["Hotkeys (general information)","Hotkeys.htm"],
  ["Hotstring","lib/Hotstring.htm",2],
  ["Hotstrings (general information)","Hotstrings.htm"],
  ["HTML color names","misc/Colors.htm"],
  ["HWND (of a control)","lib/ControlGetHwnd.htm"],
  ["HWND (of a window)","misc/WinTitle.htm#ahk_id"],
  ["Hwnd property (Gui)","lib/Gui.htm#Hwnd",7],
  ["Hwnd property (Gui.Control)","lib/GuiControl.htm#Hwnd",7],
  ["icon, changing","lib/TraySetIcon.htm"],
  ["ID number for a window","lib/WinExist.htm"],
  ["If","lib/If.htm",3,"E"],
  ["IgnoreBegin directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#IgnoreKeep",99],
  ["IgnoreEnd directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#IgnoreKeep",99],
  ["IL_Add","lib/ListView.htm#IL_Add",2],
  ["IL_Create","lib/ListView.htm#IL_Create",2],
  ["IL_Destroy","lib/ListView.htm#IL_Destroy",2],
  ["Image Lists (GUI)","lib/ListView.htm#IL"],
  ["ImageSearch","lib/ImageSearch.htm",2],
  ["Include","lib/_Include.htm"],
  ["IndexError","lib/Error.htm#IndexError",6],
  ["infrared remote controls","scripts/index.htm#WinLIRC"],
  ["IniDelete","lib/IniDelete.htm",2],
  ["IniRead","lib/IniRead.htm",2],
  ["IniWrite","lib/IniWrite.htm",2],
  ["InProgress property (InputHook)","lib/InputHook.htm#InProgress",7],
  ["Input property (InputHook)","lib/InputHook.htm#Input",7],
  ["InputBox","lib/InputBox.htm",2],
  ["InputHook","lib/InputHook.htm",2],
  ["InputHook","lib/InputHook.htm",6],
  ["Insert method (Gui.ListView)","lib/ListView.htm#Insert",7],
  ["Insert method (Menu)","lib/Menu.htm#Insert",7],
  ["InsertAt method (Array)","lib/Array.htm#InsertAt",7],
  ["InsertCol method (Gui.ListView)","lib/ListView.htm#InsertCol",7],
  ["Install","howto/Install.htm"],
  ["installer options","Program.htm#install"],
  ["InstallKeybdHook","lib/InstallKeybdHook.htm",2],
  ["InstallMouseHook","lib/InstallMouseHook.htm",2],
  ["InStr","lib/InStr.htm",2],
  ["Integer","lib/Integer.htm",2],
  ["Integer","lib/Integer.htm",6],
  ["integer (check if it is one)","lib/Is.htm"],
  ["integer (Format)","lib/Format.htm"],
  ["Interrupt (Thread)","lib/Thread.htm#Interrupt"],
  ["is","Variables.htm#is",4],
  ["IsAlnum","lib/Is.htm#alnum",2],
  ["IsAlpha","lib/Is.htm#alpha",2],
  ["IsBuiltIn property (Func)","lib/Func.htm#IsBuiltIn",7],
  ["IsByRef method (Func)","lib/Func.htm#IsByRef",7],
  ["IsDigit","lib/Is.htm#digit",2],
  ["IsFloat","lib/Is.htm#float",2],
  ["IsInteger","lib/Is.htm#integer",2],
  ["IsLabel","lib/IsLabel.htm",2],
  ["IsLower","lib/Is.htm#lower",2],
  ["IsNumber","lib/Is.htm#number",2],
  ["IsObject","lib/IsObject.htm",2],
  ["IsOptional method (Func)","lib/Func.htm#IsOptional",7],
  ["IsSet","lib/IsSet.htm",2],
  ["IsSet","lib/IsSet.htm",4],
  ["IsSetRef","lib/IsSet.htm",2],
  ["IsSpace","lib/Is.htm#space",2],
  ["IsTime","lib/Is.htm#time",2],
  ["IsUpper","lib/Is.htm#upper",2],
  ["IsVariadic property (Func)","lib/Func.htm#IsVariadic",7],
  ["IsXDigit","lib/Is.htm#xdigit",2],
  ["Join (continuation sections)","Scripts.htm#Join"],
  ["Joystick","KeyList.htm#Controller"],
  ["Controller","KeyList.htm#Controller"],
  ["Gamepad","KeyList.htm#Controller"],
  ["JScript, embedded/inline","lib/DllCall.htm#COM"],
  ["Keep directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#IgnoreKeep",99],
  ["key list (keyboard, mouse, controller)","KeyList.htm"],
  ["key state","lib/GetKeyState.htm"],
  ["keyboard hook","lib/InstallKeybdHook.htm"],
  ["KeyHistory","lib/KeyHistory.htm",2],
  ["KeyOpt method (InputHook)","lib/InputHook.htm#KeyOpt",7],
  ["keystrokes, sending","lib/Send.htm"],
  ["KeyWait","lib/KeyWait.htm",2],
  ["labels","misc/Labels.htm"],
  ["language overview","Language.htm"],
  ["last found window","misc/WinTitle.htm#LastFoundWindow"],
  ["Launcher","Program.htm#launcher"],
  ["Len method/property (Match)","lib/RegExMatch.htm#MatchObject",7],
  ["length of a string","lib/StrLen.htm"],
  ["Length property (Array)","lib/Array.htm#Length",7],
  ["Length property (File)","lib/File.htm#Length",7],
  ["Let directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#Let",99],
  ["library (Lib) folders","Scripts.htm#lib"],
  ["license","license.htm"],
  ["line continuation","Scripts.htm#continuation"],
  ["Line property (Error)","lib/Error.htm#Line",7],
  ["Link controls (GUI)","lib/GuiControls.htm#Link"],
  ["ListBox controls (GUI)","lib/GuiControls.htm#ListBox"],
  ["ListHotkeys","lib/ListHotkeys.htm",2],
  ["ListLines","lib/ListLines.htm",2],
  ["ListVars","lib/ListVars.htm",2],
  ["ListView controls (GUI)","lib/ListView.htm"],
  ["ListViewGetContent","lib/ListViewGetContent.htm",2],
  ["Ln","lib/Math.htm#Ln",2],
  ["lnk (link/shortcut) file","lib/FileCreateShortcut.htm"],
  ["LoadPicture","lib/LoadPicture.htm",2],
  ["local","Functions.htm#Local",5],
  ["local variables","Functions.htm#Local"],
  ["Locale","lib/StrCompare.htm#Locale"],
  ["Log","lib/Math.htm#Log",2],
  ["logarithm, Log","lib/Math.htm#Log"],
  ["logoff","lib/Shutdown.htm"],
  ["long file name (converting to)","lib/LoopFiles.htm#LoopFilePath"],
  ["long paths","misc/LongPaths.htm"],
  ["Loop","lib/Loop.htm",3,"E"],
  ["Loop (until)","lib/Until.htm"],
  ["Loop (while)","lib/While.htm"],
  ["Loop Files","lib/LoopFiles.htm",3,"EE",true],
  ["Loop Parse","lib/LoopParse.htm",3,"EEE",true],
  ["Loop Read","lib/LoopRead.htm",3,"EE",true],
  ["Loop Reg","lib/LoopReg.htm",3,"EE",true],
  ["lParam","lib/SendMessage.htm"],
  ["LTrim","lib/Trim.htm",2],
  ["LTrim (continuation sections)","Scripts.htm#LTrim"],
  ["macro","misc/Macros.htm"],
  ["main window","Program.htm#main-window"],
  ["Map","lib/Map.htm",6],
  ["Map","lib/Map.htm#Call",2],
  ["Maps (general information)","Objects.htm"],
  ["MarginX property (Gui)","lib/Gui.htm#MarginX",7],
  ["MarginY property (Gui)","lib/Gui.htm#MarginY",7],
  ["Mark property (Match)","lib/RegExMatch.htm#MatchObject",7],
  ["Match property (InputHook)","lib/InputHook.htm#Match",7],
  ["math functions","lib/Math.htm"],
  ["math operations (expressions)","Variables.htm#Expressions"],
  ["Max","lib/Math.htm#Max",2],
  ["Maximize method (Gui)","lib/Gui.htm#Maximize",7],
  ["MaxParams property (Func)","lib/Func.htm#MaxParams",7],
  ["MaxThreads","lib/_MaxThreads.htm"],
  ["MaxThreadsBuffer","lib/_MaxThreadsBuffer.htm"],
  ["MaxThreadsPerHotkey","lib/_MaxThreadsPerHotkey.htm"],
  ["maybe operator","Variables.htm#maybe"],
  ["MemberError","lib/Error.htm#MemberError",6],
  ["MemoryError","lib/Error.htm#MemoryError",6],
  ["Menu","lib/Menu.htm",6],
  ["Menu","lib/Menu.htm#Call",2],
  ["MenuBar","lib/Menu.htm",6],
  ["MenuBar","lib/Menu.htm#Call",2],
  ["MenuBar property (Gui)","lib/Gui.htm#MenuBar",7],
  ["MenuFromHandle","lib/MenuFromHandle.htm",2],
  ["MenuSelect","lib/MenuSelect.htm",2],
  ["message list (WM_*)","misc/SendMessageList.htm"],
  ["Message property (Error)","lib/Error.htm#Message",7],
  ["messages, posting","lib/PostMessage.htm"],
  ["messages, receiving","lib/OnMessage.htm"],
  ["messages, sending","lib/SendMessage.htm"],
  ["meta-functions (Objects)","Objects.htm#Meta_Functions"],
  ["MethodError","lib/Error.htm#MemberError",6],
  ["methods (concepts)","Concepts.htm#methods"],
  ["Min","lib/Math.htm#Min",2],
  ["Minimize method (Gui)","lib/Gui.htm#Minimize",7],
  ["MinParams property (Func)","lib/Func.htm#MinParams",7],
  ["MinSendLevel property (InputHook)","lib/InputHook.htm#MinSendLevel",7],
  ["Mod","lib/Math.htm#Mod",2],
  ["modal (always on top)","lib/MsgBox.htm"],
  ["Modify method (Gui.ListView)","lib/ListView.htm#Modify",7],
  ["Modify method (Gui.TreeView)","lib/TreeView.htm#Modify",7],
  ["ModifyCol method (Gui.ListView)","lib/ListView.htm#ModifyCol",7],
  ["modulo, Mod","lib/Math.htm#Mod"],
  ["Monitor functions","lib/Monitor.htm"],
  ["MonitorGet","lib/MonitorGet.htm",2],
  ["MonitorGetCount","lib/MonitorGetCount.htm",2],
  ["MonitorGetName","lib/MonitorGetName.htm",2],
  ["MonitorGetPrimary","lib/MonitorGetPrimary.htm",2],
  ["MonitorGetWorkArea","lib/MonitorGetWorkArea.htm",2],
  ["MonthCal controls (GUI)","lib/GuiControls.htm#MonthCal"],
  ["mouse hook","lib/InstallMouseHook.htm"],
  ["mouse speed","lib/SetDefaultMouseSpeed.htm"],
  ["mouse wheel","lib/Click.htm"],
  ["MouseClick","lib/MouseClick.htm",2],
  ["MouseClickDrag","lib/MouseClickDrag.htm",2],
  ["MouseGetPos","lib/MouseGetPos.htm",2],
  ["MouseMove","lib/MouseMove.htm",2],
  ["MouseReset (Hotstring)","lib/Hotstring.htm#MouseReset"],
  ["move a window","lib/WinMove.htm"],
  ["move file","lib/FileMove.htm"],
  ["move folder/directory","lib/DirMove.htm"],
  ["Move method (Gui)","lib/Gui.htm#Move",7],
  ["Move method (Gui.Control)","lib/GuiControl.htm#Move",7],
  ["MsgBox","lib/MsgBox.htm",2],
  ["mute (changing it)","lib/SoundSetMute.htm"],
  ["Name method/property (Match)","lib/RegExMatch.htm#MatchObject",7],
  ["Name property (Func)","lib/Func.htm#Name",7],
  ["Name property (Gui)","lib/Gui.htm#Name",7],
  ["Name property (Gui.Control)","lib/GuiControl.htm#Name",7],
  ["names","Concepts.htm#names"],
  ["nested functions","Functions.htm#nested"],
  ["Nop directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#Nop",99],
  ["not","Variables.htm#not",4],
  ["nothing (concepts)","Concepts.htm#nothing"],
  ["NotifyNonText property (InputHook)","lib/InputHook.htm#NotifyNonText",7],
  ["NoTimers (Thread)","lib/Thread.htm#NoTimers"],
  ["NoTrayIcon","lib/_NoTrayIcon.htm"],
  ["Number","lib/Number.htm",2],
  ["Number","lib/Number.htm",6],
  ["number (check if it is one)","lib/Is.htm"],
  ["number format","Concepts.htm#numbers"],
  ["NumGet","lib/NumGet.htm",2],
  ["NumPut","lib/NumPut.htm",2],
  ["Obey directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#Obey",99],
  ["ObjAddRef","lib/ObjAddRef.htm",2],
  ["ObjBindMethod","lib/ObjBindMethod.htm",2],
  ["Object","lib/Object.htm",6],
  ["Object","lib/Object.htm#Call",2],
  ["object literal","Objects.htm#object-literal"],
  ["Objects (general information)","Objects.htm"],
  ["ObjFromPtr","Objects.htm#ObjFromPtr",2],
  ["ObjFromPtrAddRef","Objects.htm#ObjFromPtr",2],
  ["ObjGetBase","lib/Any.htm#GetBase",2],
  ["ObjGetCapacity","lib/Object.htm#GetCapacity",2],
  ["ObjHasOwnProp","lib/Object.htm#HasOwnProp",2],
  ["ObjOwnPropCount","lib/Object.htm#OwnPropCount",2],
  ["ObjOwnProps","lib/Object.htm#OwnProps",2],
  ["ObjPtr","Objects.htm#ObjPtr",2],
  ["ObjPtrAddRef","Objects.htm#ObjPtr",2],
  ["ObjRelease","lib/ObjAddRef.htm",2],
  ["ObjSetBase","lib/Object.htm#SetBase",2],
  ["ObjSetCapacity","lib/Object.htm#SetCapacity",2],
  ["OnChar property (InputHook)","lib/InputHook.htm#OnChar",7],
  ["OnClipboardChange","lib/OnClipboardChange.htm",2],
  ["OnCommand (Gui)","lib/GuiOnCommand.htm"],
  ["OnCommand method (Gui.Control)","lib/GuiControl.htm#OnCommand",7],
  ["OnEnd property (InputHook)","lib/InputHook.htm#OnEnd",7],
  ["OnError","lib/OnError.htm",2],
  ["OnEvent (Gui)","lib/GuiOnEvent.htm"],
  ["OnEvent method (Gui)","lib/Gui.htm#OnEvent",7],
  ["OnEvent method (Gui.Control)","lib/GuiControl.htm#OnEvent",7],
  ["OnExit","lib/OnExit.htm",2],
  ["OnKeyDown property (InputHook)","lib/InputHook.htm#OnKeyDown",7],
  ["OnKeyUp property (InputHook)","lib/InputHook.htm#OnKeyUp",7],
  ["OnMessage","lib/OnMessage.htm",2],
  ["OnNotify (Gui)","lib/GuiOnNotify.htm"],
  ["OnNotify method (Gui.Control)","lib/GuiControl.htm#OnNotify",7],
  ["open file","lib/FileOpen.htm"],
  ["operators in expressions","Variables.htm#Operators"],
  ["Opt method (Gui)","lib/Gui.htm#Opt",7],
  ["Opt method (Gui.Control)","lib/GuiControl.htm#Opt",7],
  ["optional parameters","Functions.htm#optional"],
  ["or","Variables.htm#or",4],
  ["Ord","lib/Ord.htm",2],
  ["OSError","lib/Error.htm#OSError",2],
  ["OSError","lib/Error.htm#OSError",6],
  ["OutputDebug","lib/OutputDebug.htm",2],
  ["OwnDialogs (GUI)","lib/Gui.htm#OwnDialogs"],
  ["Owner of a GUI window","lib/Gui.htm#Owner"],
  ["OwnProps method (Object)","lib/Object.htm#OwnProps",7],
  ["parameters (Functions)","Functions.htm#param"],
  ["parameters passed into a script","Scripts.htm#cmd"],
  ["parse a string (Loop)","lib/LoopParse.htm"],
  ["parse a string (StrSplit)","lib/StrSplit.htm"],
  ["Pause","lib/Pause.htm",2],
  ["performance of scripts","misc/Performance.htm"],
  ["Persistent","lib/Persistent.htm",2],
  ["Picture controls (GUI)","lib/GuiControls.htm#Picture"],
  ["PID (Process ID)","misc/WinTitle.htm#ahk_pid"],
  ["PixelGetColor","lib/PixelGetColor.htm",2],
  ["PixelSearch","lib/PixelSearch.htm",2],
  ["play a sound or video file","lib/SoundPlay.htm"],
  ["Pop method (Array)","lib/Array.htm#Pop",7],
  ["Pos method/property (Match)","lib/RegExMatch.htm#MatchObject",7],
  ["Pos property (File)","lib/File.htm#Pos",7],
  ["PostExec directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#PostExec",99],
  ["PostMessage","lib/PostMessage.htm",2],
  ["power (exponentiation)","Variables.htm#pow"],
  ["prefix and suffix keys","Hotkeys.htm"],
  ["Primitive","Objects.htm#primitive",6],
  ["print a file","lib/Run.htm"],
  ["Priority (Thread)","lib/Thread.htm#Priority"],
  ["priority of a process","lib/ProcessSetPriority.htm"],
  ["Process functions","lib/Process.htm"],
  ["ProcessClose","lib/ProcessClose.htm",2],
  ["ProcessExist","lib/ProcessExist.htm",2],
  ["ProcessGetName","lib/ProcessGetName.htm",2],
  ["ProcessGetParent","lib/ProcessGetParent.htm",2],
  ["ProcessGetPath","lib/ProcessGetName.htm",2],
  ["ProcessSetPriority","lib/ProcessSetPriority.htm",2],
  ["ProcessWait","lib/ProcessWait.htm",2],
  ["ProcessWaitClose","lib/ProcessWaitClose.htm",2],
  ["Progress controls (GUI)","lib/GuiControls.htm#Progress"],
  ["properties (Objects)","Objects.htm#Custom_Classes_property"],
  ["properties of a file or folder","lib/Run.htm"],
  ["PropertyError","lib/Error.htm#MemberError",6],
  ["Prototype property (Class)","lib/Class.htm#Prototype",7],
  ["Ptr property (Buffer)","lib/Buffer.htm#Ptr",7],
  ["Push method (Array)","lib/Array.htm#Push",7],
  ["quit script","lib/ExitApp.htm"],
  ["Radio controls (GUI)","lib/GuiControls.htm#Radio"],
  ["Random","lib/Random.htm",2],
  ["raw-mode Send","lib/Send.htm#SendRaw"],
  ["RawRead method (File)","lib/File.htm#RawRead",7],
  ["RawWrite method (File)","lib/File.htm#RawWrite",7],
  ["read file","lib/FileRead.htm"],
  ["Read method (File)","lib/File.htm#Read",7],
  ["ReadLine method (File)","lib/File.htm#ReadLine",7],
  ["ReadNumType method (File)","lib/File.htm#ReadNum",7],
  ["READONLY","lib/FileGetAttrib.htm"],
  ["reboot","lib/Shutdown.htm"],
  ["Redraw method (Gui.Control)","lib/GuiControl.htm#Redraw",7],
  ["reference counting","Objects.htm#Refs"],
  ["reference operator (&)","Variables.htm#ref"],
  ["REG_BINARY","lib/RegRead.htm"],
  ["REG_DWORD","lib/RegRead.htm"],
  ["REG_EXPAND_SZ","lib/RegRead.htm"],
  ["REG_MULTI_SZ","lib/RegRead.htm"],
  ["REG_SZ","lib/RegRead.htm"],
  ["RegCreateKey","lib/RegCreateKey.htm",2],
  ["RegDelete","lib/RegDelete.htm",2],
  ["RegDeleteKey","lib/RegDeleteKey.htm",2],
  ["RegEx: Callouts","misc/RegExCallout.htm"],
  ["RegEx: Quick Reference","misc/RegEx-QuickRef.htm"],
  ["RegEx: SetTitleMatchMode RegEx","lib/SetTitleMatchMode.htm#RegEx"],
  ["RegExMatch","lib/RegExMatch.htm",2],
  ["RegExMatchInfo","lib/RegExMatch.htm#MatchObject",6],
  ["RegExReplace","lib/RegExReplace.htm",2],
  ["registry loop","lib/LoopReg.htm"],
  ["RegRead","lib/RegRead.htm",2],
  ["Regular Expression Callouts","misc/RegExCallout.htm"],
  ["regular expressions: Quick Reference","misc/RegEx-QuickRef.htm"],
  ["regular expressions: RegExMatch","lib/RegExMatch.htm"],
  ["regular expressions: RegExReplace","lib/RegExReplace.htm"],
  ["regular expressions: SetTitleMatchMode RegEx","lib/SetTitleMatchMode.htm#RegEx"],
  ["RegWrite","lib/RegWrite.htm",2],
  ["Reload","lib/Reload.htm",2],
  ["remap controller","misc/RemapController.htm"],
  ["remap keys or mouse buttons","misc/Remap.htm"],
  ["remote controls, hand-held","scripts/index.htm#WinLIRC"],
  ["remove folder/directory","lib/DirDelete.htm"],
  ["RemoveAt method (Array)","lib/Array.htm#RemoveAt",7],
  ["rename file","lib/FileMove.htm"],
  ["Rename method (Menu)","lib/Menu.htm#Rename",7],
  ["Reset (Hotstring)","lib/Hotstring.htm#Reset"],
  ["resize a window","lib/WinMove.htm"],
  ["restart the computer","lib/Shutdown.htm"],
  ["Restore method (Gui)","lib/Gui.htm#Restore",7],
  ["Return","lib/Return.htm",3,"E"],
  ["RGB color names","misc/Colors.htm"],
  ["RGB colors","lib/PixelGetColor.htm"],
  ["Round","lib/Math.htm#Round",2],
  ["rounding a number","lib/Math.htm#Round"],
  ["RTrim","lib/Trim.htm",2],
  ["Run","lib/Run.htm",2],
  ["RunAs","lib/RunAs.htm",2],
  ["RunWait","lib/Run.htm",2],
  ["scan code","lib/Send.htm#vk"],
  ["scientific notation","lib/Format.htm"],
  ["Script Showcase","scripts/index.htm"],
  ["Scripts","Scripts.htm"],
  ["Seek method (File)","lib/File.htm#Seek",7],
  ["select file","lib/FileSelect.htm"],
  ["select folder","lib/DirSelect.htm"],
  ["Send","lib/Send.htm",2],
  ["SendEvent","lib/Send.htm#SendEvent",2],
  ["sending data between scripts","lib/OnMessage.htm#ExSendString"],
  ["SendInput","lib/Send.htm#SendInputDetail",2],
  ["SendLevel","lib/SendLevel.htm",2],
  ["SendMessage","lib/SendMessage.htm",2],
  ["SendMode","lib/SendMode.htm",2],
  ["SendPlay","lib/Send.htm#SendPlayDetail",2],
  ["SendText","lib/Send.htm",2],
  ["Set directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#Set",99],
  ["Set method (Map)","lib/Map.htm#Set",7],
  ["SetCapsLockState","lib/SetNumScrollCapsLockState.htm",2],
  ["SetColor method (Menu)","lib/Menu.htm#SetColor",7],
  ["SetControlDelay","lib/SetControlDelay.htm",2],
  ["SetDefaultMouseSpeed","lib/SetDefaultMouseSpeed.htm",2],
  ["SetFont method (Gui)","lib/Gui.htm#SetFont",7],
  ["SetFont method (Gui.Control)","lib/GuiControl.htm#SetFont",7],
  ["SetFormat method (Gui.DateTime)","lib/GuiControls.htm#DateTime_SetFormat",7],
  ["SetIcon method (Gui.StatusBar)","lib/GuiControls.htm#SB_SetIcon"],
  ["SetIcon method (Menu)","lib/Menu.htm#SetIcon",7],
  ["SetImageList method (Gui.ListView)","lib/ListView.htm#SetImageList",7],
  ["SetImageList method (Gui.TreeView)","lib/TreeView.htm#SetImageList",7],
  ["SetKeyDelay","lib/SetKeyDelay.htm",2],
  ["SetMainIcon directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#SetMainIcon",99],
  ["SetMouseDelay","lib/SetMouseDelay.htm",2],
  ["SetNumLockState","lib/SetNumScrollCapsLockState.htm",2],
  ["SetParts method (Gui.StatusBar)","lib/GuiControls.htm#SB_SetParts"],
  ["SetProp directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#SetProp",99],
  ["SetRegView","lib/SetRegView.htm",2],
  ["SetScrollLockState","lib/SetNumScrollCapsLockState.htm",2],
  ["SetStoreCapsLockMode","lib/SetStoreCapsLockMode.htm",2],
  ["SetText method (Gui.StatusBar)","lib/GuiControls.htm#SB_SetText"],
  ["SetTimer","lib/SetTimer.htm",2],
  ["SetTitleMatchMode","lib/SetTitleMatchMode.htm",2],
  ["SetWinDelay","lib/SetWinDelay.htm",2],
  ["SetWorkingDir","lib/SetWorkingDir.htm",2],
  ["short file name (8.3 format)","lib/LoopFiles.htm#LoopFileShortPath"],
  ["short-circuit boolean evaluation","Functions.htm#ShortCircuit"],
  ["shortcut file","lib/FileCreateShortcut.htm"],
  ["Show method (Gui)","lib/Gui.htm#Show",7],
  ["Show method (Menu)","lib/Menu.htm#Show",7],
  ["Shutdown","lib/Shutdown.htm",2],
  ["Silent Install/Uninstall","Program.htm#install"],
  ["Sin","lib/Math.htm#Sin",2],
  ["SingleInstance","lib/_SingleInstance.htm"],
  ["Size (Gui event)","lib/GuiOnEvent.htm#Size"],
  ["size of a file/folder","lib/FileGetSize.htm"],
  ["size of a window","lib/WinGetPos.htm"],
  ["Size property (Buffer)","lib/Buffer.htm#Size",7],
  ["Sleep","lib/Sleep.htm",2],
  ["Slider controls (GUI)","lib/GuiControls.htm#Slider"],
  ["Sort","lib/Sort.htm",2],
  ["Sound functions","lib/Sound.htm"],
  ["SoundBeep","lib/SoundBeep.htm",2],
  ["SoundGetInterface","lib/SoundGetInterface.htm",2],
  ["SoundGetMute","lib/SoundGetMute.htm",2],
  ["SoundGetName","lib/SoundGetName.htm",2],
  ["SoundGetVolume","lib/SoundGetVolume.htm",2],
  ["SoundPlay","lib/SoundPlay.htm",2],
  ["SoundSetMute","lib/SoundSetMute.htm",2],
  ["SoundSetVolume","lib/SoundSetVolume.htm",2],
  ["space","lib/Is.htm"],
  ["spinner control (GUI)","lib/GuiControls.htm#UpDown"],
  ["SplitPath","lib/SplitPath.htm",2],
  ["splitting long lines","Scripts.htm#continuation"],
  ["Sqrt","lib/Math.htm#Sqrt",2],
  ["Stack property (Error)","lib/Error.htm#Stack",7],
  ["standard library","Scripts.htm#lib"],
  ["standard output (stdout)","lib/FileAppend.htm"],
  ["Start method (InputHook)","lib/InputHook.htm#Start",7],
  ["static","Functions.htm#static",5],
  ["static functions","Functions.htm#static-functions"],
  ["static variables","Functions.htm#static"],
  ["StatusBar controls (GUI)","lib/GuiControls.htm#StatusBar"],
  ["StatusBarGetText","lib/StatusBarGetText.htm",2],
  ["StatusBarWait","lib/StatusBarWait.htm",2],
  ["Stop method (InputHook)","lib/InputHook.htm#Stop",7],
  ["StrCompare","lib/StrCompare.htm",2],
  ["StrGet","lib/StrGet.htm",2],
  ["String","lib/String.htm",2],
  ["String","lib/String.htm",6],
  ["string (search for)","lib/InStr.htm"],
  ["string: InStr","lib/InStr.htm"],
  ["string: SubStr","lib/SubStr.htm"],
  ["strings (concepts)","Concepts.htm#strings"],
  ["StrLen","lib/StrLen.htm",2],
  ["StrLower","lib/StrLower.htm",2],
  ["StrPtr","lib/StrPtr.htm",2],
  ["StrPut","lib/StrPut.htm",2],
  ["StrReplace","lib/StrReplace.htm",2],
  ["StrSplit","lib/StrSplit.htm",2],
  ["StrTitle","lib/StrLower.htm",2],
  ["structures, via DllCall","lib/DllCall.htm#struct"],
  ["StrUpper","lib/StrLower.htm",2],
  ["styles for GUI methods","misc/Styles.htm"],
  ["Submit method (Gui)","lib/Gui.htm#Submit",7],
  ["SubStr","lib/SubStr.htm",2],
  ["super","Objects.htm#Custom_Classes_super"],
  ["Suspend","lib/Suspend.htm",2],
  ["suspend or hibernate","lib/Shutdown.htm#ExSuspend"],
  ["Switch","lib/Switch.htm",3,"E"],
  ["SysGet","lib/SysGet.htm",2],
  ["SysGetIPAddresses","lib/SysGetIPAddresses.htm",2],
  ["Tab controls (GUI)","lib/GuiControls.htm#Tab"],
  ["Tan","lib/Math.htm#Tan",2],
  ["TargetError","lib/Error.htm#TargetError",6],
  ["terminate a window","lib/WinKill.htm"],
  ["terminate script","lib/ExitApp.htm"],
  ["ternary operator (?:)","Variables.htm#ternary"],
  ["Text controls (GUI)","lib/GuiControls.htm#Text"],
  ["Text property (Gui.Control)","lib/GuiControl.htm#Text",7],
  ["text-mode Send","lib/Send.htm#SendText"],
  ["ThisHotkey","Hotkeys.htm#ThisHotkey"],
  ["Thread","lib/Thread.htm",2],
  ["threads","misc/Threads.htm"],
  ["Throw","lib/Throw.htm",3,"E"],
  ["time","lib/Is.htm"],
  ["Timeout property (InputHook)","lib/InputHook.htm#Timeout",7],
  ["TimeoutError","lib/Error.htm#TimeoutError",6],
  ["Timer (timed subroutines)","lib/SetTimer.htm"],
  ["times and dates (compare)","lib/DateDiff.htm"],
  ["times and dates (math)","lib/DateAdd.htm"],
  ["times and dates (of files)","lib/FileSetTime.htm"],
  ["title of a window","lib/WinSetTitle.htm"],
  ["Title property (Gui)","lib/Gui.htm#Title",7],
  ["toast notification","lib/TrayTip.htm"],
  ["ToggleCheck method (Menu)","lib/Menu.htm#ToggleCheck",7],
  ["ToggleEnable method (Menu)","lib/Menu.htm#ToggleEnable",7],
  ["ToolTip","lib/ToolTip.htm",2],
  ["transparency of a window","lib/WinSetTransparent.htm"],
  ["tray icon","Program.htm#tray-icon"],
  ["tray menu (customizing)","Variables.htm#TrayMenu"],
  ["TraySetIcon","lib/TraySetIcon.htm",2],
  ["TrayTip","lib/TrayTip.htm",2],
  ["TreeView controls (GUI)","lib/TreeView.htm"],
  ["Trim","lib/Trim.htm",2],
  ["True","Variables.htm#Boolean",1],
  ["Try","lib/Try.htm",3,""],
  ["Tutorial","Tutorial.htm"],
  ["Type","lib/Type.htm",2],
  ["Type property (Gui.Control)","lib/GuiControl.htm#Type",7],
  ["TypeError","lib/Error.htm#TypeError",6],
  ["Uncheck method (Menu)","lib/Menu.htm#Uncheck",7],
  ["uninitialized variables","Concepts.htm#uninitialized-variables"],
  ["Unset","Language.htm#unset",1],
  ["UnsetError","lib/Error.htm#UnsetError",6],
  ["UnsetItemError","lib/Error.htm#UnsetError",6],
  ["Until","lib/Until.htm",3,"E"],
  ["UpdateManifest directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#UpdateManifest",99],
  ["UpDown controls (GUI)","lib/GuiControls.htm#UpDown"],
  ["UseHook","lib/_UseHook.htm"],
  ["user (run as a different user)","lib/RunAs.htm"],
  ["user library","Scripts.htm#lib"],
  ["UseResourceLang directive (Ahk2Exe)","misc/Ahk2ExeDirectives.htm#UseResourceLang",99],
  ["UseTab method (Gui.Tab)","lib/GuiControls.htm#Tab_UseTab",7],
  ["Value property (Gui.Control)","lib/GuiControl.htm#Value",7],
  ["ValueError","lib/Error.htm#ValueError",6],
  ["variables","Variables.htm"],
  ["variables, built-in","Variables.htm#BuiltIn"],
  ["variables, ListVars","lib/ListVars.htm"],
  ["variables, type of data","lib/Is.htm"],
  ["variadic functions","Functions.htm#Variadic"],
  ["variants (duplicate hotkeys and hotstrings)","lib/_HotIf.htm#variant"],
  ["VarRef","Concepts.htm#variable-references",6],
  ["VarSetStrCapacity","lib/VarSetStrCapacity.htm",2],
  ["VerCompare","lib/VerCompare.htm",2],
  ["version of a file","lib/FileGetVersion.htm"],
  ["virtual key","lib/Send.htm#vk"],
  ["Visible property (Gui.Control)","lib/GuiControl.htm#Visible",7],
  ["VisibleNonText property (InputHook)","lib/InputHook.htm#VisibleNonText",7],
  ["VisibleText property (InputHook)","lib/InputHook.htm#VisibleText",7],
  ["volume (changing it)","lib/SoundSetVolume.htm"],
  ["wait (sleep)","lib/Sleep.htm"],
  ["wait for a key to be released or pressed","lib/KeyWait.htm"],
  ["Wait method (InputHook)","lib/InputHook.htm#Wait",7],
  ["What property (Error)","lib/Error.htm#What",7],
  ["Wheel hotkeys for mouse","Hotkeys.htm#Wheel"],
  ["Wheel, simulating rotation","lib/Click.htm"],
  ["While","lib/While.htm",3,"E"],
  ["wildcards (for files & folders)","lib/LoopFiles.htm#wildcards"],
  ["Win functions","lib/Win.htm"],
  ["WinActivate","lib/WinActivate.htm",2],
  ["WinActivateBottom","lib/WinActivateBottom.htm",2],
  ["WinActivateForce","lib/_WinActivateForce.htm"],
  ["WinActive","lib/WinActive.htm",2],
  ["Winamp automation","misc/Winamp.htm"],
  ["WinClose","lib/WinClose.htm",2],
  ["window group","misc/WinTitle.htm#ahk_group"],
  ["window messages","misc/SendMessageList.htm"],
  ["WinExist","lib/WinExist.htm",2],
  ["WinGetClass","lib/WinGetClass.htm",2],
  ["WinGetClientPos","lib/WinGetClientPos.htm",2],
  ["WinGetControls","lib/WinGetControls.htm",2],
  ["WinGetControlsHwnd","lib/WinGetControlsHwnd.htm",2],
  ["WinGetCount","lib/WinGetCount.htm",2],
  ["WinGetExStyle","lib/WinGetStyle.htm",2],
  ["WinGetID","lib/WinGetID.htm",2],
  ["WinGetIDLast","lib/WinGetIDLast.htm",2],
  ["WinGetList","lib/WinGetList.htm",2],
  ["WinGetMinMax","lib/WinGetMinMax.htm",2],
  ["WinGetPID","lib/WinGetPID.htm",2],
  ["WinGetPos","lib/WinGetPos.htm",2],
  ["WinGetProcessName","lib/WinGetProcessName.htm",2],
  ["WinGetProcessPath","lib/WinGetProcessPath.htm",2],
  ["WinGetStyle","lib/WinGetStyle.htm",2],
  ["WinGetText","lib/WinGetText.htm",2],
  ["WinGetTitle","lib/WinGetTitle.htm",2],
  ["WinGetTransColor","lib/WinGetTransColor.htm",2],
  ["WinGetTransparent","lib/WinGetTransparent.htm",2],
  ["WinHide","lib/WinHide.htm",2],
  ["WinKill","lib/WinKill.htm",2],
  ["WinLIRC, connecting to","scripts/index.htm#WinLIRC"],
  ["WinMaximize","lib/WinMaximize.htm",2],
  ["WinMinimize","lib/WinMinimize.htm",2],
  ["WinMinimizeAll","lib/WinMinimizeAll.htm",2],
  ["WinMinimizeAllUndo","lib/WinMinimizeAll.htm",2],
  ["WinMove","lib/WinMove.htm",2],
  ["WinMoveBottom","lib/WinMoveBottom.htm",2],
  ["WinMoveTop","lib/WinMoveTop.htm",2],
  ["WinRedraw","lib/WinRedraw.htm",2],
  ["WinRestore","lib/WinRestore.htm",2],
  ["WinSetAlwaysOnTop","lib/WinSetAlwaysOnTop.htm",2],
  ["WinSetEnabled","lib/WinSetEnabled.htm",2],
  ["WinSetExStyle","lib/WinSetStyle.htm",2],
  ["WinSetRegion","lib/WinSetRegion.htm",2],
  ["WinSetStyle","lib/WinSetStyle.htm",2],
  ["WinSetTitle","lib/WinSetTitle.htm",2],
  ["WinSetTransColor","lib/WinSetTransColor.htm",2],
  ["WinSetTransparent","lib/WinSetTransparent.htm",2],
  ["WinShow","lib/WinShow.htm",2],
  ["WinSize (via WinMove)","lib/WinMove.htm"],
  ["WinTitle","misc/WinTitle.htm"],
  ["WinWait","lib/WinWait.htm",2],
  ["WinWaitActive","lib/WinWaitActive.htm",2],
  ["WinWaitClose","lib/WinWaitClose.htm",2],
  ["WinWaitNotActive","lib/WinWaitActive.htm",2],
  ["WM_* (Windows messages)","misc/SendMessageList.htm"],
  ["WM_COPYDATA","lib/OnMessage.htm#ExSendString"],
  ["working directory","lib/SetWorkingDir.htm"],
  ["wParam","lib/SendMessage.htm"],
  ["write file","lib/FileAppend.htm"],
  ["Write method (File)","lib/File.htm#Write",7],
  ["WriteLine method (File)","lib/File.htm#WriteLine",7],
  ["WriteNumType method (File)","lib/File.htm#WriteNum",7],
  ["WS_* (GUI styles)","misc/Styles.htm"],
  ["XButton","KeyList.htm#mouse-advanced"],
  ["YYYYMMDDHH24MISS","lib/FileSetTime.htm#YYYYMMDD"],
  ["ZeroDivisionError","lib/Error.htm#ZeroDivisionError",6],
  ["ZIP files (copying their contents)","lib/DirCopy.htm"]
];
