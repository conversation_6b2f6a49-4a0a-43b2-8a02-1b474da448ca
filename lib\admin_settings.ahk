#Requires AutoHotkey v2.0

; Settings functionality for WinCBT-Admin
; This module handles system-wide settings for the admin application

; Define paths
global SETTINGS_DB_PATH := IniRead(A_ScriptDir "\config.ini", "Paths", "dbPath", "db")
; Convert relative to absolute path if needed
if (SubStr(SETTINGS_DB_PATH, 1, 1) != "\" && SubStr(SETTINGS_DB_PATH, 2, 1) != ":") {
    SETTINGS_DB_PATH := A_ScriptDir "\" SETTINGS_DB_PATH "\"
} else if (SubStr(SETTINGS_DB_PATH, -1) != "\") {
    SETTINGS_DB_PATH := SETTINGS_DB_PATH "\"
}

; Define settings file paths
global SETTINGS_CONFIG_PATH := SETTINGS_DB_PATH "config.ini"
global SETTINGS_APP_CONFIG_PATH := A_ScriptDir "\config.ini"

; ; SettingsManager class
; ; Handles system-wide settings for the admin application
class SettingsManager {
    ; Settings categories
    static CATEGORIES := Map(
        "Company", ["Name", "Logo", "ContactEmail"],
        "Exam", ["ExamID", "Name", "Paper", "PaperID", "Subject", "TimeLimit", "Date", "StartTime", "EndTime", "PassingScore", "TotalMarks"],
        "Centre", ["ID", "Name", "Address", "City", "Zipcode", "ContactPerson", "ContactEmail", "ContactPhone", "TotalRooms", "TotalFloors"],
        "Biometric", ["EarliestApprovalTime", "RequiredForExam", "BiometricTimeout"],
        "Verification", ["SignatureVerification", "PhotoVerificationMode", "SignatureVerificationMode", "FingerprintVerificationMode", "EnablePostExamVerification", "RightThumbprintVerification", "PhotoConfidenceThreshold", "SignatureConfidenceThreshold", "FingerprintConfidenceThreshold", "FingerprintCaptureThreshold", "FingerprintAutoSaveThreshold", "FingerprintMode"],
        "Database", ["BackupInterval", "BackupLocation", "MaxBackups", "EnableAutoBackup"],
        "Network", ["SharedDatabasePath", "EnableNetworkSharing", "RefreshInterval"]
    )

    ; Default settings
    static DEFAULT_SETTINGS := Map(
        "Company", Map(
            "Name", "University of Information and Technology",
            "Logo", "db\img\company.jpg",
            "ContactEmail", "<EMAIL>"
        ),
        "Exam", Map(
            "ExamID", "EXAM098543",
            "Name", "Computer Networks",
            "Paper", "PAPER_B",
            "PaperID", "PAPER_B_2025",
            "Subject", "Networking",
            "TimeLimit", "300",
            "Date", "2025-05-11",
            "StartTime", "001200",
            "EndTime", "150000",
            "PassingScore", "60",
            "TotalMarks", "100"
        ),
        "Centre", Map(
            "ID", "CNTR654345",
            "Name", "Main Campus Test Center",
            "Address", "123 University Road, Technology Park",
            "City", "Techville",
            "Zipcode", "54321",
            "ContactPerson", "John Smith",
            "ContactEmail", "<EMAIL>",
            "ContactPhone", "1234567890",
            "TotalRooms", "10",
            "TotalFloors", "2"
        ),
        "Biometric", Map(
            "EarliestApprovalTime", "200000",
            "RequiredForExam", "1",
            "BiometricTimeout", "300"
        ),
        "Verification", Map(
            "SignatureVerification", "0",
            "PhotoVerificationMode", "Both",
            "SignatureVerificationMode", "Both",
            "FingerprintVerificationMode", "Both",
            "EnablePostExamVerification", "0",
            "RightThumbprintVerification", "1",
            "PhotoConfidenceThreshold", "85",
            "SignatureConfidenceThreshold", "85",
            "FingerprintConfidenceThreshold", "85",
            "FingerprintCaptureThreshold", "70",
            "FingerprintAutoSaveThreshold", "80",
            "FingerprintMode", "save"
        ),
        "Database", Map(
            "BackupInterval", "3600",
            "BackupLocation", "db\backup",
            "MaxBackups", "10",
            "EnableAutoBackup", "1"
        ),
        "Network", Map(
            "SharedDatabasePath", "",
            "EnableNetworkSharing", "0",
            "RefreshInterval", "300"
        )
    )

    ; Constructor
    __New() {
        ; Create config.ini if it doesn't exist
        if (!FileExist(SETTINGS_CONFIG_PATH)) {
            try {
                this.CreateDefaultConfig()
                ErrorHandler.LogMessage("INFO", "Created default config.ini")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create config.ini: " err.Message)
            }
        }

        ; Create app config.ini if it doesn't exist
        if (!FileExist(SETTINGS_APP_CONFIG_PATH)) {
            try {
                this.CreateDefaultAppConfig()
                ErrorHandler.LogMessage("INFO", "Created default app config.ini")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create app config.ini: " err.Message)
            }
        }
    }

    ; Create default config.ini
    CreateDefaultConfig() {
        for category, settings in SettingsManager.DEFAULT_SETTINGS {
            for key, value in settings {
                IniWrite(value, SETTINGS_CONFIG_PATH, category, key)
            }
        }
    }

    ; Create default app config.ini
    CreateDefaultAppConfig() {
        ; Create default application configuration
        defaultConfig := "[Settings]`n"
                      . "LogLevel=Info`n"
                      . "DebugMode=0`n"
                      . "AutoLogin=0`n"
                      . "DefaultOperator=admin`n`n"
                      . "[Paths]`n"
                      . "dbPath=db`n"
                      . "logsPath=logs`n"
                      . "tempPath=temp`n`n"
                      . "[Network]`n"
                      . "EnableNetworkSharing=0`n"
                      . "SharedDatabasePath=`n"
                      . "RefreshInterval=300`n"

        FileOpen(SETTINGS_APP_CONFIG_PATH, "w").Write(defaultConfig)
    }

    ; Get all settings
    ; Returns: Object with all settings
    GetAllSettings() {
        settings := Map()

        try {
            ; Read all categories and settings
            for category, keys in SettingsManager.CATEGORIES {
                settings[category] := Map()
                for key in keys {
                    settings[category][key] := IniRead(SETTINGS_CONFIG_PATH, category, key, SettingsManager.DEFAULT_SETTINGS[category][key])
                }
            }

            return settings
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get settings: " err.Message)
            return SettingsManager.DEFAULT_SETTINGS
        }
    }

    ; Get a specific setting
    ; Parameters:
    ; - category: The category of the setting
    ; - key: The key of the setting
    ; Returns: The value of the setting
    GetSetting(category, key) {
        try {
            ; Check if category and key are valid
            if (!SettingsManager.CATEGORIES.Has(category) || !HasValue(SettingsManager.CATEGORIES[category], key)) {
                throw Error("Invalid setting: " category "." key)
            }

            ; Read the setting
            return IniRead(SETTINGS_CONFIG_PATH, category, key, SettingsManager.DEFAULT_SETTINGS[category][key])
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get setting: " err.Message)
            return SettingsManager.DEFAULT_SETTINGS[category][key]
        }
    }

    ; Set a specific setting
    ; Parameters:
    ; - category: The category of the setting
    ; - key: The key of the setting
    ; - value: The value to set
    ; Returns: True if successful, False otherwise
    SetSetting(category, key, value) {
        try {
            ; Check if category and key are valid
            if (!SettingsManager.CATEGORIES.Has(category) || !HasValue(SettingsManager.CATEGORIES[category], key)) {
                throw Error("Invalid setting: " category "." key)
            }

            ; Write the setting
            IniWrite(value, SETTINGS_CONFIG_PATH, category, key)
            ErrorHandler.LogMessage("INFO", "Set setting " category "." key " to " value)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to set setting: " err.Message)
            return false
        }
    }

    ; Get all application settings
    ; Returns: Object with all application settings
    GetAllAppSettings() {
        settings := {}

        try {
            ; Read all sections from the INI file
            fileContent := FileRead(SETTINGS_APP_CONFIG_PATH)
            sections := []

            ; Parse the file to extract section names
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    sections.Push(match[1])
                }
            }

            ; Read all settings for each section
            for section in sections {
                settings[section] := {}

                ; Read all keys in the section
                sectionContent := IniRead(SETTINGS_APP_CONFIG_PATH, section)

                ; Parse the section content to extract keys and values
                Loop Parse, sectionContent, "`n", "`r" {
                    if (RegExMatch(A_LoopField, "^(.*?)=(.*?)$", &match)) {
                        key := match[1]
                        value := match[2]
                        settings[section][key] := value
                    }
                }
            }

            return settings
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get app settings: " err.Message)
            return {}
        }
    }

    ; Get a specific application setting
    ; Parameters:
    ; - section: The section of the setting
    ; - key: The key of the setting
    ; - default: The default value if the setting doesn't exist
    ; Returns: The value of the setting
    GetAppSetting(section, key, default := "") {
        try {
            return IniRead(SETTINGS_APP_CONFIG_PATH, section, key, default)
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get app setting: " err.Message)
            return default
        }
    }

    ; Set a specific application setting
    ; Parameters:
    ; - section: The section of the setting
    ; - key: The key of the setting
    ; - value: The value to set
    ; Returns: True if successful, False otherwise
    SetAppSetting(section, key, value) {
        try {
            IniWrite(value, SETTINGS_APP_CONFIG_PATH, section, key)
            ErrorHandler.LogMessage("INFO", "Set app setting " section "." key " to " value)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to set app setting: " err.Message)
            return false
        }
    }

    ; Update network settings
    ; Parameters:
    ; - enableSharing: Whether to enable network sharing
    ; - sharedPath: The path to the shared database
    ; - refreshInterval: The interval to refresh the database (in seconds)
    ; Returns: True if successful, False otherwise
    UpdateNetworkSettings(enableSharing, sharedPath, refreshInterval) {
        try {
            ; Update settings in both config files
            this.SetSetting("Network", "EnableNetworkSharing", enableSharing ? "1" : "0")
            this.SetSetting("Network", "SharedDatabasePath", sharedPath)
            this.SetSetting("Network", "RefreshInterval", refreshInterval)

            this.SetAppSetting("Network", "EnableNetworkSharing", enableSharing ? "1" : "0")
            this.SetAppSetting("Network", "SharedDatabasePath", sharedPath)
            this.SetAppSetting("Network", "RefreshInterval", refreshInterval)

            ErrorHandler.LogMessage("INFO", "Updated network settings")
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to update network settings: " err.Message)
            return false
        }
    }

    ; Update database settings
    ; Parameters:
    ; - backupInterval: The interval to backup the database (in seconds)
    ; - backupLocation: The location to store backups
    ; - maxBackups: The maximum number of backups to keep
    ; - enableAutoBackup: Whether to enable automatic backups
    ; Returns: True if successful, False otherwise
    UpdateDatabaseSettings(backupInterval, backupLocation, maxBackups, enableAutoBackup) {
        try {
            this.SetSetting("Database", "BackupInterval", backupInterval)
            this.SetSetting("Database", "BackupLocation", backupLocation)
            this.SetSetting("Database", "MaxBackups", maxBackups)
            this.SetSetting("Database", "EnableAutoBackup", enableAutoBackup ? "1" : "0")

            ErrorHandler.LogMessage("INFO", "Updated database settings")
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to update database settings: " err.Message)
            return false
        }
    }

    ; Update verification settings
    ; Parameters:
    ; - settings: Object with verification settings
    ; Returns: True if successful, False otherwise
    UpdateVerificationSettings(settings) {
        try {
            for key, value in settings {
                this.SetSetting("Verification", key, value)
            }

            ErrorHandler.LogMessage("INFO", "Updated verification settings")
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to update verification settings: " err.Message)
            return false
        }
    }

    ; Update company and exam information
    ; Parameters:
    ; - companySettings: Object with company settings
    ; - examSettings: Object with exam settings
    ; - centreSettings: Object with centre settings
    ; Returns: True if successful, False otherwise
    UpdateCompanyExamInfo(companySettings, examSettings, centreSettings) {
        try {
            ; Update company settings
            for key, value in companySettings {
                this.SetSetting("Company", key, value)
            }

            ; Update exam settings
            for key, value in examSettings {
                this.SetSetting("Exam", key, value)
            }

            ; Update centre settings
            for key, value in centreSettings {
                this.SetSetting("Centre", key, value)
            }

            ErrorHandler.LogMessage("INFO", "Updated company and exam information")
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to update company and exam information: " err.Message)
            return false
        }
    }
}

; Helper function to check if an array contains a value
HasValue(array, value) {
    for item in array {
        if (item = value)
            return true
    }
    return false
}
