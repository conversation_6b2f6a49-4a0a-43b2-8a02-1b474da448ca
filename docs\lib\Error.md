# Error Object

``` NoIndent
class Error extends Object
```

Error objects are [thrown](Throw.htm) by built-in code when a runtime
error occurs, and may also be thrown explicitly by the script.

## Table of Contents {#toc}

- [Standard Properties](#Standard_Properties)
- [Error()](#new)
- [Error Types](#error-types)
- [Related](#Related)

## Standard Properties {#Standard_Properties}

**Message:** An error message.

**What:** What threw the exception. This is usually the name of a
function, but is blank for exceptions thrown due to an error in an
expression (such as using a math operator on a non-numeric value).

**Extra:** A string value relating to the error, if available. If this
value can be converted to a non-empty string, the standard error dialog
displays a line with \"Specifically:\" followed by this string.

**File:** The full path of the script file which contains the line at
which the error occurred, or at which the Error object was constructed.

**Line:** The line number at which the error occurred, or at which the
Error object was constructed.

**Stack:** A string representing the call stack at the time the Error
object was constructed. Each line may be formatted as follows:

*`File`{.no-highlight}*` (`{.no-highlight}*`Line`{.no-highlight}*`) : [`{.no-highlight}*`What`{.no-highlight}*`] `{.no-highlight}*`SourceCode`{.no-highlight}*`` `r`n ``{.no-highlight}
:   Represents a call to the function *What*. *File* and *Line* indicate
    the current script line at this stack depth. *SourceCode* is an
    approximation of the source code at that line, as it would be shown
    in [ListLines](ListLines.htm).

`> `{.no-highlight}*`What`{.no-highlight}*`` `r`n ``{.no-highlight}
:   Represents the launching of a thread, typically the direct cause of
    the function call above it.

`... `{.no-highlight}*`N`{.no-highlight}*` more`{.no-highlight}
:   Indicates that the stack trace was truncated, and there are *N* more
    stack entries not shown. Currently the `Stack`{.no-highlight}
    property cannot exceed 2047 characters.

**Note:** The standard error dialog requires *Message*, *Extra*, *File*
and *Line* to be [own value properties](Object.htm).

## Error() {#new}

Creates an Error object.

``` Syntax
NewError := Error(Message , What, Extra)
```

***Error*** may be replaced with one of the subclasses listed under
[Error Types](#error-types), although some subclasses may take different
parameters.

The parameters directly correspond to properties described above, but
may differ for Error subclasses which override the \_\_New method.

*Message* and *Extra* are converted to strings. These are displayed by
an error dialog if the exception is thrown and not caught.

*What* indicates the source of the error. It can be an arbitrary string,
but should be a negative integer or the name of a running function.
Specifying -1 indicates the current function, -2 indicates the function
which called it, and so on. If the script is
[compiled](../Scripts.htm#ahk2exe) or the value does not identify a
valid stack frame, the value is merely converted to a string and
assigned to `NewError.What`. Otherwise, the identified stack frame is
used as follows to determine the other properties:

- `NewError.What` contains the name of the function.
- `NewError.Line` and `NewError.File` indicate the line which *called*
  the function.
- `NewError.Stack` contains a partial stack trace, with the indicated
  stack frame at the top.

Use of the *What* parameter can allow a complex function to use helper
functions to perform its work or parameter validation, while omitting
those internal details from any reported error information. For example:

    MyFunction(a, b) {
        CheckArg "a", a
        CheckArg "b", b
        ;...
        CheckArg(name, value) {
            if value < 0
                throw ValueError(name " is negative", "myfunction", value)
        }
    }

    try
        MyFunction(1, -1)  ; err.Line indicates this line.
    catch ValueError as err
        MsgBox Format("{1}: {2}.`n`nFile:`t{3}`nLine:`t{4}`nWhat:`t{5}`nStack:`n{6}"
            , type(err), err.Message, err.File, err.Line, err.What, err.Stack)

    try
        SomeFunction()
    catch as e
        MsgBox(type(e) " in " e.What ", which was called at line " e.Line)

    SomeFunction() {
        throw Error("Fail", -1)
    }

## Error Types

The following subclasses of **Error** are predefined:

- [**MemoryError:** A memory allocation failed.]{#MemoryError}
- [**OSError:** An internal function call to a Win32 function failed.
  **Message** includes an error code and description generated by the
  operating system. OSErrors have an additional **Number** property
  which contains the error code. Calling `OSError(Code)` where *Code* is
  numeric sets *Number* and *Message* based on the given OS-defined
  error code. If *Code* is omitted, it defaults to
  [A_LastError](../Variables.htm#LastError). For example,
  `OSError(5).Message` returns \"(5) Access is denied.\"]{#OSError}
- [**TargetError:** A function failed because its target could not be
  found. **Message** indicates what kind of target, such as a window,
  control, menu or status bar.]{#TargetError}
- [**TimeoutError:** [SendMessage](SendMessage.htm) timed
  out.]{#TimeoutError}
- [**TypeError:** An unexpected type of value was used as input for a
  function, property assignment, or some other operation. Usually
  **Message** indicates the expected and actual type, and **Extra**
  contains a string representing the errant value.]{#TypeError}
- [**UnsetError:** An attempt was made to read the value of a variable,
  property or item, but there was no value.]{#UnsetError}
  - [**MemberError**]{#MemberError}
    - [**PropertyError**]{#PropertyError}
    - [**MethodError**]{#MethodError}
  - [**UnsetItemError**]{#UnsetItemError}
- [**ValueError:** An unexpected value was used as input for a function,
  property assignment, or some other operation. Usually **Message**
  indicates which expectation was broken, and **Extra** contains a
  string representing the errant value.]{#ValueError}
  - [**IndexError:** The index parameter of an object\'s [\_\_Item
    property](../Objects.htm#__Item) was invalid or out of
    range.]{#IndexError}
- [**ZeroDivisionError:** Division by zero was attempted in an
  expression or with the Mod function.]{#ZeroDivisionError}

Errors are also thrown using the base Error class.

## Related {#Related}

[Throw](Throw.htm), [Try](Try.htm), [Catch](Catch.htm),
[Finally](Finally.htm), [OnError](OnError.htm)
