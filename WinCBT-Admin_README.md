# WinCBT-Admin

WinCBT-Admin is a centralized database management application for WinCBT-Biometric installations. It provides a comprehensive interface for managing candidate data, operator accounts, hardware configurations, and system-wide settings across multiple biometric verification stations.

## Features

- **Candidate Database Management**: Import, export, edit, and search candidate records
- **Operator Account Management**: Create and manage operator accounts with role-based permissions
- **Hardware Configuration**: Manage computer hardware inventory and seat mapping
- **Room Configuration**: Configure examination rooms and seating layouts
- **Report Generation**: Generate and export various reports on verification status and seat assignments
- **System-wide Settings**: Configure settings that apply across all biometric stations

## System Requirements

- Windows 10 or later
- AutoHotkey v2.0 or later
- Network share access for multi-station deployments

## Installation

1. Clone or download this repository
2. Ensure AutoHotkey v2 is installed
3. Run `WinCBT-Admin.exe` or `WinCBT-Admin.ahk`

## Directory Structure

```
WinCBT-Admin/
├── db/                  # Database files
│   ├── candidates.ini   # Candidate information
│   ├── config.ini       # Database configuration
│   ├── hardware.ini     # Computer hardware mapping
│   ├── rooms.ini        # Room configurations
│   ├── operators.ini    # Operator accounts
│   ├── img/             # Candidate images
│   └── fpt/             # Fingerprint templates
├── lib/                 # Library files
│   ├── admin_db_functions.ahk        # Database functions
│   ├── admin_import_export.ahk       # Import/export functionality
│   ├── admin_operator_management.ahk # Operator management
│   ├── admin_reports.ahk             # Report generation
│   ├── admin_settings.ahk            # Settings management
│   └── admin_candidate_management.ahk # Candidate management
├── logs/                # Log files
└── config.ini           # Application configuration
```

## Configuration

The application uses several INI files for configuration:

- `config.ini`: Main application settings
- `db/config.ini`: Database-specific settings
- `db/candidates.ini`: Candidate information
- `db/hardware.ini`: Computer hardware mapping
- `db/rooms.ini`: Room configurations
- `db/operators.ini`: Operator account information

### Network Configuration

For multi-station deployments, configure the network settings in the `config.ini` file:

```ini
[Network]
EnableNetworkSharing=1
SharedDatabasePath=\\server\share\db
RefreshInterval=300
```

## Usage

### Candidate Management

1. **Import Candidates**: Import candidate data from CSV files
2. **Export Candidates**: Export candidate data to various formats
3. **Search Candidates**: Search for candidates by roll number, name, or other criteria
4. **Edit Candidates**: Modify candidate information and biometric data

### Operator Management

1. **Create Operators**: Add new operator accounts with specific roles
2. **Manage Permissions**: Configure role-based permissions for operators
3. **Reset Passwords**: Reset operator passwords when needed

### Hardware and Room Configuration

1. **Add Hardware**: Register new computers and assign them to rooms
2. **Configure Rooms**: Set up examination rooms with capacity and special needs support
3. **Room Layout**: Design and visualize room layouts for optimal seating

### Report Generation

1. **Verification Reports**: Generate reports on candidate verification status
2. **Seat Assignment Reports**: View and export seat assignment information
3. **Special Candidates Reports**: Generate reports on candidates with special needs

## Troubleshooting

If you encounter issues:

1. Check the log files in the `logs` directory
2. Verify that all required directories exist
3. Ensure network share is accessible if using multi-station deployment
4. Check configuration files for correct settings

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- AutoHotkey community for the scripting language
- Contributors to the WinCBT-Biometric project
