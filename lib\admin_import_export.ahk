#Requires AutoHotkey v2.0

; Import/Export functionality for WinCBT-Admin
; This module handles importing and exporting data for the admin application

; Define paths
global ADMIN_DB_PATH := IniRead(A_ScriptDir "\config.ini", "Paths", "dbPath", "db")
; Convert relative to absolute path if needed
if (SubStr(ADMIN_DB_PATH, 1, 1) != "\" && SubStr(ADMIN_DB_PATH, 2, 1) != ":") {
    ADMIN_DB_PATH := A_ScriptDir "\" ADMIN_DB_PATH "\"
} else if (SubStr(ADMIN_DB_PATH, -1) != "\") {
    ADMIN_DB_PATH := ADMIN_DB_PATH "\"
}

; Define database file paths using the ADMIN_DB_PATH
global ADMIN_CANDIDATES_PATH := ADMIN_DB_PATH "candidates.ini"
global ADMIN_HARDWARE_PATH := ADMIN_DB_PATH "hardware.ini"
global ADMIN_ROOMS_PATH := ADMIN_DB_PATH "rooms.ini"
global ADMIN_SEAT_ASSIGNMENTS_PATH := ADMIN_DB_PATH "seat_assignments.ini"
global ADMIN_OPERATORS_PATH := ADMIN_DB_PATH "operators.ini"
global ADMIN_CANDIDATES_IMG_PATH := ADMIN_DB_PATH "img\candidates\"
global ADMIN_FINGERPRINT_PATH := ADMIN_DB_PATH "fpt\"

; Function to parse various date formats
DateParse(dateStr) {
    ; Try common date formats
    formats := [
        "yyyy-MM-dd",
        "MM/dd/yyyy",
        "dd/MM/yyyy",
        "yyyy/MM/dd",
        "MMM dd, yyyy",
        "dd MMM yyyy",
        "MMMM dd, yyyy",
        "dd MMMM yyyy",
        "ddMMyyyy"
    ]

    ; Try to parse with each format
    for format in formats {
        try {
            date := ParseDate(dateStr, format)
            if date
                return date
        } catch {
            continue
        }
    }

    return ""
}

; Function to parse a date string according to a specific format
; Parameters:
; - dateStr: The date string to parse
; - format: The format to use for parsing
; Returns: A date object if successful, or "" if parsing fails
ParseDate(dateStr, format) {
    try {
        ; Basic validation
        if (dateStr = "" || format = "")
            return ""

        ; Handle different formats
        if (format = "yyyy-MM-dd") {
            if (RegExMatch(dateStr, "^(\d{4})-(\d{1,2})-(\d{1,2})$", &match))
                return { year: match[1], month: match[2], day: match[3] }
        } else if (format = "MM/dd/yyyy") {
            if (RegExMatch(dateStr, "^(\d{1,2})/(\d{1,2})/(\d{4})$", &match))
                return { year: match[3], month: match[1], day: match[2] }
        } else if (format = "dd/MM/yyyy") {
            if (RegExMatch(dateStr, "^(\d{1,2})/(\d{1,2})/(\d{4})$", &match))
                return { year: match[3], month: match[2], day: match[1] }
        } else if (format = "yyyy/MM/dd") {
            if (RegExMatch(dateStr, "^(\d{4})/(\d{1,2})/(\d{1,2})$", &match))
                return { year: match[1], month: match[2], day: match[3] }
        } else if (format = "ddMMyyyy") {
            if (RegExMatch(dateStr, "^(\d{2})(\d{2})(\d{4})$", &match))
                return { year: match[3], month: match[2], day: match[1] }
        } else if (format = "MMM dd, yyyy" || format = "dd MMM yyyy" ||
                  format = "MMMM dd, yyyy" || format = "dd MMMM yyyy") {
            ; These formats require more complex parsing with month names
            ; For simplicity, we'll just check if the string contains a month name and digits
            monthNames := ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec",
                          "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]

            hasMonth := false
            for monthName in monthNames {
                if (InStr(dateStr, monthName)) {
                    hasMonth := true
                    break
                }
            }

            ; Check if the string has a month name and at least 4 digits (for year)
            if (hasMonth && RegExMatch(dateStr, "\d{4}"))
                return { valid: true }  ; Return a simple valid flag for these complex formats
        }

        return ""  ; Format not recognized or parsing failed
    } catch {
        return ""  ; Error during parsing
    }
}

; Function to join array elements into a string
StrJoin(array, delimiter := ",") {
    result := ""
    for i, value in array {
        if (i > 1)
            result .= delimiter
        result .= value
    }
    return result
}

; ; AdminImportExportManager class
; ; Handles importing and exporting data for the admin application
class AdminImportExportManager {
    ; Configuration
    importDir := A_ScriptDir "\import"
    exportDir := A_ScriptDir "\export"
    tempDir := A_ScriptDir "\temp"
    candidatesPath := ADMIN_CANDIDATES_PATH
    hardwarePath := ADMIN_HARDWARE_PATH
    roomsPath := ADMIN_ROOMS_PATH
    seatAssignmentsPath := ADMIN_SEAT_ASSIGNMENTS_PATH
    operatorsPath := ADMIN_OPERATORS_PATH

    ; Required fields for candidate import
    requiredFields := ["RollNumber", "Name"]

    ; Field mappings for CSV import
    defaultFieldMappings := Map(
        "RollNumber", "RollNumber",
        "Name", "Name",
        "FatherName", "FatherName",
        "DateOfBirth", "DOB",
        "Email", "Email",
        "Mobile", "Mobile",
        "Gender", "Gender",
        "CenterID", "CenterID",
        "ExamID", "ExamID",
        "StudentID", "StudentID",
        "Status", "Status",
        "Special", "Special"
    )

    ; Default fields for export
    defaultExportFields := [
        {field: "RollNumber", header: "Roll Number"},
        {field: "Name", header: "Name"},
        {field: "FatherName", header: "Father Name"},
        {field: "DateOfBirth", header: "Date of Birth"},
        {field: "Gender", header: "Gender"},
        {field: "Email", header: "Email"},
        {field: "Mobile", header: "Mobile"},
        {field: "Status", header: "Status"},
        {field: "Special", header: "Special Needs"},
        {field: "BiometricStatus", header: "Biometric Status"},
        {field: "PhotoStatus", header: "Photo Status"},
        {field: "FingerprintStatus", header: "Fingerprint Status"},
        {field: "RightFingerprintStatus", header: "Right Fingerprint Status"},
        {field: "SignatureStatus", header: "Signature Status"}
    ]

    ; Constructor
    __New() {
        ; Create required directories if they don't exist
        for dir in [this.importDir, this.exportDir, this.tempDir] {
            if (!DirExist(dir)) {
                try {
                    DirCreate(dir)
                    ErrorHandler.LogMessage("INFO", "Created directory: " dir)
                } catch as err {
                    ErrorHandler.LogMessage("ERROR", "Failed to create directory: " err.Message)
                }
            }
        }
    }

    ; Import candidates from CSV file
    ; Parameters:
    ; - csvFile: Path to CSV file
    ; - fieldMappings: Map of field mappings (optional, uses default if not provided)
    ; - options: Object with import options
    ;   - overwrite: Whether to overwrite existing candidates (default: false)
    ;   - validateOnly: Only validate without importing (default: false)
    ;   - delimiter: CSV delimiter (default: comma)
    ; Returns: Object with import results
    ImportCandidatesFromCSV(csvFile, fieldMappings := "", options := "") {
        ; Initialize results
        results := {
            totalRows: 0,
            imported: 0,
            skipped: 0,
            errors: [],
            validationErrors: Map()
        }

        ; Set default options
        if (!IsObject(options)) {
            options := {}
        }
        options.overwrite := options.HasOwnProp("overwrite") ? options.overwrite : false
        options.validateOnly := options.HasOwnProp("validateOnly") ? options.validateOnly : false
        options.delimiter := options.HasOwnProp("delimiter") ? options.delimiter : ","

        ; Use default field mappings if not provided
        if (!IsObject(fieldMappings) || !fieldMappings.Count) {
            fieldMappings := this.defaultFieldMappings.Clone()
        }

        try {
            ; Read CSV file
            fileContent := FileRead(csvFile)
            lines := StrSplit(fileContent, "`n", "`r")

            ; Get headers from first line
            if (lines.Length < 2) {
                throw Error("CSV file is empty or has no data rows")
            }

            headers := StrSplit(lines[1], options.delimiter)
            results.totalRows := lines.Length - 1  ; Exclude header row

            ; Process each data row
            Loop (lines.Length - 1) {
                i := A_Index + 1  ; Start from line 2 (skip header)

                ; Skip empty lines
                if (lines[i] = "")
                    continue

                ; Split line into fields
                fields := StrSplit(lines[i], options.delimiter)

                ; Create candidate data object
                candidateData := {}

                ; Map fields using field mappings
                Loop headers.Length {
                    j := A_Index

                    if (j > fields.Length)
                        break

                    header := headers[j]
                    value := fields[j]

                    ; Skip empty headers
                    if (header = "")
                        continue

                    ; Map header to field name using field mappings
                    if (fieldMappings.Has(header))
                        candidateData[fieldMappings[header]] := value
                    else
                        candidateData[header] := value
                }

                ; Validate candidate data
                validationErrors := this.ValidateCandidateData(candidateData)
                if (validationErrors.Length > 0) {
                    results.validationErrors[i - 1] := validationErrors
                    results.errors.Push("Row " (i - 1) ": " StrJoin(validationErrors, ", "))
                    continue
                }

                ; Check if candidate already exists
                rollNumber := candidateData.RollNumber
                candidateExists := FileExist(this.candidatesPath) && IniRead(this.candidatesPath, rollNumber, "Name", "") != ""

                ; Skip if candidate exists and overwrite is false
                if (candidateExists && !options.overwrite) {
                    results.skipped++
                    continue
                }

                ; Import candidate if not in validate-only mode
                if (!options.validateOnly) {
                    try {
                        this.ImportCandidate(candidateData)
                        results.imported++
                    } catch as err {
                        results.errors.Push("Failed to import candidate " rollNumber ": " err.Message)
                    }
                }
            }

            return results
        } catch as err {
            results.errors.Push("Error importing candidates: " err.Message)
            return results
        }
    }

    ; Validate candidate data
    ; Parameters:
    ; - candidateData: Object with candidate data
    ; Returns: Array of validation error messages
    ValidateCandidateData(candidateData) {
        errors := []

        ; Check required fields
        for field in this.requiredFields {
            if (!candidateData.HasOwnProp(field) || candidateData[field] = "") {
                errors.Push(field " is required")
            }
        }

        ; Validate roll number format (alphanumeric)
        if (candidateData.HasOwnProp("RollNumber") && candidateData.RollNumber != "") {
            if (!RegExMatch(candidateData.RollNumber, "^[A-Za-z0-9]+$")) {
                errors.Push("Roll Number must be alphanumeric")
            }
        }

        ; Validate date of birth format
        if (candidateData.HasOwnProp("DateOfBirth") && candidateData.DateOfBirth != "") {
            if (!DateParse(candidateData.DateOfBirth)) {
                errors.Push("Date of Birth is not in a valid format")
            }
        }

        ; Validate email format
        if (candidateData.HasOwnProp("Email") && candidateData.Email != "") {
            if (!RegExMatch(candidateData.Email, "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$")) {
                errors.Push("Email is not in a valid format")
            }
        }

        ; Validate mobile format
        if (candidateData.HasOwnProp("Mobile") && candidateData.Mobile != "") {
            if (!RegExMatch(candidateData.Mobile, "^[0-9+\-\s]{10,15}$")) {
                errors.Push("Mobile number is not in a valid format")
            }
        }

        return errors
    }

    ; Import a single candidate
    ; Parameters:
    ; - candidateData: Object with candidate data
    ImportCandidate(candidateData) {
        ; Ensure roll number exists
        if (!candidateData.HasOwnProp("RollNumber") || candidateData.RollNumber = "") {
            throw Error("Roll Number is required")
        }

        ; Write candidate data to INI file
        for field, value in candidateData.OwnProps() {
            IniWrite(value, this.candidatesPath, candidateData.RollNumber, field)
        }

        ; Set default values for missing fields
        defaultFields := Map(
            "Status", "Active",
            "PhotoStatus", "",
            "BiometricStatus", "",
            "FingerprintStatus", "",
            "RightFingerprintStatus", "",
            "SignatureStatus", ""
        )

        for field, defaultValue in defaultFields {
            if (!candidateData.HasOwnProp(field)) {
                IniWrite(defaultValue, this.candidatesPath, candidateData.RollNumber, field)
            }
        }
    }

    ; Export candidates to CSV file
    ; Parameters:
    ; - csvFile: Path to CSV file
    ; - options: Object with export options
    ;   - delimiter: CSV delimiter (default: comma)
    ;   - includeHeaders: Whether to include headers (default: true)
    ;   - fields: Array of field objects to export (default: defaultExportFields)
    ;   - filter: Function to filter candidates (default: export all)
    ; Returns: Object with export results
    ExportCandidatesToCSV(csvFile, options := "") {
        ; Initialize results
        results := {
            totalCandidates: 0,
            exported: 0,
            errors: []
        }

        ; Set default options
        if (!IsObject(options)) {
            options := {}
        }
        options.delimiter := options.HasOwnProp("delimiter") ? options.delimiter : ","
        options.includeHeaders := options.HasOwnProp("includeHeaders") ? options.includeHeaders : true
        options.fields := options.HasOwnProp("fields") ? options.fields : this.defaultExportFields

        try {
            ; Get all candidates
            candidates := this.GetAllCandidates(options.HasOwnProp("filter") ? options.filter : "")

            results.totalCandidates := candidates.Length

            ; Create CSV content
            csvContent := ""

            ; Add headers if requested
            if (options.includeHeaders) {
                headers := []
                for field in options.fields {
                    headers.Push(field.header)
                }
                csvContent := StrJoin(headers, options.delimiter) "`n"
            }

            ; Add candidate data
            for candidate in candidates {
                row := []
                for field in options.fields {
                    row.Push(candidate.HasOwnProp(field.field) ? candidate[field.field] : "")
                }
                csvContent .= StrJoin(row, options.delimiter) "`n"
                results.exported++
            }

            ; Write to file
            FileOpen(csvFile, "w").Write(csvContent)

            return results
        } catch as err {
            results.errors.Push("Error exporting candidates: " err.Message)
            return results
        }
    }

    ; Get all candidates
    ; Parameters:
    ; - filter: Function to filter candidates (optional)
    ; Returns: Array of candidate objects
    GetAllCandidates(filter := "") {
        candidates := []

        try {
            ; Read all sections from the INI file
            fileContent := FileRead(this.candidatesPath)
            sections := []

            ; Parse the file to extract section names
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    sections.Push(match[1])
                }
            }

            ; Read data for each candidate
            for rollNumber in sections {
                candidateData := {}
                candidateData.RollNumber := rollNumber
                candidateData.Name := IniRead(this.candidatesPath, rollNumber, "Name", "")
                candidateData.FatherName := IniRead(this.candidatesPath, rollNumber, "FatherName", "")
                candidateData.DateOfBirth := IniRead(this.candidatesPath, rollNumber, "DateOfBirth", "")
                candidateData.Gender := IniRead(this.candidatesPath, rollNumber, "Gender", "")
                candidateData.Email := IniRead(this.candidatesPath, rollNumber, "Email", "")
                candidateData.Mobile := IniRead(this.candidatesPath, rollNumber, "Mobile", "")
                candidateData.Status := IniRead(this.candidatesPath, rollNumber, "Status", "Active")
                candidateData.Special := IniRead(this.candidatesPath, rollNumber, "Special", "0")
                candidateData.PhotoStatus := IniRead(this.candidatesPath, rollNumber, "PhotoStatus", "")
                candidateData.BiometricStatus := IniRead(this.candidatesPath, rollNumber, "BiometricStatus", "")
                candidateData.FingerprintStatus := IniRead(this.candidatesPath, rollNumber, "FingerprintStatus", "")
                candidateData.RightFingerprintStatus := IniRead(this.candidatesPath, rollNumber, "RightFingerprintStatus", "")
                candidateData.SignatureStatus := IniRead(this.candidatesPath, rollNumber, "SignatureStatus", "")

                ; Apply filter if provided
                if (IsObject(filter)) {
                    if (filter(candidateData))
                        candidates.Push(candidateData)
                } else {
                    candidates.Push(candidateData)
                }
            }

            return candidates
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get candidates: " err.Message)
            return []
        }
    }
}
