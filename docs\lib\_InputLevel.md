# #InputLevel

Controls which artificial keyboard and mouse events are ignored by
hotkeys and hotstrings.

``` Syntax
#InputLevel Level
```

## Parameters {#Parameters}

Level

:   Type: [Integer](../Concepts.htm#numbers)

    If omitted, it defaults to 0. Otherwise, specify an integer between
    0 and 100.

## General Remarks {#General_Remarks}

If this directive is unspecified in the script, it will behave as though
set to 0.

For an explanation of how SendLevel and #InputLevel are used, see
[SendLevel](SendLevel.htm).

This directive is positional: it affects all hotkeys and hotstrings
between it and the next #InputLevel directive. If not specified by an
#InputLevel directive, hotkeys and hotstrings default to level 0.

A hotkey\'s input level can also be set using the Hotkey function. For
example: `Hotkey "#z", my_hotkey_sub, "`**`I1`**`"`

The input level of a hotkey or non-auto-replace hotstring is also used
as the default [send level](SendLevel.htm) for any keystrokes or button
clicks generated by that hotkey or hotstring. Since a keyboard or mouse
[remapping](../misc/Remap.htm) is actually a pair of hotkeys, this
allows #InputLevel to be used to allow remappings to trigger other
hotkeys.

AutoHotkey versions older than v1.1.06 behave as though `#InputLevel 0`
and `SendLevel 0` are in effect.

Like other directives, #InputLevel cannot be executed conditionally.

## Related {#Related}

[SendLevel](SendLevel.htm), [Hotkeys](../Hotkeys.htm),
[Hotstrings](../Hotstrings.htm)

## Examples {#Examples}

::: {#ExBasic .ex}
[](#ExBasic){.ex_number} Causes the first hotkey `*Numpad0::` to trigger
the second hotkey `~LButton::`. This would be not the case if the
#InputLevel directives are omitted or commented out.

    #InputLevel 1
    ; Use SendEvent so that the script's own hotkeys can be triggered.
    *Numpad0::SendEvent "{Blind}{Click Down}"
    *Numpad0 up::SendEvent "{Blind}{Click Up}"
    #InputLevel 0
    ; This hotkey can be triggered by both Numpad0 and LButton:
    ~LButton::MsgBox "Clicked"
:::
