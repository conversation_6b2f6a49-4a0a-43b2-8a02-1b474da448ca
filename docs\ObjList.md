# Built-in Classes

- [Any](lib/Any.htm)
  - [Object](lib/Object.htm)
    - [Array](lib/Array.htm)
    - [Buffer](lib/Buffer.htm)
      - [ClipboardAll](lib/ClipboardAll.htm)
    - [Class](lib/Class.htm)
    - [Error](lib/Error.htm)
      - MemoryError
      - OSError
      - TargetError
      - TimeoutError
      - TypeError
      - UnsetError
        - MemberError
          - PropertyError
          - MethodError
        - UnsetItemError
      - ValueError
        - IndexError
      - ZeroDivisionError
    - [File](lib/File.htm)
    - [Func](lib/Func.htm)
      - [BoundFunc](misc/Functor.htm#BoundFunc)
      - [Closure](Functions.htm#closures)
      - [Enumerator](lib/Enumerator.htm)
    - [Gui](lib/Gui.htm)
    - [Gui.Control](lib/GuiControl.htm)
      - [Gui.ActiveX](lib/GuiControls.htm#ActiveX)
      - [Gui.Button](lib/GuiControls.htm#Button)
      - [Gui.CheckBox](lib/GuiControls.htm#CheckBox)
      - [Gui.Custom](lib/GuiControls.htm#Custom)
      - [Gui.DateTime](lib/GuiControls.htm#DateTime)
      - [Gui.Edit](lib/GuiControls.htm#Edit)
      - [Gui.GroupBox](lib/GuiControls.htm#GroupBox)
      - [Gui.Hotkey](lib/GuiControls.htm#Hotkey)
      - [Gui.Link](lib/GuiControls.htm#Link)
      - Gui.List
        - [Gui.ComboBox](lib/GuiControls.htm#ComboBox)
        - [Gui.DDL](lib/GuiControls.htm#DropDownList)
        - [Gui.ListBox](lib/GuiControls.htm#ListBox)
        - [Gui.Tab](lib/GuiControls.htm#Tab)
      - [Gui.ListView](lib/ListView.htm)
      - [Gui.MonthCal](lib/GuiControls.htm#MonthCal)
      - [Gui.Pic](lib/GuiControls.htm#Pic)
      - [Gui.Progress](lib/GuiControls.htm#Progress)
      - [Gui.Radio](lib/GuiControls.htm#Radio)
      - [Gui.Slider](lib/GuiControls.htm#Slider)
      - [Gui.StatusBar](lib/GuiControls.htm#StatusBar)
      - [Gui.Text](lib/GuiControls.htm#Text)
      - [Gui.TreeView](lib/TreeView.htm)
      - [Gui.UpDown](lib/GuiControls.htm#UpDown)
    - [InputHook](lib/InputHook.htm#object)
    - [Map](lib/Map.htm)
    - [Menu](lib/Menu.htm)
      - [MenuBar](lib/Menu.htm)
    - [RegExMatchInfo](lib/RegExMatch.htm#MatchObject)
  - [Primitive](Objects.htm#primitive)
    - Number
      - Float
      - Integer
    - String
  - [VarRef](Concepts.htm#variable-references)
  - [ComValue](lib/ComValue.htm)
    - [ComObjArray](lib/ComObjArray.htm)
    - [ComObject](lib/ComObject.htm)
    - ComValueRef
