#Requires AutoHotkey v2.0+
#SingleInstance Force

; Test script for WinCBT-Admin biometric features
; This script tests the enhanced operator management system

; Include the main application files
#Include %A_ScriptDir%\lib\admin_error_handler.ahk
#Include %A_ScriptDir%\lib\admin_operator_management.ahk

; Initialize error handler
<PERSON>rror<PERSON><PERSON><PERSON>.Initialize(StatusCallback, true)

; Status callback function
StatusCallback(message) {
    OutputDebug(message)
}

; Test function
TestBiometricOperatorManagement() {
    try {
        ; Create operator manager
        operatorManager := CreateOperatorManager()
        
        ; Test 1: Create directories
        OutputDebug("Test 1: Creating biometric directories...")
        CreateBiometricDirectories()
        
        ; Verify directories exist
        operatorsDir := A_ScriptDir "\db\operators"
        photosDir := operatorsDir "\photos"
        fingerprintsDir := operatorsDir "\fingerprints"
        logsDir := operatorsDir "\logs"
        
        if (DirExist(operatorsDir) && DirExist(photosDir) && DirExist(fingerprintsDir) && DirExist(logsDir)) {
            OutputDebug("✓ Biometric directories created successfully")
        } else {
            OutputDebug("✗ Failed to create biometric directories")
            return false
        }
        
        ; Test 2: Add operator with biometric data
        OutputDebug("Test 2: Adding operator with biometric data...")
        
        testOperator := {}
        testOperator.Username := "test_biometric"
        testOperator.FullName := "Test Biometric User"
        testOperator.Email := "<EMAIL>"
        testOperator.Phone := "************"
        testOperator.Department := "IT"
        testOperator.Role := "Operator"
        testOperator.Status := "Active"
        testOperator.Password := "test123"
        testOperator.Notes := "Test operator for biometric features"
        testOperator.PhotoPath := photosDir "\test_biometric_photo.jpg"
        testOperator.FingerprintPath := fingerprintsDir "\test_biometric_fingerprint.dat"
        
        ; Create dummy biometric files for testing
        FileAppend("Dummy photo data", testOperator.PhotoPath)
        FileAppend("Dummy fingerprint data", testOperator.FingerprintPath)
        
        if (operatorManager.AddOperator(testOperator)) {
            OutputDebug("✓ Operator with biometric data added successfully")
        } else {
            OutputDebug("✗ Failed to add operator with biometric data")
            return false
        }
        
        ; Test 3: Retrieve operator and verify biometric data
        OutputDebug("Test 3: Retrieving operator and verifying biometric data...")
        
        retrievedOperator := operatorManager.GetOperator("test_biometric")
        
        if (retrievedOperator.PhotoPath = testOperator.PhotoPath && 
            retrievedOperator.FingerprintPath = testOperator.FingerprintPath &&
            retrievedOperator.AttendanceStatus = "Present") {
            OutputDebug("✓ Operator biometric data retrieved correctly")
        } else {
            OutputDebug("✗ Operator biometric data not retrieved correctly")
            OutputDebug("Expected PhotoPath: " testOperator.PhotoPath)
            OutputDebug("Actual PhotoPath: " retrievedOperator.PhotoPath)
            OutputDebug("Expected FingerprintPath: " testOperator.FingerprintPath)
            OutputDebug("Actual FingerprintPath: " retrievedOperator.FingerprintPath)
            return false
        }
        
        ; Test 4: Update attendance status
        OutputDebug("Test 4: Testing attendance status update...")
        
        IniWrite("Absent", A_ScriptDir "\db\operators.ini", "test_biometric", "AttendanceStatus")
        
        updatedOperator := operatorManager.GetOperator("test_biometric")
        if (updatedOperator.AttendanceStatus = "Absent") {
            OutputDebug("✓ Attendance status updated successfully")
        } else {
            OutputDebug("✗ Failed to update attendance status")
            return false
        }
        
        ; Test 5: Clean up test data
        OutputDebug("Test 5: Cleaning up test data...")
        
        operatorManager.DeleteOperator("test_biometric")
        
        ; Remove test files
        if (FileExist(testOperator.PhotoPath))
            FileDelete(testOperator.PhotoPath)
        if (FileExist(testOperator.FingerprintPath))
            FileDelete(testOperator.FingerprintPath)
        
        OutputDebug("✓ Test data cleaned up successfully")
        
        OutputDebug("=== ALL TESTS PASSED ===")
        MsgBox("All biometric operator management tests passed successfully!", "Test Results", "Icon!")
        return true
        
    } catch as err {
        OutputDebug("✗ Test failed with error: " err.Message)
        MsgBox("Test failed with error: " err.Message, "Test Error", "Icon!")
        return false
    }
}

; Function to create biometric directories (copied from main app)
CreateBiometricDirectories() {
    try {
        ; Create main operators directory
        operatorsDir := A_ScriptDir "\db\operators"
        if (!DirExist(operatorsDir)) {
            DirCreate(operatorsDir)
            ErrorHandler.LogMessage("INFO", "Created operators directory: " operatorsDir)
        }
        
        ; Create subdirectories
        photosDir := operatorsDir "\photos"
        if (!DirExist(photosDir)) {
            DirCreate(photosDir)
            ErrorHandler.LogMessage("INFO", "Created photos directory: " photosDir)
        }
        
        fingerprintsDir := operatorsDir "\fingerprints"
        if (!DirExist(fingerprintsDir)) {
            DirCreate(fingerprintsDir)
            ErrorHandler.LogMessage("INFO", "Created fingerprints directory: " fingerprintsDir)
        }
        
        logsDir := operatorsDir "\logs"
        if (!DirExist(logsDir)) {
            DirCreate(logsDir)
            ErrorHandler.LogMessage("INFO", "Created operator logs directory: " logsDir)
        }
        
    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Failed to create biometric directories: " err.Message)
    }
}

; Run the test
TestBiometricOperatorManagement()
