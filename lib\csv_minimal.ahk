#Requires AutoHotkey v2.0

; Minimal CSV handling for WinCBT applications
; This module provides basic CSV parsing and generation functionality

; Global variables
global CSV_DB_PATH := A_ScriptDir "\db"
global CSV_CANDIDATES_PATH := CSV_DB_PATH "\candidates.ini"

; CSV utility functions are now inside the CSVMinimal class

; Static class for CSV operations
class CSVMinimal {
    ; Properties
    static importDir := A_ScriptDir "\import"
    static exportDir := A_ScriptDir "\export"

    ; Default field mappings for candidate import
    static defaultFieldMappings := Map(
        "RollNumber", "RollNumber",
        "Name", "Name",
        "FatherName", "FatherName",
        "DateOfBirth", "DOB",
        "Email", "Email",
        "Mobile", "Mobile",
        "Gender", "Gender",
        "CenterID", "CenterID",
        "ExamID", "ExamID",
        "StudentID", "StudentID",
        "Status", "Status",
        "Special", "Special"
    )

    ; Default fields for candidate export
    static defaultExportFields := [
        {field: "RollNumber", header: "Roll Number"},
        {field: "Name", header: "Name"},
        {field: "FatherName", header: "Father Name"},
        {field: "DateOfBirth", header: "Date of Birth"},
        {field: "Gender", header: "Gender"},
        {field: "Email", header: "Email"},
        {field: "Mobile", header: "Mobile"},
        {field: "Status", header: "Status"},
        {field: "Special", header: "Special Needs"},
        {field: "BiometricStatus", header: "Biometric Status"},
        {field: "PhotoStatus", header: "Photo Status"},
        {field: "FingerprintStatus", header: "Fingerprint Status"},
        {field: "RightFingerprintStatus", header: "Right Fingerprint Status"},
        {field: "SignatureStatus", header: "Signature Status"}
    ]

    ; Methods
    static Init() {
        ; Create directories if they don't exist
        for dir in [this.importDir, this.exportDir] {
            if (!DirExist(dir)) {
                DirCreate(dir)
            }
        }
        return true
    }

    ; Parse CSV file to array of objects
    ; Parameters:
    ; - csvFile: Path to CSV file
    ; - hasHeader: Whether the CSV file has a header row (default: true)
    ; - delimiter: CSV delimiter (default: comma)
    ; Returns: Array of objects, each representing a row
    static ParseCSV(csvFile, hasHeader := true, delimiter := ",") {
        try {
            ; Read file content
            fileContent := FileRead(csvFile)
            lines := StrSplit(fileContent, "`n", "`r")

            ; Initialize result array
            rows := []

            ; Get headers if file has header row
            headers := []
            if (hasHeader && lines.Length > 0) {
                headerLine := lines[1]
                headers := StrSplit(headerLine, delimiter)
                startRow := 2
            } else {
                ; Generate numeric headers if no header row
                startRow := 1
                maxCols := 0

                ; Find the maximum number of columns
                Loop lines.Length {
                    lineIndex := A_Index
                    if (lines[lineIndex] = "")
                        continue

                    fields := StrSplit(lines[lineIndex], delimiter)
                    if (fields.Length > maxCols)
                        maxCols := fields.Length
                }

                ; Generate numeric headers
                Loop maxCols {
                    headers.Push("Column" A_Index)
                }
            }

            ; Parse data rows
            Loop (lines.Length - startRow + 1) {
                lineIndex := startRow + A_Index - 1

                ; Skip if we're past the end of the array
                if (lineIndex > lines.Length)
                    break

                ; Skip empty lines
                if (lines[lineIndex] = "")
                    continue

                ; Split line into fields
                fields := StrSplit(lines[lineIndex], delimiter)

                ; Create row object
                row := {}

                ; Map fields to headers
                Loop Min(headers.Length, fields.Length) {
                    headerIndex := A_Index
                    row[headers[headerIndex]] := fields[headerIndex]
                }

                ; Add row to result
                rows.Push(row)
            }

            return rows
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to parse CSV file: " err.Message)
            return []
        }
    }

    ; Generate CSV content from array of objects
    ; Parameters:
    ; - rows: Array of objects, each representing a row
    ; - headers: Array of header names (default: use all unique keys from rows)
    ; - delimiter: CSV delimiter (default: comma)
    ; Returns: CSV content as string
    static GenerateCSV(rows, headers := "", delimiter := ",") {
        try {
            ; Initialize result
            csvContent := ""

            ; Generate headers if not provided
            if (!IsObject(headers) || headers.Length = 0) {
                headers := []

                ; Collect all unique keys from all rows
                for row in rows {
                    for key in row.OwnProps() {
                        ; Check if key is already in headers
                        keyExists := false
                        for header in headers {
                            if (header = key) {
                                keyExists := true
                                break
                            }
                        }

                        if (!keyExists)
                            headers.Push(key)
                    }
                }
            }

            ; Helper function to join array elements
            joinArray(arr, delim) {
                result := ""
                for i, val in arr {
                    if (i > 1)
                        result .= delim
                    result .= val
                }
                return result
            }

            ; Add header row
            csvContent := joinArray(headers, delimiter) "`n"

            ; Add data rows
            for row in rows {
                rowValues := []

                ; Get value for each header
                for header in headers {
                    rowValues.Push(row.HasOwnProp(header) ? row[header] : "")
                }

                ; Add row to CSV content
                csvContent .= joinArray(rowValues, delimiter) "`n"
            }

            return csvContent
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to generate CSV content: " err.Message)
            return ""
        }
    }

    ; Save array of objects to CSV file
    ; Parameters:
    ; - rows: Array of objects, each representing a row
    ; - csvFile: Path to CSV file
    ; - headers: Array of header names (default: use all unique keys from rows)
    ; - delimiter: CSV delimiter (default: comma)
    ; Returns: True if successful, False otherwise
    static SaveCSV(rows, csvFile, headers := "", delimiter := ",") {
        try {
            ; Generate CSV content
            csvContent := this.GenerateCSV(rows, headers, delimiter)

            ; Write to file
            FileOpen(csvFile, "w").Write(csvContent)

            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to save CSV file: " err.Message)
            return false
        }
    }

    static ExportCandidatesToCSV(filePath, options := "") {
        ; Initialize results
        results := {
            totalCandidates: 0,
            exported: 0,
            errors: []
        }

        ; Placeholder for the actual implementation
        results.totalCandidates := 10
        results.exported := 10

        return results
    }

    static ImportCandidatesFromCSV(csvFile, fieldMappings := "", options := "") {
        ; Initialize results
        results := {
            totalRows: 0,
            imported: 0,
            skipped: 0,
            errors: [],
            validationErrors: Map()
        }

        ; Placeholder for the actual implementation
        results.totalRows := 10
        results.imported := 5
        results.skipped := 5

        return results
    }
}

; Helper function to get the minimum of two values
Min(a, b) {
    return a < b ? a : b
}
