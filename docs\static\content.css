@import url('highlighter/light.css');

@font-face {
font-family:'icons'; /* http://fontastic.me/ */
src:url(fonts/icons.eot?) format("embedded-opentype")
}

@font-face {
font-family:'icons';
src:url(data:font/opentype;base64,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) format("truetype"),url(fonts/icons.woff) format("woff"),url(fonts/icons.svg) format("svg")
}

.body {
height:100%;
width:100%;
position:fixed;
margin:0em
}

[data-content]:before {
content:attr(data-content);
display:inline-block
}

#main {
height:100%;
padding-top:3em;
box-sizing:border-box
}

#head {
box-shadow:0px 4px 16px 0px rgba(0,0,0,0.2);
z-index:999;
background:#3F627F;
position:absolute;
height:3em;
width:100%;
-webkit-touch-callout:none;
-webkit-user-select:none;
-khtml-user-select:none;
-moz-user-select:none;
-ms-user-select:none;
user-select:none;
font-family:arial,helvetica,Segoe UI Symbol;
color:#eee
}

#head .skip-nav {
clip: rect(1px, 1px, 1px, 1px);
height: 1px;
overflow: hidden;
white-space: nowrap;
width: 1px;
position: absolute;
top: 0;
left: 0;
background: #000;
color: #FFF;
z-index: 100;
padding: .4em;
}

#head .skip-nav:focus {
clip: auto;
width: auto;
height: auto;
overflow: auto;
}

#head div {
display:table-cell
}

#head .h-area {
display:table
}

#head ul {
list-style:none;
margin:0;
padding:0;
height:100%;
width:100%;
display:table;
table-layout:fixed
}

#head .h-tabs {
height:100%;
width:18em;
font-weight:700
}

#head .h-tabs ul {
position:absolute;
top:0;
width:inherit
}

#head .h-tools {
font-family:icons;
font-size:2em
}

#head .h-tools.sidebar > ul {
max-width:1.75em
}

#head .h-tools.online > ul {
max-width:7em
}

#head .h-tools.chm > ul {
max-width:8.75em
}

#head .h-tools.main > ul {
max-width:3.5em
}

#head li {
display:table-cell;
text-align:center;
line-height:1.5em;
vertical-align:middle;
height:100%
}

#head li > a {
display:block;
text-decoration:none;
color:inherit
}

:focus:not(:focus-visible) {
outline: 0;
}

#head button, #left button {
border-width: 2px;
}

#head li > button {
align-items: normal;
background-color: transparent;
border: 0;
box-sizing: content-box;
color: inherit;
cursor: pointer;
display: block;
font: inherit;
height: 100%;
padding: 0;
width: 100%;
}

#head .h-tools li {
max-width:1.75em
}

#head li:hover {
background:#fff;
color:#d06d3c;
cursor:pointer
}

@media (hover: none) {
  #head li:hover {
  background: inherit;
  color: inherit;
  }
}

#head .selected {
background:#fff;
color:#3F627F
}

#head .dropdown {
display:none;
position:absolute;
height:auto;
width:1.75em;
top:1.5em;
z-index:999;
box-shadow:0px 8px 16px -4px rgba(0,0,0,0.2)
}

#head .dropdown li {
display:block
}

#head div.dropdown {
background-color:#f5f5f5;
color:#000;
max-width:30em;
position:fixed;
white-space:normal;
padding:.72em
}

#head .dropdown li,li.version,li.language {
font-family:arial,helvetica,Segoe UI Symbol!important
}

#head .online,#head .chm {
display:none
}

#left {
z-index:999;
background:#f9f9f9;
color:#000;
float:left;
width:18em;
height:100%;
-webkit-touch-callout:none;
-webkit-user-select:none;
-khtml-user-select:none;
-moz-user-select:none;
-ms-user-select:none;
user-select:none;
position:relative
}

#left.phone {
float:none;
height:auto;
position:absolute;
top:3em;
bottom:0;
left:0;
z-index:999
}

#left .toc {
overflow:auto;
-webkit-overflow-scrolling:touch;
float:left;
width:inherit
}

#left .load {
position:absolute;
top:0;
right:0;
bottom:0;
left:0;
background-color: inherit;
}

#left .load div, #right .load div {
height:100%;
width:100%;
display:flex;
justify-content:center;
align-items:center
}

.lds-dual-ring {
display:inline-block;
width:64px;
height:64px;
opacity: 0;
animation: fadeIn .6s ease 1;
animation-delay: 2s;
animation-fill-mode: forwards;
}

.lds-dual-ring:after {
content:" ";
display:block;
width:46px;
height:46px;
margin:1px;
border-radius:50%;
border:5px solid #fff;
border-color:#3F627F transparent #3F627F transparent;
animation:rotate 1.2s linear infinite
}

@keyframes rotate {
  0% {
    transform:rotate(0deg);
  }
  100% {
    transform:rotate(360deg);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

#left .toc ul, #left .quick .main ul {
color:#3F5770;
padding:0;
margin:0
}

#left .toc > ul, #left .quick .main > ul {
padding:1em 0
}

#left .toc li, #left .quick .main li {
list-style:none;
white-space:nowrap;
overflow:hidden;
margin:0;
}

#left .toc li > span, #left .quick .main li > span {
box-sizing:border-box;
padding:.1em 0;
padding-left:1.5em;
display:inline-block;
height:100%;
width:100%;
border-left:.2em solid transparent;
cursor: pointer;
}

#left .toc ul > li > ul {
border-left:1px solid silver;
margin-left:1.8em
}

#left .toc ul > li.highlighted > ul {
border-left:1px solid #d06d3c
}

#left .toc li.closed > span,#left .toc li.opened > span {
color:#000
}

#left .toc li.closed > span:before {
content:'+';
width:1em;
display:inline-block;
margin-left:-1em;
font-size:1.1em
}

#left .toc li.opened > span:before {
content:'\2212';
width:1em;
display:inline-block;
margin-left:-1em;
font-size:1.1em
}

#left .toc a, #left .quick .main a {
box-sizing:border-box;
color:inherit;
display:inline-block;
text-decoration: none; /* For IE only */
width: 100%;
}

#left .toc span:hover a:before, #left .quick .main span:hover a:before {
text-decoration: underline;
}

#left .toc button, #left .quick button {
align-items: normal;
background-color: transparent;
border: 0;
box-sizing: content-box;
color: inherit;
cursor: pointer;
font: inherit;
height: auto;
padding: 0;
width: 100%;
text-align: left;
}

#left .toc .selected {
font-weight:700;
border-left:.2em solid #d06d3c
}

#left .index,#left .search {
position:absolute;
top:0;
left:0;
right:0;
bottom:0
}

#left .label {
position:absolute;
top:0;
left:.72em;
right:.72em
}

#left .input {
position:absolute;
top:.72em;
left:.72em;
right:.72em;
height:2em
}

#left .input input {
border:1px solid #ccc;
width:100%;
height:100%;
box-sizing:border-box;
color:inherit;
background:#fff;
padding:.3em .25em;
font-family:helvetica,Arial,sans-serif;
font-size:1em
}

#left .input input.match {
background-color:#E6FFE6
}

#left .input input.mismatch {
background-color:#fcc
}

#left .select {
position:absolute;
top:3em;
left:.72em;
right:.72em;
height:2em
}

#left .select select {
border:1px solid #ccc;
width:100%;
height:100%;
box-sizing:border-box;
color:inherit;
background:#fff;
font-size:inherit
}

#left .select select>option {
font-size:inherit;
color:black
}

#left .select .empty {
color:grey !important
}

#left .list {
position:absolute;
left:.72em;
right:.72em;
background-color:#fff;
border:1px solid silver;
overflow:auto;
-webkit-overflow-scrolling:touch
}

#left .index .list {
top:5.28em;
bottom:.72em
}

#left .search .list {
top:3em;
bottom:3em
}

#left .search .checkbox {
position:absolute;
bottom:.72em;
left:.72em;
right:.72em;
height:2em
}

#left .search .checkbox input {
height:100%;
width:1em;
margin:0 0 0 .5em;
vertical-align:middle;
text-align: center
}

#left .search .checkbox label {
border:1px solid #ccc;
box-sizing:border-box;
padding-left:2em;
height:100%;
position:absolute;
left:0;
right:0;
bottom:0;
cursor: pointer;
line-height: 1.9em;
}

#left .search .checkbox .updown {
border:1px solid #ccc;
position:absolute;
bottom:0;
right:0;
top:0;
width: 1.15em;
}

#left .search .checkbox .up {
position:absolute;
left:0;
right:0;
top:0;
height: 50%;
}

.triangle-up {
width:0;
margin: auto;
position: absolute;
left: 0;
right: 0;
top: 25%;
border-style:solid;
border-width:0 .4em .4em .4em;
border-color:transparent transparent #505050 transparent
}

#left .search .checkbox .up:hover {
background-color:#c2c2c2;
cursor:pointer
}

#left .search .checkbox .down {
position:absolute;
bottom:0;
left:0;
right:0;
height: 50%;
}

#left .deprecated {
color: brown !important;
}

.triangle-down {
width:0;
margin: auto;
position: absolute;
left: 0;
right: 0;
bottom: 25%;
border-style:solid;
border-width:.4em .4em 0 .4em;
border-color:#505050 transparent transparent transparent;
}

#left .search .checkbox .down:hover {
background-color:#c2c2c2;
cursor:pointer
}

#left .list > a {
text-decoration:none;
color:inherit;
line-height:1em;
padding:.3em .25em;
white-space:nowrap;
text-overflow:ellipsis;
overflow:hidden;
display:block;
cursor:default
}

#left .list > a:hover {
color:inherit;
background-color:silver;
cursor:default
}

#left .list > a.selected {
background-color:grey;
color:#FFF
}

#left .tab {
visibility: hidden;
}

#left .tab.full {
height: calc(100% - 2em);
transition: height .1s;
}
#left .tab.full.no-quick { /* for IE8 and IE9 */
  height: 100%;
}

#left .tab.shrinked {
height: 70%;
transition: height .1s;
}

#left .quick {
width: 100%;
height: calc(30% - 2em);
float: left;
}

#left .quick .main {
overflow: auto;
-webkit-overflow-scrolling: touch;
height: 100%;
width: inherit;
}

#left .quick .main.no-scroll {
overflow: hidden;
}

#left .quick div {
float: left;
}

#left .quick .header {
height: 2em;
line-height: 2em;
cursor:pointer;
border-top: 1px solid silver;
border-bottom: 1px solid silver;
}

#left .quick .header .chevron {
font-family: icons;
font-size: 1.1em;
margin: 0 .2em 0 .3em;
}

#left .quick .header .chevron.down:before {
content: '\25BC';
}

#left .quick .header .chevron.right:before {
content: '\25B6';
}

.dragbar {
background-color:#eee;
width:3px;
cursor:col-resize;
z-index:1000;
position:absolute;
top:0;
bottom:0;
left:18em
}

.ghostbar {
width:3px;
background-color:#000;
opacity:0.5;
position:absolute;
cursor:col-resize;
z-index:1000;
height:100%;
top:0
}

#right {
height:100%;
overflow:auto;
-webkit-overflow-scrolling:touch;
outline:none;
}

div#right {
display: grid;
grid-template-columns: 1fr;
}

#right .load {
grid-row-start: 1;
grid-column-start: 1;
}

#frame {
border:0;
height:100%;
width:100%;
display:block;
position:relative;
grid-row-start: 1;
grid-column-start: 1;
}

#frame.hidden {
opacity: 0;
visibility: hidden;
}

#frame.visible {
opacity: 1.0;
visibility: visible;
transition: opacity .1s, visibility .1s;
}

#right .area {
padding:.72em
}

#right .footer {
padding:15px 0 15px 0;
margin-top:20px;
opacity:0.5;
border-top:1px solid silver
}

#right .back-to-top {
display:none;
line-height:20px;
width:40px;
font-family:icons;
font-size:200%;
text-align:center;
position:fixed;
bottom:10px;
right:10px;
z-index:999;
background-color:#3F627F;
color:white;
cursor:pointer;
padding-top:10px;
padding-bottom:10px;
opacity:0.5
}

div#right .back-to-top {
right:27px;
}

#right .back-to-top:hover {
opacity:1.0
}

@media (hover: none) {
  #right .back-to-top:hover {
  opacity: 0.5;
  }
}

#right .back-to-top:before {
content:'\25B2'
}

#right pre.parent {
position:relative
}

#right pre.origin {
margin:0;
padding:0;
background-color:inherit;
border:0;
line-height:inherit
}

#right pre.parent > div.buttons {
display:none;
position:absolute;
right:0;
top:0
}

#right pre.parent > div.buttons > a {
background-color:#777; 
color:#fff;
cursor:pointer;
display:inline-block;
font-size:1.3em;
line-height:1em;
padding:.165em;
font-family:icons
}

#right pre.parent > div.buttons > a:hover {
cursor:pointer;
background:rgba(0,0,0,.9);
filter:progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr='#000000',endColorstr='#000000'); /* IE8 */
text-decoration:none
}

#right .headLine:hover:after {
content:" \0000B6";
color:#888
}

#right .headLine > a.headLink, .ver > a {
color:inherit !important
}

#right .headLine > a.headLink:hover {
text-decoration:none;
color:inherit
}

#right .extLink:after {
content:"\00A0\00AC";
text-decoration:none;
text-decoration-color: transparent;
font-family:icons;
vertical-align:-2px;
}

#right .search_highlight {
background-color:#ff9632;
color:#000
}

#right .search_highlight.current {
background-color:#ff0000;
color:#fff
}

#right table.mobile {
border:solid 1px silver;
border-collapse:collapse
}

#right table.mobile tbody {
border-top:solid 1px silver
}

#right table.mobile td {
vertical-align:top;
padding:.3em .5em;
border:none;
width:100%
}

#right table.mobile p {
margin-top:0
}

#right table.mobile td:first-child {
background-color:#F6F6F6;
width:auto;
white-space:nowrap
}

#right .deprecated {
color: brown;
text-decoration: none;
border-bottom: 1px dashed brown;
}

pre.origin em, code em {
color:#008000
}

@media print {
  #head,#left,#main>.dragbar,#right>.load {display:none;}
  #main {padding-top: 0; }
}

pre.highlight>code {
  white-space: inherit;
  line-height: inherit;
  word-break: inherit;
  word-wrap: inherit;
  tab-size: inherit;
  padding: 0;
}
