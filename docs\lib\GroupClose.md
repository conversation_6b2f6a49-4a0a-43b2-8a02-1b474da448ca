# GroupClose

Closes the active window if it was just activated by
[GroupActivate](GroupActivate.htm) or
[GroupDeactivate](GroupDeactivate.htm). It then activates the next
window in the series. It can also close all windows in a group.

``` Syntax
GroupClose GroupName , Mode
```

## Parameters {#Parameters}

GroupName

:   Type: [String](../Concepts.htm#strings)

    The name of the group as originally defined by
    [GroupAdd](GroupAdd.htm).

Mode

:   Type: [String](../Concepts.htm#strings)

    If blank or omitted, the function closes the active window and
    activates the oldest window in the series. Otherwise, specify one of
    the following letters:

    **R:** The newest window (the one most recently active) is
    activated, but only if no members of the group are active when the
    function is given. \"R\" is useful in cases where you temporarily
    switch to working on an unrelated task. When you return to the group
    via [GroupActivate](GroupActivate.htm),
    [GroupDeactivate](GroupDeactivate.htm), or GroupClose, the window
    you were most recently working with is activated rather than the
    oldest window.

    **A:** All members of the group will be closed. This is the same
    effect as [`WinClose`](WinClose.htm)` "ahk_group GroupName"`.

## Remarks {#Remarks}

When the *Mode* parameter is not \"A\", the behavior of this function is
determined by whether the previous action on *GroupName* was
[GroupActivate](GroupActivate.htm) or
[GroupDeactivate](GroupDeactivate.htm). If it was
[GroupDeactivate](GroupDeactivate.htm), this function will close the
active window only if it is [not]{.underline} a member of the group
(otherwise it will do nothing). If it was
[GroupActivate](GroupActivate.htm) or nothing, this function will close
the active window only if it [is]{.underline} a member of the group
(otherwise it will do nothing). This behavior allows GroupClose to be
assigned to a hotkey as a companion to *GroupName*\'s
[GroupActivate](GroupActivate.htm) or
[GroupDeactivate](GroupDeactivate.htm) hotkey.

When the active window closes, the system typically activates the next
most recently active window. If the newly active window is a match for
the same window specification as the window that was just closed, it is
left active even though the default *Mode* would normally dictate that
the [oldest]{.underline} window should be activated next. If the newly
active window is a match for [any]{.underline} of the group\'s window
specifications, it is left active.

See [GroupAdd](GroupAdd.htm) for more details about window groups.

## Related {#Related}

[GroupAdd](GroupAdd.htm), [GroupActivate](GroupActivate.htm),
[GroupDeactivate](GroupDeactivate.htm)

## Examples {#Examples}

::: {#ExBasic .ex}
[](#ExBasic){.ex_number} Closes the active window activated by
[GroupActivate](GroupActivate.htm) or
[GroupDeactivate](GroupDeactivate.htm) and activates the newest window
(the one most recently active) in a window group.

    GroupClose "MyGroup", "R"
:::
