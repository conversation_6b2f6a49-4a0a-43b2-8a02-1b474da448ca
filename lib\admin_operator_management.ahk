#Requires AutoHotkey v2.0

; Operator Management functionality for WinCBT-Admin
; This module handles operator account management for the admin application

; Define paths
global OPERATOR_DB_PATH := IniRead(A_ScriptDir "\config.ini", "Paths", "dbPath", "db")
; Convert relative to absolute path if needed
if (SubStr(OPERATOR_DB_PATH, 1, 1) != "\" && SubStr(OPERATOR_DB_PATH, 2, 1) != ":") {
    OPERATOR_DB_PATH := A_ScriptDir "\" OPERATOR_DB_PATH "\"
} else if (SubStr(OPERATOR_DB_PATH, -1) != "\") {
    OPERATOR_DB_PATH := OPERATOR_DB_PATH "\"
}

; Define operators file path
global OPERATORS_PATH := OPERATOR_DB_PATH "operators.ini"

; ; OperatorManager class
; ; Handles operator account management for the admin application
class OperatorManager {
    ; Operator roles and their permissions
    static ROLES := Map(
        "Administrator", Map(
            "description", "Full access to all system functions",
            "permissions", ["manage_candidates", "manage_operators", "manage_hardware", "manage_rooms",
                         "import_export", "generate_reports", "system_settings", "view_logs"]
        ),
        "Supervisor", Map(
            "description", "Can manage candidates, hardware, and rooms, but not operators",
            "permissions", ["manage_candidates", "manage_hardware", "manage_rooms",
                         "import_export", "generate_reports", "view_logs"]
        ),
        "Operator", Map(
            "description", "Basic access to candidate management and reports",
            "permissions", ["manage_candidates", "generate_reports"]
        ),
        "Viewer", Map(
            "description", "Read-only access to view data and reports",
            "permissions", ["view_candidates", "view_reports"]
        )
    )

    ; Constructor
    __New() {
        ; Create operators.ini if it doesn't exist or is empty
        needsDefaultAdmin := false

        if (!FileExist(OPERATORS_PATH)) {
            needsDefaultAdmin := true
        } else {
            ; Check if file is empty or has no admin account
            try {
                fileContent := FileRead(OPERATORS_PATH)
                if (Trim(fileContent) = "" || !InStr(fileContent, "[admin]")) {
                    needsDefaultAdmin := true
                }
            } catch {
                needsDefaultAdmin := true
            }
        }

        if (needsDefaultAdmin) {
            try {
                ; Create default admin account
                FileAppend("[admin]`n", OPERATORS_PATH)
                IniWrite("Administrator", OPERATORS_PATH, "admin", "FullName")
                IniWrite("<EMAIL>", OPERATORS_PATH, "admin", "Email")
                IniWrite("", OPERATORS_PATH, "admin", "Phone")
                IniWrite("", OPERATORS_PATH, "admin", "Department")
                IniWrite("Administrator", OPERATORS_PATH, "admin", "Role")
                IniWrite(FormatTime(, "yyyyMMddHHmmss"), OPERATORS_PATH, "admin", "Created")
                IniWrite("", OPERATORS_PATH, "admin", "LastLogin")
                IniWrite("Active", OPERATORS_PATH, "admin", "Status")
                IniWrite("Default administrator account", OPERATORS_PATH, "admin", "Notes")

                ; Hash the default password (admin)
                passwordHash := this.HashPassword("admin")
                IniWrite(passwordHash, OPERATORS_PATH, "admin", "PasswordHash")

                ErrorHandler.LogMessage("INFO", "Created default admin account")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create operators file: " err.Message)
            }
        }
    }

    ; Get all operators
    ; Returns: Array of operator objects
    GetAllOperators() {
        operators := []

        try {
            ; Read all sections from the INI file
            fileContent := FileRead(OPERATORS_PATH)
            sections := []

            ; Parse the file to extract section names
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    sections.Push(match[1])
                }
            }

            ; Read data for each operator
            for username in sections {
                operatorData := {}
                operatorData.Username := username
                operatorData.FullName := IniRead(OPERATORS_PATH, username, "FullName", "")
                operatorData.Email := IniRead(OPERATORS_PATH, username, "Email", "")
                operatorData.Phone := IniRead(OPERATORS_PATH, username, "Phone", "")
                operatorData.Department := IniRead(OPERATORS_PATH, username, "Department", "")
                operatorData.Role := IniRead(OPERATORS_PATH, username, "Role", "Operator")
                operatorData.Created := IniRead(OPERATORS_PATH, username, "Created", "")
                operatorData.LastLogin := IniRead(OPERATORS_PATH, username, "LastLogin", "")
                operatorData.Status := IniRead(OPERATORS_PATH, username, "Status", "Active")
                operatorData.Notes := IniRead(OPERATORS_PATH, username, "Notes", "")
                operatorData.PhotoPath := IniRead(OPERATORS_PATH, username, "PhotoPath", "")
                operatorData.FingerprintPath := IniRead(OPERATORS_PATH, username, "FingerprintPath", "")
                operatorData.AttendanceStatus := IniRead(OPERATORS_PATH, username, "AttendanceStatus", "Present")

                operators.Push(operatorData)
            }

            return operators
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get operators: " err.Message)
            return []
        }
    }

    ; Get operator by username
    ; Parameters:
    ; - username: The username of the operator
    ; Returns: Operator object or empty object if not found
    GetOperator(username) {
        try {
            if (!FileExist(OPERATORS_PATH))
                return {}

            ; Check if operator exists
            if (IniRead(OPERATORS_PATH, username, "FullName", "") = "")
                return {}

            ; Read operator data
            operatorData := {}
            operatorData.Username := username
            operatorData.FullName := IniRead(OPERATORS_PATH, username, "FullName", "")
            operatorData.Email := IniRead(OPERATORS_PATH, username, "Email", "")
            operatorData.Phone := IniRead(OPERATORS_PATH, username, "Phone", "")
            operatorData.Department := IniRead(OPERATORS_PATH, username, "Department", "")
            operatorData.Role := IniRead(OPERATORS_PATH, username, "Role", "Operator")
            operatorData.Created := IniRead(OPERATORS_PATH, username, "Created", "")
            operatorData.LastLogin := IniRead(OPERATORS_PATH, username, "LastLogin", "")
            operatorData.Status := IniRead(OPERATORS_PATH, username, "Status", "Active")
            operatorData.Notes := IniRead(OPERATORS_PATH, username, "Notes", "")
            operatorData.PhotoPath := IniRead(OPERATORS_PATH, username, "PhotoPath", "")
            operatorData.FingerprintPath := IniRead(OPERATORS_PATH, username, "FingerprintPath", "")
            operatorData.AttendanceStatus := IniRead(OPERATORS_PATH, username, "AttendanceStatus", "Present")

            return operatorData
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to get operator: " err.Message)
            return {}
        }
    }

    ; Add a new operator
    ; Parameters:
    ; - operatorData: Object with operator data (Username, FullName, Email, Role, Password)
    ; Returns: True if successful, False otherwise
    AddOperator(operatorData) {
        try {
            ; Validate required fields
            if (!operatorData.HasOwnProp("Username") || operatorData.Username = "")
                throw Error("Username is required")

            if (!operatorData.HasOwnProp("FullName") || operatorData.FullName = "")
                throw Error("Full Name is required")

            if (!operatorData.HasOwnProp("Email") || operatorData.Email = "")
                throw Error("Email is required")

            if (!operatorData.HasOwnProp("Role") || operatorData.Role = "")
                throw Error("Role is required")

            if (!operatorData.HasOwnProp("Password") || operatorData.Password = "")
                throw Error("Password is required")

            ; Check if username already exists
            if (IniRead(OPERATORS_PATH, operatorData.Username, "FullName", "") != "")
                throw Error("Username already exists")

            ; Hash the password
            passwordHash := this.HashPassword(operatorData.Password)

            ; Write operator data to INI file
            IniWrite(operatorData.FullName, OPERATORS_PATH, operatorData.Username, "FullName")
            IniWrite(operatorData.Email, OPERATORS_PATH, operatorData.Username, "Email")
            IniWrite(operatorData.HasOwnProp("Phone") ? operatorData.Phone : "", OPERATORS_PATH, operatorData.Username, "Phone")
            IniWrite(operatorData.HasOwnProp("Department") ? operatorData.Department : "", OPERATORS_PATH, operatorData.Username, "Department")
            IniWrite(operatorData.Role, OPERATORS_PATH, operatorData.Username, "Role")
            IniWrite(FormatTime(, "yyyyMMddHHmmss"), OPERATORS_PATH, operatorData.Username, "Created")
            IniWrite("", OPERATORS_PATH, operatorData.Username, "LastLogin")
            IniWrite(operatorData.HasOwnProp("Status") ? operatorData.Status : "Active", OPERATORS_PATH, operatorData.Username, "Status")
            IniWrite(operatorData.HasOwnProp("Notes") ? operatorData.Notes : "", OPERATORS_PATH, operatorData.Username, "Notes")
            IniWrite(passwordHash, OPERATORS_PATH, operatorData.Username, "PasswordHash")

            ; Write biometric data paths if provided
            IniWrite(operatorData.HasOwnProp("PhotoPath") ? operatorData.PhotoPath : "", OPERATORS_PATH, operatorData.Username, "PhotoPath")
            IniWrite(operatorData.HasOwnProp("FingerprintPath") ? operatorData.FingerprintPath : "", OPERATORS_PATH, operatorData.Username, "FingerprintPath")
            IniWrite("Present", OPERATORS_PATH, operatorData.Username, "AttendanceStatus")  ; Default attendance status

            ErrorHandler.LogMessage("INFO", "Added new operator: " operatorData.Username)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to add operator: " err.Message)
            return false
        }
    }

    ; Update an existing operator
    ; Parameters:
    ; - username: The username of the operator to update
    ; - operatorData: Object with operator data to update
    ; Returns: True if successful, False otherwise
    UpdateOperator(username, operatorData) {
        try {
            ; Check if operator exists
            if (IniRead(OPERATORS_PATH, username, "FullName", "") = "")
                throw Error("Operator not found")

            ; Update operator data
            for field, value in operatorData.OwnProps() {
                ; Skip username field
                if (field = "Username")
                    continue

                ; Handle password separately
                if (field = "Password") {
                    if (value != "") {
                        passwordHash := this.HashPassword(value)
                        IniWrite(passwordHash, OPERATORS_PATH, username, "PasswordHash")
                    }
                    continue
                }

                ; Write other fields
                IniWrite(value, OPERATORS_PATH, username, field)
            }

            ErrorHandler.LogMessage("INFO", "Updated operator: " username)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to update operator: " err.Message)
            return false
        }
    }

    ; Delete an operator
    ; Parameters:
    ; - username: The username of the operator to delete
    ; Returns: True if successful, False otherwise
    DeleteOperator(username) {
        try {
            ; Check if operator exists
            if (IniRead(OPERATORS_PATH, username, "FullName", "") = "")
                throw Error("Operator not found")

            ; Don't allow deleting the admin account
            if (username = "admin")
                throw Error("Cannot delete the admin account")

            ; Delete the operator section
            IniDelete(OPERATORS_PATH, username)

            ErrorHandler.LogMessage("INFO", "Deleted operator: " username)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to delete operator: " err.Message)
            return false
        }
    }

    ; Authenticate an operator
    ; Parameters:
    ; - username: The username of the operator
    ; - password: The password to check
    ; Returns: Operator object if authenticated, empty object otherwise
    AuthenticateOperator(username, password) {
        try {
            ; Get operator data
            operatorData := this.GetOperator(username)
            if (!operatorData.HasOwnProp("Username"))
                return {}

            ; Check if operator is active
            if (operatorData.Status != "Active")
                return {}

            ; Get password hash
            passwordHash := IniRead(OPERATORS_PATH, username, "PasswordHash", "")
            if (passwordHash = "")
                return {}

            ; Check password
            if (!this.VerifyPassword(password, passwordHash))
                return {}

            ; Update last login time
            IniWrite(FormatTime(, "yyyyMMddHHmmss"), OPERATORS_PATH, username, "LastLogin")

            return operatorData
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Authentication error: " err.Message)
            return {}
        }
    }

    ; Check if an operator has a specific permission
    ; Parameters:
    ; - username: The username of the operator
    ; - permission: The permission to check
    ; Returns: True if the operator has the permission, False otherwise
    HasPermission(username, permission) {
        try {
            ; Get operator data
            operatorData := this.GetOperator(username)
            if (!operatorData.HasOwnProp("Username"))
                return false

            ; Check if operator is active
            if (operatorData.Status != "Active")
                return false

            ; Get role permissions
            role := operatorData.Role
            if (!OperatorManager.ROLES.Has(role))
                return false

            ; Check if role has the permission
            rolePermissions := OperatorManager.ROLES[role]["permissions"]
            for perm in rolePermissions {
                if (perm = permission)
                    return true
            }

            return false
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Permission check error: " err.Message)
            return false
        }
    }

    ; Hash a password
    ; Parameters:
    ; - password: The password to hash
    ; Returns: Hashed password
    HashPassword(password) {
        ; In a real implementation, use a secure hashing algorithm with salt
        ; For this example, we'll use a simple hash function

        ; Create a salt
        salt := this.GenerateRandomString(16)

        ; Combine password and salt
        combined := password salt

        ; Hash the combined string
        hash := this.SimpleHash(combined)

        ; Return salt and hash separated by a colon
        return salt ":" hash
    }

    ; Verify a password against a hash
    ; Parameters:
    ; - password: The password to verify
    ; - storedHash: The stored hash to check against
    ; Returns: True if the password matches, False otherwise
    VerifyPassword(password, storedHash) {
        ; Split the stored hash into salt and hash
        parts := StrSplit(storedHash, ":")
        if (parts.Length != 2)
            return false

        salt := parts[1]
        hash := parts[2]

        ; Combine password and salt
        combined := password salt

        ; Hash the combined string
        newHash := this.SimpleHash(combined)

        ; Compare the hashes
        return (newHash = hash)
    }

    ; Generate a random string
    ; Parameters:
    ; - length: The length of the string to generate
    ; Returns: Random string
    GenerateRandomString(length) {
        chars := "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        result := ""

        Loop length {
            randIndex := Random(1, StrLen(chars))
            result .= SubStr(chars, randIndex, 1)
        }

        return result
    }

    ; Simple hash function
    ; Parameters:
    ; - str: The string to hash
    ; Returns: Hashed string
    SimpleHash(str) {
        ; In a real implementation, use a secure hashing algorithm
        ; For this example, we'll use a simple hash function

        hash := 0

        Loop Parse, str {
            hash := (hash * 31 + Ord(A_LoopField)) & 0xFFFFFFFF
        }

        return Format("{:08x}", hash)
    }
}

; Factory function to create OperatorManager instances
CreateOperatorManager() {
    return OperatorManager()
}
