tocData = [
  ["Quick Reference","index.htm"],
  ["Usage and Syntax","",
  [
    ["Using the Program","Program.htm"],
    ["Concepts and Conventions","Concepts.htm"],
    ["Scripting Language","Language.htm"],
    ["Hotkeys","Hotkeys.htm"],
    ["Hotstrings","Hotstrings.htm"],
    ["Remapping Keys","misc/Remap.htm"],
    ["List of Keys","KeyList.htm"],
    ["Scripts (misc)","Scripts.htm"],
    ["Variables and Expressions","Variables.htm"],
    ["Functions","Functions.htm"],
    ["Labels","misc/Labels.htm"],
    ["Threads","misc/Threads.htm"],
    ["Editors with AHK Support","misc/Editors.htm"],
    ["Debugging Clients","AHKL_DBGPClients.htm"],
    ["Compiler Directives","misc/Ahk2ExeDirectives.htm"],
    ["Objects","Objects.htm"]
  ]],
  ["Frequently Asked Questions","FAQ.htm"],
  ["Tutorials","",
  [
    ["Install AutoHotkey","howto/Install.htm"],
    ["Run Example Code","howto/RunExamples.htm"],
    ["Run Programs","howto/RunPrograms.htm"],
    ["Write Hotkeys","howto/WriteHotkeys.htm"],
    ["Send Keystrokes","howto/SendKeys.htm"],
    ["Manage Windows","howto/ManageWindows.htm"],
    ["Tutorial by tidbit","Tutorial.htm"]
  ]],
  ["Recent Changes","ChangeLog.htm"],
  ["Changes from v1.1 to v2.0","v2-changes.htm"],
  ["Changes from v1.0 to v1.1","v1-changes.htm"],
  ["Script Showcase","scripts/index.htm"],
  ["Function Index","lib/index.htm"],
  ["Drive", "lib/Drive.htm",
  [
    ["DriveEject/Retract", "lib/DriveEject.htm"],
    ["DriveGetCapacity", "lib/DriveGetCapacity.htm"],
    ["DriveGetFileSystem", "lib/DriveGetFileSystem.htm"],
    ["DriveGetLabel", "lib/DriveGetLabel.htm"],
    ["DriveGetList", "lib/DriveGetList.htm"],
    ["DriveGetSerial", "lib/DriveGetSerial.htm"],
    ["DriveGetSpaceFree", "lib/DriveGetSpaceFree.htm"],
    ["DriveGetStatus", "lib/DriveGetStatus.htm"],
    ["DriveGetStatusCD", "lib/DriveGetStatusCD.htm"],
    ["DriveLock", "lib/DriveLock.htm"],
    ["DriveSetLabel", "lib/DriveSetLabel.htm"],
    ["DriveUnlock", "lib/DriveUnlock.htm"],
    ["DriveGetType", "lib/DriveGetType.htm"]
  ]],
  ["Environment","",
  [
    ["A_Clipboard","lib/A_Clipboard.htm"],
    ["ClipboardAll","lib/ClipboardAll.htm"],
    ["ClipWait","lib/ClipWait.htm"],
    ["DPI Scaling","misc/DPIScaling.htm"],
    ["EnvGet","lib/EnvGet.htm"],
    ["EnvSet","lib/EnvSet.htm"],
    ["OnClipboardChange","lib/OnClipboardChange.htm"],
    ["SysGet","lib/SysGet.htm"],
    ["SysGetIPAddresses","lib/SysGetIPAddresses.htm"]
  ]],
  ["External Libraries","",
  [
    ["Binary Compatibility","Compat.htm"],
    ["Buffer Object","lib/Buffer.htm"],
    ["CallbackCreate","lib/CallbackCreate.htm"],
    ["DllCall","lib/DllCall.htm"],
    ["NumGet","lib/NumGet.htm"],
    ["NumPut","lib/NumPut.htm"],
    ["StrGet","lib/StrGet.htm"],
    ["StrPtr","lib/StrPtr.htm"],
    ["StrPut","lib/StrPut.htm"],
    ["VarSetStrCapacity","lib/VarSetStrCapacity.htm"],
    ["COM","",
    [
      ["ComObjActive","lib/ComObjActive.htm"],
      ["ComObjArray","lib/ComObjArray.htm"],
      ["ComCall","lib/ComCall.htm"],
      ["ComObjConnect","lib/ComObjConnect.htm"],
      ["ComObjGet","lib/ComObjGet.htm"],
      ["ComObject","lib/ComObject.htm"],
      ["ComObjFlags","lib/ComObjFlags.htm"],
      ["ComObjFromPtr","lib/ComObjFromPtr.htm"],
      ["ComObjQuery","lib/ComObjQuery.htm"],
      ["ComObjType","lib/ComObjType.htm"],
      ["ComObjValue","lib/ComObjValue.htm"],
      ["ComValue","lib/ComValue.htm"],
      ["ObjAddRef / ObjRelease","lib/ObjAddRef.htm"]
    ]]
  ]],
  ["File and Directory","",
  [
    ["DirCopy","lib/DirCopy.htm"],
    ["DirCreate","lib/DirCreate.htm"],
    ["DirDelete","lib/DirDelete.htm"],
    ["DirExist","lib/DirExist.htm"],
    ["DirMove","lib/DirMove.htm"],
    ["DirSelect","lib/DirSelect.htm"],
    ["FileAppend","lib/FileAppend.htm"],
    ["FileCopy","lib/FileCopy.htm"],
    ["FileCreateShortcut","lib/FileCreateShortcut.htm"],
    ["FileDelete","lib/FileDelete.htm"],
    ["FileEncoding","lib/FileEncoding.htm"],
    ["FileExist","lib/FileExist.htm"],
    ["FileGetAttrib","lib/FileGetAttrib.htm"],
    ["FileGetShortcut","lib/FileGetShortcut.htm"],
    ["FileGetSize","lib/FileGetSize.htm"],
    ["FileGetTime","lib/FileGetTime.htm"],
    ["FileGetVersion","lib/FileGetVersion.htm"],
    ["FileInstall","lib/FileInstall.htm"],
    ["FileMove","lib/FileMove.htm"],
    ["FileOpen","lib/FileOpen.htm"],
    ["FileRead","lib/FileRead.htm"],
    ["FileRecycle","lib/FileRecycle.htm"],
    ["FileRecycleEmpty","lib/FileRecycleEmpty.htm"],
    ["FileSelect","lib/FileSelect.htm"],
    ["FileSetAttrib","lib/FileSetAttrib.htm"],
    ["FileSetTime","lib/FileSetTime.htm"],
    ["IniDelete","lib/IniDelete.htm"],
    ["IniRead","lib/IniRead.htm"],
    ["IniWrite","lib/IniWrite.htm"],
    ["Long Paths","misc/LongPaths.htm"],
    ["Loop Files (and folders)","lib/LoopFiles.htm"],
    ["Loop Read (file contents)","lib/LoopRead.htm"],
    ["SetWorkingDir","lib/SetWorkingDir.htm"],
    ["SplitPath","lib/SplitPath.htm"]
  ]],
  ["Flow of Control","",
  [
    ["#Include[Again]","lib/_Include.htm"],
    ["{ ... } (block)","lib/Block.htm"],
    ["Catch","lib/Catch.htm"],
    ["Critical","lib/Critical.htm"],
    ["Else","lib/Else.htm"],
    ["Exit","lib/Exit.htm"],
    ["ExitApp","lib/ExitApp.htm"],
    ["Finally","lib/Finally.htm"],
    ["Goto","lib/Goto.htm"],
    ["If","lib/If.htm"],
    ["Loop Statements","Language.htm#loop-statement",
    [
      ["Loop","lib/Loop.htm"],
      ["Loop Files (and folders)","lib/LoopFiles.htm"],
      ["Loop Parse (strings)","lib/LoopParse.htm"],
      ["Loop Read (file contents)","lib/LoopRead.htm"],
      ["Loop Reg (registry)","lib/LoopReg.htm"],
      ["While","lib/While.htm"],
      ["For","lib/For.htm"],
      ["Break","lib/Break.htm"],
      ["Continue","lib/Continue.htm"],
      ["Until","lib/Until.htm"]
    ]],
    ["OnError","lib/OnError.htm"],
    ["OnExit","lib/OnExit.htm"],
    ["Pause","lib/Pause.htm"],
    ["Reload","lib/Reload.htm"],
    ["Return","lib/Return.htm"],
    ["SetTimer","lib/SetTimer.htm"],
    ["Sleep","lib/Sleep.htm"],
    ["Suspend","lib/Suspend.htm"],
    ["Switch","lib/Switch.htm"],
    ["Thread","lib/Thread.htm"],
    ["Throw","lib/Throw.htm"],
    ["Try","lib/Try.htm"]
  ]],
  ["Graphical User Interfaces","",
  [
    ["DirSelect","lib/DirSelect.htm"],
    ["FileSelect","lib/FileSelect.htm"],
    ["Gui control types","lib/GuiControls.htm"],
    ["Gui object","lib/Gui.htm"],
    ["GuiControl object","lib/GuiControl.htm"],
    ["GuiCtrlFromHwnd","lib/GuiCtrlFromHwnd.htm"],
    ["GuiFromHwnd","lib/GuiFromHwnd.htm"],
    ["Gui ListView control","lib/ListView.htm"],
    ["Gui TreeView control","lib/TreeView.htm"],
    ["Image Handles","misc/ImageHandles.htm"],
    ["InputBox","lib/InputBox.htm"],
    ["LoadPicture","lib/LoadPicture.htm"],
    ["Menu/MenuBar object","lib/Menu.htm"],
    ["MenuFromHandle","lib/MenuFromHandle.htm"],
    ["MsgBox","lib/MsgBox.htm"],
    ["OnCommand method","lib/GuiOnCommand.htm"],
    ["OnEvent method","lib/GuiOnEvent.htm"],
    ["OnMessage","lib/OnMessage.htm"],
    ["OnNotify method","lib/GuiOnNotify.htm"],
    ["Standard Windows Fonts","misc/FontsStandard.htm"],
    ["Styles for a window/control","misc/Styles.htm"],
    ["ToolTip","lib/ToolTip.htm"],
    ["TraySetIcon","lib/TraySetIcon.htm"],
    ["TrayTip","lib/TrayTip.htm"]
  ]],
  ["Maths","lib/Math.htm",
  [
    ["Abs","lib/Math.htm#Abs"],
    ["Ceil","lib/Math.htm#Ceil"],
    ["DateAdd","lib/DateAdd.htm"],
    ["DateDiff","lib/DateDiff.htm"],
    ["Exp","lib/Math.htm#Exp"],
    ["Float","lib/Float.htm"],
    ["Floor","lib/Math.htm#Floor"],
    ["Integer","lib/Integer.htm"],
    ["Log","lib/Math.htm#Log"],
    ["Ln","lib/Math.htm#Ln"],
    ["Max","lib/Math.htm#Max"],
    ["Min","lib/Math.htm#Min"],
    ["Mod","lib/Math.htm#Mod"],
    ["Number","lib/Number.htm"],
    ["Random","lib/Random.htm"],
    ["Round","lib/Math.htm#Round"],
    ["Sqrt","lib/Math.htm#Sqrt"],
    ["Sin/Cos/Tan","lib/Math.htm#Sin"],
    ["ASin/ACos/ATan","lib/Math.htm#ASin"]
  ]],
  ["Monitor","lib/Monitor.htm",
  [
    ["MonitorGet","lib/MonitorGet.htm"],
    ["MonitorGetCount","lib/MonitorGetCount.htm"],
    ["MonitorGetName","lib/MonitorGetName.htm"],
    ["MonitorGetPrimary","lib/MonitorGetPrimary.htm"],
    ["MonitorGetWorkArea","lib/MonitorGetWorkArea.htm"]
  ]],
  ["Mouse and Keyboard","",
  [
    ["Hotkeys and Hotstrings","Hotkeys.htm",
    [
      ["#HotIf","lib/_HotIf.htm"],
      ["#HotIfTimeout","lib/_HotIfTimeout.htm"],
      ["#Hotstring","lib/_Hotstring.htm"],
      ["#InputLevel","lib/_InputLevel.htm"],
      ["#MaxThreads","lib/_MaxThreads.htm"],
      ["#MaxThreadsBuffer","lib/_MaxThreadsBuffer.htm"],
      ["#MaxThreadsPerHotkey","lib/_MaxThreadsPerHotkey.htm"],
      ["#SuspendExempt","lib/_SuspendExempt.htm"],
      ["#UseHook","lib/_UseHook.htm"],
      ["A_HotkeyModifierTimeout","lib/A_HotkeyModifierTimeout.htm"],
      ["A_MaxHotkeysPerInterval","lib/A_MaxHotkeysPerInterval.htm"],
      ["A_MenuMaskKey","lib/A_MenuMaskKey.htm"],
      ["HotIf / HotIfWin...","lib/HotIf.htm"],
      ["Hotkey","lib/Hotkey.htm"],
      ["Hotstring","lib/Hotstring.htm"],
      ["Hotstrings & auto-replace","Hotstrings.htm"],
      ["ListHotkeys","lib/ListHotkeys.htm"],
      ["Suspend","lib/Suspend.htm"]
    ]],
    ["BlockInput","lib/BlockInput.htm"],
    ["CaretGetPos","lib/CaretGetPos.htm"],
    ["Click","lib/Click.htm"],
    ["ControlClick","lib/ControlClick.htm"],
    ["ControlSend[Text]","lib/ControlSend.htm"],
    ["CoordMode","lib/CoordMode.htm"],
    ["GetKeyName","lib/GetKeyName.htm"],
    ["GetKeySC","lib/GetKeySC.htm"],
    ["GetKeyState","lib/GetKeyState.htm"],
    ["GetKeyVK","lib/GetKeyVK.htm"],
    ["List of Keys","KeyList.htm"],
    ["KeyHistory","lib/KeyHistory.htm"],
    ["KeyWait","lib/KeyWait.htm"],
    ["InputHook","lib/InputHook.htm"],
    ["InstallKeybdHook","lib/InstallKeybdHook.htm"],
    ["InstallMouseHook","lib/InstallMouseHook.htm"],
    ["MouseClick","lib/MouseClick.htm"],
    ["MouseClickDrag","lib/MouseClickDrag.htm"],
    ["MouseGetPos","lib/MouseGetPos.htm"],
    ["MouseMove","lib/MouseMove.htm"],
    ["Send[Text|Input|Play|Event]","lib/Send.htm"],
    ["SendLevel","lib/SendLevel.htm"],
    ["SendMode","lib/SendMode.htm"],
    ["SetCapsLockState","lib/SetNumScrollCapsLockState.htm"],
    ["SetDefaultMouseSpeed","lib/SetDefaultMouseSpeed.htm"],
    ["SetKeyDelay","lib/SetKeyDelay.htm"],
    ["SetMouseDelay","lib/SetMouseDelay.htm"],
    ["SetNumLockState","lib/SetNumScrollCapsLockState.htm"],
    ["SetScrollLockState","lib/SetNumScrollCapsLockState.htm"],
    ["SetStoreCapsLockMode","lib/SetStoreCapsLockMode.htm"]
  ]],
  ["Misc.","",
  [
    ["Download","lib/Download.htm"],
    ["Edit","lib/Edit.htm"],
    ["GetMethod","lib/GetMethod.htm"],
    ["HasBase","lib/HasBase.htm"],
    ["HasMethod","lib/HasMethod.htm"],
    ["HasProp","lib/HasProp.htm"],
    ["Is Functions","lib/Is.htm"],
    ["IsLabel","lib/IsLabel.htm"],
    ["IsObject","lib/IsObject.htm"],
    ["IsSet / IsSetRef","lib/IsSet.htm"],
    ["ListLines","lib/ListLines.htm"],
    ["ListVars","lib/ListVars.htm"],
    ["OutputDebug","lib/OutputDebug.htm"],
    ["Persistent","lib/Persistent.htm"]
  ]],
  ["Object Types","ObjList.htm",
  [
    ["Object","lib/Object.htm"],
    ["Array Object","lib/Array.htm"],
    ["Buffer Object","lib/Buffer.htm"],
    ["Class Object","lib/Class.htm"],
    ["Enumerator Object","lib/Enumerator.htm"],
    ["Error Object","lib/Error.htm"],
    ["File Object","lib/File.htm"],
    ["Func Object","lib/Func.htm"],
    ["Function Objects","misc/Functor.htm"],
    ["Gui Object","lib/Gui.htm"],
    ["GuiControl Object","lib/GuiControl.htm"],
    ["InputHook Object","lib/InputHook.htm#object"],
    ["Map Object","lib/Map.htm"],
    ["Menu/MenuBar Object","lib/Menu.htm"],
    ["RegExMatch Object","lib/RegExMatch.htm#MatchObject"],
    ["Any Prototype","lib/Any.htm"]
  ]],
  ["Process","lib/Process.htm",
  [
    ["ProcessClose","lib/ProcessClose.htm"],
    ["ProcessExist","lib/ProcessExist.htm"],
    ["ProcessGetName/Path","lib/ProcessGetName.htm"],
    ["ProcessGetParent","lib/ProcessGetParent.htm"],
    ["ProcessSetPriority","lib/ProcessSetPriority.htm"],
    ["ProcessWait","lib/ProcessWait.htm"],
    ["ProcessWaitClose","lib/ProcessWaitClose.htm"],
    ["Run[Wait]","lib/Run.htm"],
    ["RunAs","lib/RunAs.htm"],
    ["Shutdown","lib/Shutdown.htm"]
  ]],
  ["Registry","",
  [
    ["Loop Reg","lib/LoopReg.htm"],
    ["RegCreateKey","lib/RegCreateKey.htm"],
    ["RegDelete","lib/RegDelete.htm"],
    ["RegDeleteKey","lib/RegDeleteKey.htm"],
    ["RegRead","lib/RegRead.htm"],
    ["RegWrite","lib/RegWrite.htm"],
    ["SetRegView","lib/SetRegView.htm"]
  ]],
  ["Screen","",
  [
    ["ImageSearch","lib/ImageSearch.htm"],
    ["PixelGetColor","lib/PixelGetColor.htm"],
    ["PixelSearch","lib/PixelSearch.htm"]
  ]],
  ["Sound","",
  [
    ["Sound Functions","lib/Sound.htm"],
    ["SoundBeep","lib/SoundBeep.htm"],
    ["SoundGetInterface","lib/SoundGetInterface.htm"],
    ["SoundGetMute","lib/SoundGetMute.htm"],
    ["SoundGetName","lib/SoundGetName.htm"],
    ["SoundGetVolume","lib/SoundGetVolume.htm"],
    ["SoundPlay","lib/SoundPlay.htm"],
    ["SoundSetMute","lib/SoundSetMute.htm"],
    ["SoundSetVolume","lib/SoundSetVolume.htm"]
  ]],
  ["String","",
  [
    ["Chr","lib/Chr.htm"],
    ["Format","lib/Format.htm"],
    ["FormatTime","lib/FormatTime.htm"],
    ["InStr","lib/InStr.htm"],
    ["Loop Parse (strings)","lib/LoopParse.htm"],
    ["Ord","lib/Ord.htm"],
    ["RegEx Quick Reference","misc/RegEx-QuickRef.htm"],
    ["RegExMatch","lib/RegExMatch.htm"],
    ["RegExReplace","lib/RegExReplace.htm"],
    ["Sort","lib/Sort.htm"],
    ["StrCompare","lib/StrCompare.htm"],
    ["String","lib/String.htm"],
    ["StrLower/Upper/Title","lib/StrLower.htm"],
    ["StrLen","lib/StrLen.htm"],
    ["StrGet","lib/StrGet.htm"],
    ["StrPut","lib/StrPut.htm"],
    ["StrReplace","lib/StrReplace.htm"],
    ["StrSplit","lib/StrSplit.htm"],
    ["SubStr","lib/SubStr.htm"],
    ["Trim / LTrim / RTrim","lib/Trim.htm"],
    ["VarSetStrCapacity","lib/VarSetStrCapacity.htm"],
    ["VerCompare","lib/VerCompare.htm"]
  ]],
  ["Window","lib/Win.htm",
  [
    ["Controls","lib/Control.htm",
    [
      ["ControlAddItem","lib/ControlAddItem.htm"],
      ["ControlChooseIndex","lib/ControlChooseIndex.htm"],
      ["ControlChooseString","lib/ControlChooseString.htm"],
      ["ControlClick","lib/ControlClick.htm"],
      ["ControlDeleteItem","lib/ControlDeleteItem.htm"],
      ["ControlFindItem","lib/ControlFindItem.htm"],
      ["ControlFocus","lib/ControlFocus.htm"],
      ["ControlGetChecked","lib/ControlGetChecked.htm"],
      ["ControlGetChoice","lib/ControlGetChoice.htm"],
      ["ControlGetClassNN","lib/ControlGetClassNN.htm"],
      ["ControlGetEnabled","lib/ControlGetEnabled.htm"],
      ["ControlGetFocus","lib/ControlGetFocus.htm"],
      ["ControlGetHwnd","lib/ControlGetHwnd.htm"],
      ["ControlGetIndex","lib/ControlGetIndex.htm"],
      ["ControlGetItems","lib/ControlGetItems.htm"],
      ["ControlGetPos","lib/ControlGetPos.htm"],
      ["ControlGet[Ex]Style","lib/ControlGetStyle.htm"],
      ["ControlGetText","lib/ControlGetText.htm"],
      ["ControlGetVisible","lib/ControlGetVisible.htm"],
      ["ControlHide","lib/ControlHide.htm"],
      ["ControlHideDropDown","lib/ControlHideDropDown.htm"],
      ["ControlMove","lib/ControlMove.htm"],
      ["ControlSend[Text]","lib/ControlSend.htm"],
      ["ControlSetChecked","lib/ControlSetChecked.htm"],
      ["ControlSetEnabled","lib/ControlSetEnabled.htm"],
      ["ControlSet[Ex]Style","lib/ControlSetStyle.htm"],
      ["ControlSetText","lib/ControlSetText.htm"],
      ["ControlShow","lib/ControlShow.htm"],
      ["ControlShowDropDown","lib/ControlShowDropDown.htm"],
      ["EditGetCurrentCol","lib/EditGetCurrentCol.htm"],
      ["EditGetCurrentLine","lib/EditGetCurrentLine.htm"],
      ["EditGetLine","lib/EditGetLine.htm"],
      ["EditGetLineCount","lib/EditGetLineCount.htm"],
      ["EditGetSelectedText","lib/EditGetSelectedText.htm"],
      ["EditPaste","lib/EditPaste.htm"],
      ["ListViewGetContent","lib/ListViewGetContent.htm"],
      ["MenuSelect","lib/MenuSelect.htm"],
      ["PostMessage","lib/PostMessage.htm"],
      ["SendMessage","lib/SendMessage.htm"],
      ["SetControlDelay","lib/SetControlDelay.htm"]
    ]],
    ["Window Groups","",
    [
      ["GroupActivate","lib/GroupActivate.htm"],
      ["GroupAdd","lib/GroupAdd.htm"],
      ["GroupClose","lib/GroupClose.htm"],
      ["GroupDeactivate","lib/GroupDeactivate.htm"]
    ]],
    ["Window Titles","misc/WinTitle.htm"],
    ["#WinActivateForce","lib/_WinActivateForce.htm"],
    ["DetectHiddenText","lib/DetectHiddenText.htm"],
    ["DetectHiddenWindows","lib/DetectHiddenWindows.htm"],
    ["SetTitleMatchMode","lib/SetTitleMatchMode.htm"],
    ["SetWinDelay","lib/SetWinDelay.htm"],
    ["StatusBarGetText","lib/StatusBarGetText.htm"],
    ["StatusBarWait","lib/StatusBarWait.htm"],
    ["WinActivate","lib/WinActivate.htm"],
    ["WinActivateBottom","lib/WinActivateBottom.htm"],
    ["WinActive","lib/WinActive.htm"],
    ["WinClose","lib/WinClose.htm"],
    ["WinExist","lib/WinExist.htm"],
    ["WinGetClass","lib/WinGetClass.htm"],
    ["WinGetClientPos","lib/WinGetClientPos.htm"],
    ["WinGetControls","lib/WinGetControls.htm"],
    ["WinGetControlsHwnd","lib/WinGetControlsHwnd.htm"],
    ["WinGetCount","lib/WinGetCount.htm"],
    ["WinGetID","lib/WinGetID.htm"],
    ["WinGetIDLast","lib/WinGetIDLast.htm"],
    ["WinGetList","lib/WinGetList.htm"],
    ["WinGetMinMax","lib/WinGetMinMax.htm"],
    ["WinGetPID","lib/WinGetPID.htm"],
    ["WinGetPos","lib/WinGetPos.htm"],
    ["WinGetProcessName","lib/WinGetProcessName.htm"],
    ["WinGetProcessPath","lib/WinGetProcessPath.htm"],
    ["WinGet[Ex]Style","lib/WinGetStyle.htm"],
    ["WinGetText","lib/WinGetText.htm"],
    ["WinGetTitle","lib/WinGetTitle.htm"],
    ["WinGetTransColor","lib/WinGetTransColor.htm"],
    ["WinGetTransparent","lib/WinGetTransparent.htm"],
    ["WinHide","lib/WinHide.htm"],
    ["WinKill","lib/WinKill.htm"],
    ["WinMaximize","lib/WinMaximize.htm"],
    ["WinMinimize","lib/WinMinimize.htm"],
    ["WinMinimizeAll[Undo]","lib/WinMinimizeAll.htm"],
    ["WinMove","lib/WinMove.htm"],
    ["WinMoveBottom","lib/WinMoveBottom.htm"],
    ["WinMoveTop","lib/WinMoveTop.htm"],
    ["WinRedraw","lib/WinRedraw.htm"],
    ["WinRestore","lib/WinRestore.htm"],
    ["WinSetAlwaysOnTop","lib/WinSetAlwaysOnTop.htm"],
    ["WinSetEnabled","lib/WinSetEnabled.htm"],
    ["WinSetRegion","lib/WinSetRegion.htm"],
    ["WinSet[Ex]Style","lib/WinSetStyle.htm"],
    ["WinSetTitle","lib/WinSetTitle.htm"],
    ["WinSetTransColor","lib/WinSetTransColor.htm"],
    ["WinSetTransparent","lib/WinSetTransparent.htm"],
    ["WinShow","lib/WinShow.htm"],
    ["WinWait","lib/WinWait.htm"],
    ["WinWait[Not]Active","lib/WinWaitActive.htm"],
    ["WinWaitClose","lib/WinWaitClose.htm"]
  ]],
  ["#Directives","",
  [
    ["#ClipboardTimeout","lib/_ClipboardTimeout.htm"],
    ["#DllLoad","lib/_DllLoad.htm"],
    ["#ErrorStdOut","lib/_ErrorStdOut.htm"],
    ["#HotIf","lib/_HotIf.htm"],
    ["#HotIfTimeout","lib/_HotIfTimeout.htm"],
    ["#Hotstring","lib/_Hotstring.htm"],
    ["#Include[Again]","lib/_Include.htm"],
    ["#InputLevel","lib/_InputLevel.htm"],
    ["#MaxThreads","lib/_MaxThreads.htm"],
    ["#MaxThreadsBuffer","lib/_MaxThreadsBuffer.htm"],
    ["#MaxThreadsPerHotkey","lib/_MaxThreadsPerHotkey.htm"],
    ["#NoTrayIcon","lib/_NoTrayIcon.htm"],
    ["#Requires","lib/_Requires.htm"],
    ["#SingleInstance","lib/_SingleInstance.htm"],
    ["#SuspendExempt","lib/_SuspendExempt.htm"],
    ["#UseHook","lib/_UseHook.htm"],
    ["#Warn","lib/_Warn.htm"],
    ["#WinActivateForce","lib/_WinActivateForce.htm"]
  ]]
];
