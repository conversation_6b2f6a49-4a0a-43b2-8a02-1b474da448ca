# ControlGetChoice

Returns the name of the currently selected entry in a ListBox or
ComboBox.

``` Syntax
Choice := ControlGetChoice(Control , WinTitle, WinText, ExcludeTitle, ExcludeText)
```

## Parameters {#Parameters}

Control

:   Type: [String](../Concepts.htm#strings),
    [Integer](../Concepts.htm#numbers) or
    [Object](../Concepts.htm#objects)

    The control\'s ClassNN, text or HWND, or an object with a
    `Hwnd`{.no-highlight} property. For details, see [The Control
    Parameter](Control.htm#Parameter).

WinTitle, WinText, ExcludeTitle, ExcludeText

:   Type: [String](../Concepts.htm#strings),
    [Integer](../Concepts.htm#numbers) or
    [Object](../Concepts.htm#objects)

    If each of these is blank or omitted, the [Last Found
    Window](../misc/WinTitle.htm#LastFoundWindow) will be used.
    Otherwise, specify for *WinTitle* a [window title or other
    criteria](../misc/WinTitle.htm) to identify the target window and/or
    for *WinText* a substring from a single text element of the target
    window (as revealed by the included Window Spy utility).

    *ExcludeTitle* and *ExcludeText* can be used to exclude one or more
    windows by their title or text. Their specification is similar to
    *WinTitle* and *WinText*, except that *ExcludeTitle* does not
    recognize any criteria other than the window title.

    Window titles and text are case-sensitive. By default, hidden
    windows are not detected and hidden text elements are detected,
    unless changed with [DetectHiddenWindows](DetectHiddenWindows.htm)
    and [DetectHiddenText](DetectHiddenText.htm); however, when using
    [pure HWNDs](../misc/WinTitle.htm#ahk_id), hidden windows are always
    detected regardless of DetectHiddenWindows. By default, a window
    title can contain *WinTitle* or *ExcludeTitle* anywhere inside it to
    be a match, unless changed with
    [SetTitleMatchMode](SetTitleMatchMode.htm).

## Return Value {#Return_Value}

Type: [String](../Concepts.htm#strings)

This function returns the name of the currently selected entry in a
ListBox or ComboBox.

## Error Handling {#Error_Handling}

A [TargetError](Error.htm#TargetError) is thrown if the window or
control could not be found, or if the control\'s class name does not
contain \"Combo\" or \"List\".

An [Error](Error.htm) is thrown on failure.

## Remarks {#Remarks}

To instead retrieve the position of the selected item, follow this
example (use only one of the first two lines):

    ChoicePos := SendMessage(0x0188, 0, 0, "ListBox1", WinTitle)  ; 0x0188 is LB_GETCURSEL (for a ListBox).
    ChoicePos := SendMessage(0x0147, 0, 0, "ComboBox1", WinTitle)  ; 0x0147 is CB_GETCURSEL (for a DropDownList or ComboBox).
    ChoicePos += 1  ; Convert from 0-based to 1-based, i.e. so that the first item is known as 1, not 0.
    ; ChoicePos is now 0 if there is no item selected.

## Related {#Related}

[ControlChooseIndex](ControlChooseIndex.htm),
[ControlChooseString](ControlChooseString.htm), [Value property
(GuiControl object)](GuiControl.htm#Value), [Choose method (GuiControl
object)](GuiControl.htm#Choose), [Control functions](Control.htm)
