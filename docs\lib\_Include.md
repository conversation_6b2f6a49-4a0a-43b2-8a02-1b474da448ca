# #Include / #IncludeAgain

Causes the script to behave as though the specified file\'s contents are
present at this exact position.

``` Syntax
#Include FileOrDirName
#Include <LibName>
#IncludeAgain FileOrDirName
```

## Parameters {#Parameters}

FileOrDirName

:   Type: [String](../Concepts.htm#strings)

    The path of a file or directory as explained below. This must
    [not]{.underline} contain double quotes (except for an optional pair
    of double quotes surrounding the parameter), wildcards
    (`*`{.no-highlight} and `?`{.no-highlight}) or [escape
    sequences](../misc/EscapeChar.htm) other than semicolon
    (`` `; ``{.no-highlight}).

    Built-in variables may be used by enclosing them in percent signs
    (for example, `#Include "%A_ScriptDir%"`). Percent signs which are
    not part of a valid variable reference are interpreted literally.
    All built-in variables containing strings or numbers are valid.

    Known limitation: When compiling a script, variables are evaluated
    by the compiler and may differ from what the script would return
    when it is finally executed. The following variables are supported:
    [A_AhkPath](../Variables.htm#AhkPath),
    [A_AppData](../Variables.htm#AppData),
    [A_AppDataCommon](../Variables.htm#AppDataCommon),
    [A_ComputerName](../Variables.htm#ComputerName),
    [A_ComSpec](../Variables.htm#ComSpec),
    [A_Desktop](../Variables.htm#Desktop),
    [A_DesktopCommon](../Variables.htm#DesktopCommon),
    [A_IsCompiled](../Variables.htm#IsCompiled),
    [A_LineFile](../Variables.htm#LineFile),
    [A_MyDocuments](../Variables.htm#MyDocuments),
    [A_ProgramFiles](../Variables.htm#ProgramFiles),
    [A_Programs](../Variables.htm#Programs),
    [A_ProgramsCommon](../Variables.htm#ProgramsCommon),
    [A_ScriptDir](../Variables.htm#ScriptDir),
    [A_ScriptFullPath](../Variables.htm#ScriptFullPath),
    [A_ScriptName](../Variables.htm#ScriptName),
    [A_Space](../Variables.htm#Space),
    [A_StartMenu](../Variables.htm#StartMenu),
    [A_StartMenuCommon](../Variables.htm#StartMenuCommon),
    [A_Startup](../Variables.htm#Startup),
    [A_StartupCommon](../Variables.htm#StartupCommon),
    [A_Tab](../Variables.htm#Tab), [A_Temp](../Variables.htm#Temp),
    [A_UserName](../Variables.htm#UserName),
    [A_WinDir](../Variables.htm#WinDir).

    **File:** The name of the file to be included. By default, relative
    paths are relative to the directory of the file which contains the
    #Include directive. This default can be overridden by using
    `#Include Dir` as described below. Note:
    [SetWorkingDir](SetWorkingDir.htm) has no effect on #Include because
    #Include is processed before the script begins executing.

    **Directory:** Specify a directory instead of a file to change the
    working directory used by all subsequent occurrences of #Include and
    [FileInstall](FileInstall.htm) in the current file. Note: Changing
    the working directory in this way does not affect the script\'s
    initial working directory when it starts running
    ([A_WorkingDir](../Variables.htm#WorkingDir)). To change that, use
    [SetWorkingDir](SetWorkingDir.htm) at the top of the script.

    **Note:** This parameter is not an expression, but can be enclosed
    in quote marks (either \'single\' or \"double\").

\<LibName\>

:   Type: [String](../Concepts.htm#strings)

    A library file or function name. For example, `#Include <lib>` and
    `#Include <lib_func>` would both include lib.ahk from one of the
    [Lib folders](../Scripts.htm#lib). Variable references are not
    allowed.

    A subdirectory can be specified, with the caveat that the first
    underscore may be interpreted as a delimiter as described under
    [Script Library Folders](../Scripts.htm#lib), e.g.
    `#Include <a_b\c>` will try a.ahk if a_b\\c.ahk is not found.

## Remarks {#Remarks}

A script behaves as though the included file\'s contents are physically
present at the exact position of the #Include directive (as though a
copy-and-paste were done from the included file). Consequently, it
generally cannot merge two isolated scripts together into one
functioning script.

#Include ensures that the specified file is included only once, even if
multiple inclusions are encountered for it. By contrast, #IncludeAgain
allows multiple inclusions of the same file, while being the same as
#Include in all other respects.

The file path may optionally be preceded by `*i`{.no-highlight} and a
single space, which causes the program to ignore any failure to read the
file. For example: `#Include "*i SpecialOptions.ahk"`. This option
should be used only when the file\'s contents are not essential to the
main script\'s operation.

Lines displayed in the [main window](../Program.htm#main-window) via
[ListLines](ListLines.htm) or the menu View-\>Lines are always numbered
according to their physical order within their own files. In other
words, including a new file will change the line numbering of the main
script file by only one line, namely that of the #Include line itself
(except for [compiled scripts](../Scripts.htm#ahk2exe), which merge
their included files into one big script at the time of compilation).

#Include is often used to load [functions](../Functions.htm) defined in
an external file.

Like other directives, #Include cannot be executed conditionally. In
other words, this example would not work as expected:

    if (x = 1)
        #Include "SomeFile.ahk"  ; This line takes effect regardless of the value of x.

## Related {#Related}

[Script Library Folders](../Scripts.htm#lib),
[Functions](../Functions.htm), [FileInstall](FileInstall.htm)

## Examples {#Examples}

::: {#ExFile .ex}
[](#ExFile){.ex_number} Includes the contents of the specified file into
the current script.

    #Include "C:\My Documents\Scripts\Utility Subroutines.ahk"
:::

::: {#ExDir .ex}
[](#ExDir){.ex_number} Changes the working directory for subsequent
#Includes and FileInstalls.

    #Include "%A_ScriptDir%"
:::

::: {#ExDir2 .ex}
[](#ExDir2){.ex_number} Same as above but for an explicitly named
directory.

    #Include "C:\My Scripts"
:::
